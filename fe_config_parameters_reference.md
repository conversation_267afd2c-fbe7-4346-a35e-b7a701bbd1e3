# StarRocks FE 配置参数完整参考

## 配置文件位置
- 配置文件路径: `${STARROCKS_HOME}/conf/fe.conf`
- 完整参数定义: `fe/fe-core/src/main/java/com/starrocks/common/Config.java`

## 配置参数分类

### 1. JVM 配置 (大写参数)
这些参数由 `bin/start_fe.sh` 脚本读取和导出：

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| `LOG_DIR` | `${STARROCKS_HOME}/log` | 日志输出目录 |
| `JAVA_OPTS` | 见样例 | JDK 8 的 JVM 参数 |
| `JAVA_OPTS_FOR_JDK_11` | 见样例 | JDK 11+ 的 JVM 参数 |

### 2. 基础网络配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `frontend_address` | `0.0.0.0` | 指定 FE 的 IP 地址 | 否 |
| `priority_networks` | `""` | IP 选择策略 (CIDR 格式) | 否 |
| `http_port` | `8030` | HTTP 服务端口 | 否 |
| `rpc_port` | `9020` | Thrift RPC 端口 | 否 |
| `query_port` | `9030` | MySQL 协议端口 | 否 |
| `edit_log_port` | `9010` | 编辑日志端口 | 否 |
| `mysql_service_nio_enabled` | `true` | 启用 MySQL 服务 NIO 模式 | 否 |

### 3. HTTP 服务配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `http_backlog_num` | `1024` | HTTP 连接队列大小 | 否 |
| `http_max_initial_line_length` | `4096` | HTTP 初始行最大长度 | 否 |
| `http_max_header_size` | `32768` | HTTP 头部最大大小 | 否 |
| `http_max_chunk_size` | `8192` | HTTP 块最大大小 | 否 |
| `http_slow_request_threshold_ms` | `5000` | 慢请求阈值 (毫秒) | 是 |

### 4. Thrift RPC 配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `thrift_client_timeout_ms` | `5000` | Thrift 客户端超时 | 否 |
| `thrift_backlog_num` | `1024` | Thrift 连接队列大小 | 否 |
| `thrift_rpc_timeout_ms` | `10000` | Thrift RPC 超时 | 是 |
| `thrift_rpc_retry_times` | `3` | Thrift RPC 重试次数 | 是 |
| `thrift_rpc_strict_mode` | `true` | Thrift RPC 严格模式 | 否 |
| `thrift_rpc_max_body_size` | `-1` | RPC 消息体最大大小 (-1 表示无限制) | 否 |

### 5. BRPC 配置 (重要：解决过载问题)

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `brpc_connection_pool_size` | `16` | BRPC 连接池大小 | 否 |
| `brpc_idle_wait_max_time` | `10000` | BRPC 空闲等待最大时间 (毫秒) | 否 |

### 6. 查询引擎配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `qe_max_connection` | `4096` | 最大连接数 | 否 |
| `max_connection_scheduler_threads_num` | `4096` | 连接调度线程池大小 | 否 |
| `qe_query_timeout_second` | `300` | 查询超时时间 (秒) | 是 |
| `max_conn_per_user` | `100` | 每用户最大连接数 | 是 |
| `qe_slow_log_ms` | `5000` | 慢查询阈值 (毫秒) | 是 |
| `enable_qe_slow_log` | `true` | 启用慢查询日志 | 是 |

### 7. 日志配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `sys_log_level` | `INFO` | 系统日志级别 | 否 |
| `sys_log_dir` | `${STARROCKS_HOME}/log` | 系统日志目录 | 否 |
| `log_roll_size_mb` | `1024` | 日志文件大小 (MB) | 否 |
| `sys_log_roll_num` | `10` | 系统日志文件数量 | 否 |
| `sys_log_roll_interval` | `DAY` | 日志滚动间隔 | 否 |
| `sys_log_delete_age` | `7d` | 日志删除时间 | 否 |
| `sys_log_to_console` | `false` | 输出日志到控制台 | 否 |
| `sys_log_format` | `plaintext` | 日志格式 (plaintext/json) | 否 |

### 8. 审计日志配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `audit_log_dir` | `${STARROCKS_HOME}/log` | 审计日志目录 | 否 |
| `audit_log_modules` | `["slow_query", "query"]` | 审计日志模块 | 否 |
| `audit_log_roll_num` | `90` | 审计日志文件数量 | 否 |
| `audit_log_roll_interval` | `DAY` | 审计日志滚动间隔 | 否 |
| `audit_log_delete_age` | `30d` | 审计日志删除时间 | 否 |
| `audit_log_json_format` | `false` | 审计日志 JSON 格式 | 是 |

### 9. 元数据配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `meta_dir` | `${STARROCKS_HOME}/meta` | 元数据存储目录 | 否 |
| `edit_log_type` | `BDB` | 编辑日志类型 | 否 |
| `edit_log_roll_num` | `50000` | 编辑日志滚动数量 | 是 |
| `edit_log_write_slow_log_threshold_ms` | `2000` | 编辑日志慢写入阈值 | 是 |
| `meta_delay_toleration_second` | `10` | 元数据延迟容忍度 | 否 |

### 10. 存储配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `tmp_dir` | `${STARROCKS_HOME}/temp_dir` | 临时目录 | 否 |
| `storage_usage_hard_limit_percent` | `95` | 存储使用硬限制百分比 | 是 |
| `storage_usage_hard_limit_reserve_bytes` | `107374182400` | 存储预留字节数 (100GB) | 是 |

### 11. 性能调优配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `statistic_collect_interval_sec` | `300` | 统计信息收集间隔 | 是 |
| `statistic_collect_concurrency` | `3` | 统计信息收集并发度 | 是 |
| `statistic_collect_query_timeout` | `3600` | 统计查询超时 | 是 |
| `enable_materialized_view` | `true` | 启用物化视图 | 是 |
| `enable_materialized_view_text_based_rewrite` | `true` | 启用基于文本的物化视图重写 | 是 |
| `max_allowed_in_element_num_of_delete` | `10000` | 删除语句 IN 元素最大数量 | 是 |

### 12. 监控配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `port_connectivity_check_interval_sec` | `60` | 端口连通性检查间隔 | 是 |
| `port_connectivity_check_retry_times` | `3` | 端口连通性检查重试次数 | 是 |
| `port_connectivity_check_timeout_ms` | `10000` | 端口连通性检查超时 | 是 |
| `slow_lock_threshold_ms` | `3000` | 慢锁阈值 | 是 |

### 13. 外部系统集成配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `hive_meta_load_concurrency` | `4` | Hive 元数据加载并发度 | 否 |
| `hive_meta_cache_refresh_interval_s` | `7200` | Hive 元数据缓存刷新间隔 | 否 |
| `hive_meta_cache_ttl_s` | `86400` | Hive 元数据缓存 TTL | 否 |
| `max_hive_partitions_per_rpc` | `5000` | 每次 RPC 最大 Hive 分区数 | 否 |
| `jdbc_connection_pool_size` | `8` | JDBC 连接池大小 | 否 |
| `jdbc_minimum_idle_connections` | `1` | JDBC 最小空闲连接数 | 否 |
| `jdbc_connection_idle_timeout_ms` | `600000` | JDBC 连接空闲超时 | 否 |

### 14. 云原生配置

| 参数名 | 默认值 | 说明 | 是否可动态修改 |
|--------|--------|------|----------------|
| `run_mode` | `shared_nothing` | 运行模式 | 否 |
| `cloud_native_meta_port` | `6090` | 云原生元数据服务端口 | 否 |
| `cloud_native_storage_type` | `S3` | 云原生存储类型 | 否 |

## 配置修改方法

### 静态配置
1. 编辑 `fe.conf` 文件
2. 重启 FE 服务使配置生效

### 动态配置
1. 通过 SQL 命令修改: `SET GLOBAL parameter_name = value;`
2. 查看当前配置: `SHOW VARIABLES LIKE 'parameter_name';`
3. 注意：动态配置在重启后会恢复为 `fe.conf` 中的值

## 针对 RPC 过载问题的关键配置

基于您遇到的问题，以下配置最为重要：

```bash
# 增加 BRPC 连接池
brpc_connection_pool_size = 64
brpc_idle_wait_max_time = 30000

# 增加 Thrift 超时和重试
thrift_rpc_timeout_ms = 30000
thrift_rpc_retry_times = 5

# 增加连接数限制
qe_max_connection = 8192
max_connection_scheduler_threads_num = 8192
```
