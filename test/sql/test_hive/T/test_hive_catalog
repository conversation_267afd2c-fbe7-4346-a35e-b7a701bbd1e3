-- name: testHiveCatalog

create external catalog hive_sql_test_${uuid0} PROPERTIES ("type"="hive", "hive.metastore.uris"="${hive_metastore_uris}");

-- only partition column Predicate with runtime filter
select * from hive_sql_test_${uuid0}.hive_oss_db.hive_oss_par_parquet_snappy where col_date = (select max(col_date) from hive_sql_test_${uuid0}.hive_oss_db.hive_oss_par_parquet_snappy);

-- partition value is null and with runtime filter for parquet
select * from hive_sql_test_${uuid0}.hive_oss_db.string_par_with_null_parquet a inner join[shuffle] (select b.id as id from hive_sql_test_${uuid0}.hive_oss_db.string_par_with_null_parquet b inner join[shuffle] hive_sql_test_${uuid0}.hive_oss_db.string_par_with_null_parquet c on b.city=c.city) d on a.id = d.id order by 1, 2, 3, 4;

-- a sql more close to real scenario and same issue as above
select * from hive_sql_test_${uuid0}.hive_oss_db.string_par_with_null_parquet a join hive_sql_test_${uuid0}.hive_oss_db.string_par_with_null_parquet b on a.id = b.id where a.city=(select max(city) from hive_sql_test_${uuid0}.hive_oss_db.string_par_with_null_parquet) and a.name is not null order by 1, 2, 3, 4, 5, 6;

-- partition value is null and with runtime filter for orc
select * from hive_sql_test_${uuid0}.hive_oss_db.string_par_with_null_orc a inner join[shuffle] (select b.id as id from hive_sql_test_${uuid0}.hive_oss_db.string_par_with_null_orc b inner join[shuffle] hive_sql_test_${uuid0}.hive_oss_db.string_par_with_null_orc c on b.city=c.city) d on a.id = d.id order by 1, 2, 3, 4;

-- test hive view with cte
-- view definition: CREATE VIEW `test_hive_view` AS with test_cte as (select `base_hive_table`.`k1` from `test_oss`.`base_hive_table`) select `test_cte`.`k1` from test_cte
select * from hive_sql_test_${uuid0}.test_oss.test_hive_view;

create external catalog unified_catalog_${uuid0} PROPERTIES ("type"="unified",
    "hive.metastore.uris"="${hive_metastore_uris}",
    "aws.s3.access_key"="${oss_ak}",
    "aws.s3.secret_key"="${oss_sk}",
    "aws.s3.endpoint"="${oss_endpoint}",
    "unified.metastore.type"="hive"
);

refresh external table unified_catalog_${uuid0}.test_oss.test_hive_view;


drop catalog unified_catalog_${uuid0};
drop catalog hive_sql_test_${uuid0};
