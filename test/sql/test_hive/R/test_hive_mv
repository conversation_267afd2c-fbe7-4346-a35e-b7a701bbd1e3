-- name: test_hive_mv
create external catalog hive_mv_test_${uuid0} PROPERTIES ("type"="hive", "hive.metastore.uris"="${hive_metastore_uris}");
-- result:
-- !result
set catalog hive_mv_test_${uuid0};
-- result:
-- !result
create database hive_db_${uuid0};
-- result:
-- !result
use hive_db_${uuid0};
-- result:
-- !result
create table t1 (k1 int, k2 int, k3 string) partition by (k3);
-- result:
-- !result
insert into t1 values (1, 1, '2021-01-01'), (2, 2, '2021-02-01'), (3, 3, '2021-03-01'), (4, 4, '2021-01-02');
-- result:
-- !result
select * from t1;
-- result:
3	3	2021-03-01
1	1	2021-01-01
4	4	2021-01-02
2	2	2021-02-01
-- !result
set catalog default_catalog;
-- result:
-- !result
create database hive_mv_${uuid0};
-- result:
-- !result
use hive_mv_${uuid0};
-- result:
-- !result
create materialized view mv1
PARTITION BY (str2date(`k3`, '%Y-%m-%d'))
DISTRIBUTED BY HASH(k1, k2) BUCKETS 5  as select * from hive_mv_test_${uuid0}.hive_db_${uuid0}.t1 where k2 in (2, 3);
-- result:
-- !result
function: wait_async_materialized_view_finish("hive_db_${uuid0}", "mv1")
-- result:
None
-- !result
select max(k1) from mv1 group by k3 having k3 = '2021-02-01';
-- result:
2
-- !result
set catalog hive_mv_test_${uuid0};
-- result:
-- !result
drop table hive_db_${uuid0}.t1 force;
-- result:
-- !result
drop database hive_db_${uuid0};
-- result:
-- !result
drop catalog hive_mv_test_${uuid0};
-- result:
-- !result
set catalog default_catalog;
-- result:
-- !result