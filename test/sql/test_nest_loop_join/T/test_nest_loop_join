-- name: test_nest_loop_join

CREATE TABLE `t1` (
  `c1` int(11) NULL COMMENT "",
  `c2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`c1`)
DISTRIBUTED BY HASH(`c1`) BUCKETS 1
PROPERTIES (
"replication_num" = "1"
);

CREATE TABLE `t2` (
  `c1` int(11) NULL COMMENT "",
  `c2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`c1`)
DISTRIBUTED BY HASH(`c1`) BUCKETS 1
PROPERTIES (
"replication_num" = "1"
);

CREATE TABLE `t3` (
  `c1` int(11) NULL COMMENT "",
  `c2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`c1`)
DISTRIBUTED BY HASH(`c1`) BUCKETS 1
PROPERTIES (
"replication_num" = "1"
);

CREATE TABLE `t4` (
  `c1` int(11) NULL COMMENT "",
  `c2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`c1`)
DISTRIBUTED BY HASH(`c1`) BUCKETS 1
PROPERTIES (
"replication_num" = "1"
);

insert into t1 select generate_series, generate_series from table(generate_series(1, 5));

insert into t2 select generate_series, generate_series from table(generate_series(1, 2));

insert into t3 select generate_series, generate_series from table(generate_series(1, 9));

insert into t4 select generate_series, generate_series from table(generate_series(2, 8));

-- base test case
-- inner join
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2;
-- left outer join
select t1.c2, t2.c2 from t1 left join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2;
-- left semi join
select t1.c2 from t1 left semi join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2;
-- left anti join
select t1.c2 from t1 left anti join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2;
-- right outer join
select t1.c2, t3.c2 from t1 right outer join [shuffle] t3 on t1.c1>t3.c1 order by t1.c2, t3.c2;
-- full outer join
select t1.c2, t4.c2 from t1 full outer join [shuffle] t4 on t1.c1>t4.c1 order by t1.c2, t4.c2;
-- right semi join
select t3.c2 from t1 right semi join [shuffle] t3 on t1.c1>t3.c1 order by t3.c2;
-- right anti join
select t4.c2 from t1 right anti join [shuffle] t4 on t1.c1>t4.c1 order by t4.c2;

-- right table is empty
-- inner join
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 where t2.c1<1 order by t1.c2, t2.c2;
-- left outer join
select t1.c2, t3.c2 from t1 left join [broadcast] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t1.c2, t3.c2;
-- left semi join
select t1.c2 from t1 left semi join [broadcast] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t1.c2;
-- left anti join
select t1.c2 from t1 left anti join [broadcast] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t1.c2;
-- right outer join
select t1.c2, t3.c2 from t1 right outer join [shuffle] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t1.c2, t3.c2;
-- full outer join
select t1.c2, t3.c2 from t1 full outer join [shuffle] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t1.c2, t3.c2;
-- right semi join
select t3.c2 from t1 right semi join [shuffle] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t3.c2;
-- right anti join
select t3.c2 from t1 right anti join [shuffle] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t3.c2;

-- left table is emptry
-- inner join
select t3.c2, t2.c2 from (select * from t1 where t1.c1<1) t3 join [broadcast] t2 on t3.c1>t2.c1 order by t3.c2, t2.c2;
-- left outer join
select t3.c2, t2.c2 from (select * from t1 where t1.c1<1) t3 left join [broadcast] t2 on t3.c1>t2.c1 order by t3.c2, t2.c2;
-- left semi join
select t3.c2 from (select * from t1 where t1.c1<1) t3 left semi join [broadcast] t2 on t3.c1>t2.c1 order by t3.c2;
-- left anti join
select t3.c2 from (select * from t1 where t1.c1<1) t3 left anti join [broadcast] t2 on t3.c1>t2.c1 order by t3.c2;
-- right outer join
select t3.c2, t2.c2 from (select * from t1 where t1.c1<1) t3 right outer join [shuffle] t2 on t3.c1>t2.c1 order by t3.c2, t2.c2;
-- full outer join
select t3.c2, t2.c2 from (select * from t1 where t1.c1<1) t3 full outer join [shuffle] t2 on t3.c1>t2.c1 order by t3.c2, t2.c2;
-- right semi join
select t2.c2 from (select * from t1 where t1.c1<1) t3 right semi join [shuffle] t2 on t3.c1>t2.c1 order by t2.c2;
-- right anti join
select t2.c2 from (select * from t1 where t1.c1<1) t3 right anti join [shuffle] t2 on t3.c1>t2.c1 order by t2.c2;

-- left table is large
truncate table t1;
truncate table t2;
insert into t1 select generate_series, generate_series from table(generate_series(1, 4098));
insert into t2 select generate_series, generate_series from table(generate_series(2, 8));

select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;

-- right table is large
truncate table t1;
truncate table t2;
insert into t1 select generate_series, generate_series from table(generate_series(2, 8));
insert into t2 select generate_series, generate_series from table(generate_series(1, 4098));

select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;

-- multi large chunk
truncate table t1;
truncate table t2;
insert into t1 select generate_series, generate_series from table(generate_series(1, 10000));
insert into t2 select generate_series, generate_series from table(generate_series(1, 10000));
select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;

-- multi large chunk (swith left)
truncate table t1;
truncate table t2;
insert into t1 select generate_series, generate_series from table(generate_series(1, 9998));
insert into t2 select generate_series, generate_series from table(generate_series(1, 10000));
select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;

-- multi large chunk (swith right)
truncate table t1;
truncate table t2;
insert into t1 select generate_series, generate_series from table(generate_series(1, 10000));
insert into t2 select generate_series, generate_series from table(generate_series(1, 9998));
select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;

-- multi small chunk
truncate table t1;
truncate table t2;
insert into t1 select generate_series, generate_series from table(generate_series(1, 77));
insert into t2 select generate_series, generate_series from table(generate_series(1, 133));
select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;


-- nestloop join with runtiime filter
truncate table t1;
truncate table t2;
insert into t1 select generate_series, generate_series from table(generate_series(1, 2000000));
insert into t2 select generate_series, generate_series from table(generate_series(1, 1));
-- apply to scan
select t1.c1 from t1 join t2 on t1.c1 <= t2.c1;
-- apply to exchange
with L as ( select  c1 lk from  t1),R as ( select  c1 rk from  t1),DIM as ( select 1000000 as mx_lo_orderkey, 999999 as mn_lo_orderkey)select count(*)from  L join [shuffle] R on lk = rk join DIM where rk >= mn_lo_orderkey and rk <= mx_lo_orderkey;