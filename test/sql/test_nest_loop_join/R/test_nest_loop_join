-- name: test_nest_loop_join
CREATE TABLE `t1` (
  `c1` int(11) NULL COMMENT "",
  `c2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`c1`)
DISTRIBUTED BY HASH(`c1`) BUCKETS 1
PROPERTIES (
"replication_num" = "1"
);
-- result:
-- !result
CREATE TABLE `t2` (
  `c1` int(11) NULL COMMENT "",
  `c2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`c1`)
DISTRIBUTED BY HASH(`c1`) BUCKETS 1
PROPERTIES (
"replication_num" = "1"
);
-- result:
-- !result
CREATE TABLE `t3` (
  `c1` int(11) NULL COMMENT "",
  `c2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`c1`)
DISTRIBUTED BY HASH(`c1`) BUCKETS 1
PROPERTIES (
"replication_num" = "1"
);
-- result:
-- !result
CREATE TABLE `t4` (
  `c1` int(11) NULL COMMENT "",
  `c2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`c1`)
DISTRIBUTED BY HASH(`c1`) BUCKETS 1
PROPERTIES (
"replication_num" = "1"
);
-- result:
-- !result
insert into t1 select generate_series, generate_series from table(generate_series(1, 5));
-- result:
-- !result
insert into t2 select generate_series, generate_series from table(generate_series(1, 2));
-- result:
-- !result
insert into t3 select generate_series, generate_series from table(generate_series(1, 9));
-- result:
-- !result
insert into t4 select generate_series, generate_series from table(generate_series(2, 8));
-- result:
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2;
-- result:
2	1
3	1
3	2
4	1
4	2
5	1
5	2
-- !result
select t1.c2, t2.c2 from t1 left join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2;
-- result:
1	None
2	1
3	1
3	2
4	1
4	2
5	1
5	2
-- !result
select t1.c2 from t1 left semi join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2;
-- result:
2
3
4
5
-- !result
select t1.c2 from t1 left anti join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2;
-- result:
1
-- !result
select t1.c2, t3.c2 from t1 right outer join [shuffle] t3 on t1.c1>t3.c1 order by t1.c2, t3.c2;
-- result:
None	5
None	6
None	7
None	8
None	9
2	1
3	1
3	2
4	1
4	2
4	3
5	1
5	2
5	3
5	4
-- !result
select t1.c2, t4.c2 from t1 full outer join [shuffle] t4 on t1.c1>t4.c1 order by t1.c2, t4.c2;
-- result:
None	5
None	6
None	7
None	8
1	None
2	None
3	2
4	2
4	3
5	2
5	3
5	4
-- !result
select t3.c2 from t1 right semi join [shuffle] t3 on t1.c1>t3.c1 order by t3.c2;
-- result:
1
2
3
4
-- !result
select t4.c2 from t1 right anti join [shuffle] t4 on t1.c1>t4.c1 order by t4.c2;
-- result:
5
6
7
8
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 where t2.c1<1 order by t1.c2, t2.c2;
-- result:
-- !result
select t1.c2, t3.c2 from t1 left join [broadcast] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t1.c2, t3.c2;
-- result:
1	None
2	None
3	None
4	None
5	None
-- !result
select t1.c2 from t1 left semi join [broadcast] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t1.c2;
-- result:
-- !result
select t1.c2 from t1 left anti join [broadcast] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t1.c2;
-- result:
1
2
3
4
5
-- !result
select t1.c2, t3.c2 from t1 right outer join [shuffle] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t1.c2, t3.c2;
-- result:
-- !result
select t1.c2, t3.c2 from t1 full outer join [shuffle] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t1.c2, t3.c2;
-- result:
1	None
2	None
3	None
4	None
5	None
-- !result
select t3.c2 from t1 right semi join [shuffle] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t3.c2;
-- result:
-- !result
select t3.c2 from t1 right anti join [shuffle] (select * from t2 where t2.c1 < 1) t3 on t1.c1>t3.c1 order by t3.c2;
-- result:
-- !result
select t3.c2, t2.c2 from (select * from t1 where t1.c1<1) t3 join [broadcast] t2 on t3.c1>t2.c1 order by t3.c2, t2.c2;
-- result:
-- !result
select t3.c2, t2.c2 from (select * from t1 where t1.c1<1) t3 left join [broadcast] t2 on t3.c1>t2.c1 order by t3.c2, t2.c2;
-- result:
-- !result
select t3.c2 from (select * from t1 where t1.c1<1) t3 left semi join [broadcast] t2 on t3.c1>t2.c1 order by t3.c2;
-- result:
-- !result
select t3.c2 from (select * from t1 where t1.c1<1) t3 left anti join [broadcast] t2 on t3.c1>t2.c1 order by t3.c2;
-- result:
-- !result
select t3.c2, t2.c2 from (select * from t1 where t1.c1<1) t3 right outer join [shuffle] t2 on t3.c1>t2.c1 order by t3.c2, t2.c2;
-- result:
None	1
None	2
-- !result
select t3.c2, t2.c2 from (select * from t1 where t1.c1<1) t3 full outer join [shuffle] t2 on t3.c1>t2.c1 order by t3.c2, t2.c2;
-- result:
None	1
None	2
-- !result
select t2.c2 from (select * from t1 where t1.c1<1) t3 right semi join [shuffle] t2 on t3.c1>t2.c1 order by t2.c2;
-- result:
-- !result
select t2.c2 from (select * from t1 where t1.c1<1) t3 right anti join [shuffle] t2 on t3.c1>t2.c1 order by t2.c2;
-- result:
1
2
-- !result
truncate table t1;
-- result:
-- !result
truncate table t2;
-- result:
-- !result
insert into t1 select generate_series, generate_series from table(generate_series(1, 4098));
-- result:
-- !result
insert into t2 select generate_series, generate_series from table(generate_series(2, 8));
-- result:
-- !result
select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
28651
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
-- result:
3	2
4	2
4	3
5	2
5	3
5	4
6	2
6	3
6	4
6	5
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
-- result:
4098	8
4098	7
4098	6
4098	5
4098	4
4098	3
4098	2
4097	8
4097	7
4097	6
-- !result
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
58791838	143227
-- !result
truncate table t1;
-- result:
-- !result
truncate table t2;
-- result:
-- !result
insert into t1 select generate_series, generate_series from table(generate_series(2, 8));
-- result:
-- !result
insert into t2 select generate_series, generate_series from table(generate_series(1, 4098));
-- result:
-- !result
select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
28
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
-- result:
2	1
3	1
3	2
4	1
4	2
4	3
5	1
5	2
5	3
5	4
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
-- result:
8	7
8	6
8	5
8	4
8	3
8	2
8	1
7	6
7	5
7	4
-- !result
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
168	84
-- !result
truncate table t1;
-- result:
-- !result
truncate table t2;
-- result:
-- !result
insert into t1 select generate_series, generate_series from table(generate_series(1, 10000));
-- result:
-- !result
insert into t2 select generate_series, generate_series from table(generate_series(1, 10000));
-- result:
-- !result
select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
49995000
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
-- result:
2	1
3	1
3	2
4	1
4	2
4	3
5	1
5	2
5	3
5	4
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
-- result:
10000	9999
10000	9998
10000	9997
10000	9996
10000	9995
10000	9994
10000	9993
10000	9992
10000	9991
10000	9990
-- !result
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
333333330000	166666665000
-- !result
truncate table t1;
-- result:
-- !result
truncate table t2;
-- result:
-- !result
insert into t1 select generate_series, generate_series from table(generate_series(1, 9998));
-- result:
-- !result
insert into t2 select generate_series, generate_series from table(generate_series(1, 10000));
-- result:
-- !result
select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
49975003
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
-- result:
2	1
3	1
3	2
4	1
4	2
4	3
5	1
5	2
5	3
5	4
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
-- result:
9998	9997
9998	9996
9998	9995
9998	9994
9998	9993
9998	9992
9998	9991
9998	9990
9998	9989
9998	9988
-- !result
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
333133369998	166566684999
-- !result
truncate table t1;
-- result:
-- !result
truncate table t2;
-- result:
-- !result
insert into t1 select generate_series, generate_series from table(generate_series(1, 10000));
-- result:
-- !result
insert into t2 select generate_series, generate_series from table(generate_series(1, 9998));
-- result:
-- !result
select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
49994999
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
-- result:
2	1
3	1
3	2
4	1
4	2
4	3
5	1
5	2
5	3
5	4
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
-- result:
10000	9998
10000	9997
10000	9996
10000	9995
10000	9994
10000	9993
10000	9992
10000	9991
10000	9990
10000	9989
-- !result
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
333333320000	166666655001
-- !result
truncate table t1;
-- result:
-- !result
truncate table t2;
-- result:
-- !result
insert into t1 select generate_series, generate_series from table(generate_series(1, 77));
-- result:
-- !result
insert into t2 select generate_series, generate_series from table(generate_series(1, 133));
-- result:
-- !result
select count(*) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
2926
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2, t2.c2 limit 10;
-- result:
2	1
3	1
3	2
4	1
4	2
4	3
5	1
5	2
5	3
5	4
-- !result
select t1.c2, t2.c2 from t1 join [broadcast] t2 on t1.c1>t2.c1 order by t1.c2 desc, t2.c2 desc limit 10;
-- result:
77	76
77	75
77	74
77	73
77	72
77	71
77	70
77	69
77	68
77	67
-- !result
select sum(t1.c2), sum(t2.c2) from t1 join [broadcast] t2 on t1.c1>t2.c1;
-- result:
152152	76076
-- !result
truncate table t1;
-- result:
-- !result
truncate table t2;
-- result:
-- !result
insert into t1 select generate_series, generate_series from table(generate_series(1, 2000000));
-- result:
-- !result
insert into t2 select generate_series, generate_series from table(generate_series(1, 1));
-- result:
-- !result
select t1.c1 from t1 join t2 on t1.c1 <= t2.c1;
-- result:
1
-- !result
with L as ( select  c1 lk from  t1),R as ( select  c1 rk from  t1),DIM as ( select 1000000 as mx_lo_orderkey, 999999 as mn_lo_orderkey)select count(*)from  L join [shuffle] R on lk = rk join DIM where rk >= mn_lo_orderkey and rk <= mx_lo_orderkey;
-- result:
2
-- !result