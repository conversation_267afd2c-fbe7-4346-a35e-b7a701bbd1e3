-- name: test_xxh3
create table t0 (id int, a string, b string);
-- result:
[]
-- !result
insert into t0(id, a, b) values (0, "hello",  "world"), (1, "hello", "starrocks");
-- result:
[]
-- !result
select bit_shift_right(xx_hash3_128(a), 64),
    bitand(xx_hash3_128(a), 18446744073709551615),
    a,
    bit_shift_right(xx_hash3_128(b), 64),
    bitand(xx_hash3_128(b), 18446744073709551615),
    b    
   from t0 where id = 1;
-- result:
-5338522934378283393	14373748016363485208	hello	3846997910503780466	1697546255957561686	starrocks
-- !result
select bit_shift_right(xx_hash3_128(a,b), 64),
    bitand(xx_hash3_128(a,b), 18446744073709551615),    
    concat(a, b) from t0 order by id;
-- result:
-2452210651042717451	1087493910761260911	helloworld
1559307639436096304	8859976453967563600	hellostarrocks
-- !result