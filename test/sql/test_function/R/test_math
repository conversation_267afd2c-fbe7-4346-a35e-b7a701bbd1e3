-- name: test_math_cosine_similarity
create table t1 (id int, data array<float>) engine = olap distributed by hash(id) properties ("replication_num" = "1");
-- result:
[]
-- !result
insert into t1 values(1, array<float>[0.1, 0.2, 0.3]), (2, array<float>[0.2, 0.1, 0.3]), (3, array<float>[0.3, 0.2, 0.1]);
-- result:
[]
-- !result
select cosine_similarity(array<float>[0.1, 0.2, 0.3], data) as dist, id from t1 order by dist desc;
-- result:
0.9999999	1
0.9285713	2
0.7142856	3
-- !result
select cosine_similarity(array<float>[0.1, 0.2, 0.3], array<float>[0.1, 0.2, 0.3]) as dist;
-- result:
0.9999999
-- !result
select cosine_similarity_norm(array<float>[0.1, 0.2, 0.3], array<float>[0.1, 0.2, 0.3]) as dist;
-- result:
0.14000002
-- !result
create table test_cosine (id int, data array<float>) ENGINE=olap DUPLICATE KEY (id) DISTRIBUTED BY HASH(id) properties ("replication_num" = "1");
-- result:
[]
-- !result
insert into test_cosine values (1, array<float>[0.1, 0.2, 0.3]), (2, array<float>[0.2, 0.1, 0.3]);
-- result:
[]
-- !result
insert into test_cosine values (3, array<float>[0.15, 0.25, 0.32]), (4, array<float>[0.12, 0.11, 0.32]);
-- result:
[]
-- !result
insert into test_cosine values (5, array<float>[0.25, 0.12, 0.13]), (6, array<float>[0.22, 0.01, 0.39]);
-- result:
[]
-- !result
select id, data, cosine_similarity(array<float>[0.1, 0.2, 0.3], data) as sim from test_cosine order by sim desc;
-- result:
1	[0.1,0.2,0.3]	0.9999999
3	[0.15,0.25,0.32]	0.99397856
4	[0.12,0.11,0.32]	0.9677269
2	[0.2,0.1,0.3]	0.9285713
6	[0.22,0.01,0.39]	0.841375
5	[0.25,0.12,0.13]	0.76792216
-- !result
select a.id, b.id, a.data, b.data, cosine_similarity(a.data, b.data) as sim from test_cosine as a cross join test_cosine as b;
-- result:
6	4	[0.22,0.01,0.39]	[0.12,0.11,0.32]	0.94712645
6	1	[0.22,0.01,0.39]	[0.1,0.2,0.3]	0.841375
6	2	[0.22,0.01,0.39]	[0.2,0.1,0.3]	0.9666862
6	3	[0.22,0.01,0.39]	[0.15,0.25,0.32]	0.8267672
6	5	[0.22,0.01,0.39]	[0.25,0.12,0.13]	0.779311
6	6	[0.22,0.01,0.39]	[0.22,0.01,0.39]	0.99999994
2	4	[0.2,0.1,0.3]	[0.12,0.11,0.32]	0.97517097
2	1	[0.2,0.1,0.3]	[0.1,0.2,0.3]	0.9285713
2	2	[0.2,0.1,0.3]	[0.2,0.1,0.3]	0.9999999
2	3	[0.2,0.1,0.3]	[0.15,0.25,0.32]	0.9322407
2	5	[0.2,0.1,0.3]	[0.25,0.12,0.13]	0.8813651
2	6	[0.2,0.1,0.3]	[0.22,0.01,0.39]	0.9666862
3	4	[0.15,0.25,0.32]	[0.12,0.11,0.32]	0.95160544
3	1	[0.15,0.25,0.32]	[0.1,0.2,0.3]	0.99397856
3	2	[0.15,0.25,0.32]	[0.2,0.1,0.3]	0.9322407
3	3	[0.15,0.25,0.32]	[0.15,0.25,0.32]	1.0
3	5	[0.15,0.25,0.32]	[0.25,0.12,0.13]	0.82288384
3	6	[0.15,0.25,0.32]	[0.22,0.01,0.39]	0.8267672
5	4	[0.25,0.12,0.13]	[0.12,0.11,0.32]	0.77120167
5	1	[0.25,0.12,0.13]	[0.1,0.2,0.3]	0.76792216
5	2	[0.25,0.12,0.13]	[0.2,0.1,0.3]	0.8813651
5	3	[0.25,0.12,0.13]	[0.15,0.25,0.32]	0.82288384
5	5	[0.25,0.12,0.13]	[0.25,0.12,0.13]	0.99999994
5	6	[0.25,0.12,0.13]	[0.22,0.01,0.39]	0.779311
1	4	[0.1,0.2,0.3]	[0.12,0.11,0.32]	0.9677269
1	1	[0.1,0.2,0.3]	[0.1,0.2,0.3]	0.9999999
1	2	[0.1,0.2,0.3]	[0.2,0.1,0.3]	0.9285713
1	3	[0.1,0.2,0.3]	[0.15,0.25,0.32]	0.99397856
1	5	[0.1,0.2,0.3]	[0.25,0.12,0.13]	0.76792216
1	6	[0.1,0.2,0.3]	[0.22,0.01,0.39]	0.841375
4	4	[0.12,0.11,0.32]	[0.12,0.11,0.32]	0.9999999
4	1	[0.12,0.11,0.32]	[0.1,0.2,0.3]	0.9677269
4	2	[0.12,0.11,0.32]	[0.2,0.1,0.3]	0.97517097
4	3	[0.12,0.11,0.32]	[0.15,0.25,0.32]	0.95160544
4	5	[0.12,0.11,0.32]	[0.25,0.12,0.13]	0.77120167
4	6	[0.12,0.11,0.32]	[0.22,0.01,0.39]	0.94712645
-- !result
select a.id, b.id, a.data, b.data, cosine_similarity(a.data, b.data) as sim from test_cosine as a cross join test_cosine as b order by sim desc;
-- result:
3	3	[0.15,0.25,0.32]	[0.15,0.25,0.32]	1.0
5	5	[0.25,0.12,0.13]	[0.25,0.12,0.13]	0.99999994
6	6	[0.22,0.01,0.39]	[0.22,0.01,0.39]	0.99999994
4	4	[0.12,0.11,0.32]	[0.12,0.11,0.32]	0.9999999
1	1	[0.1,0.2,0.3]	[0.1,0.2,0.3]	0.9999999
2	2	[0.2,0.1,0.3]	[0.2,0.1,0.3]	0.9999999
1	3	[0.1,0.2,0.3]	[0.15,0.25,0.32]	0.99397856
3	1	[0.15,0.25,0.32]	[0.1,0.2,0.3]	0.99397856
4	2	[0.12,0.11,0.32]	[0.2,0.1,0.3]	0.97517097
2	4	[0.2,0.1,0.3]	[0.12,0.11,0.32]	0.97517097
1	4	[0.1,0.2,0.3]	[0.12,0.11,0.32]	0.9677269
4	1	[0.12,0.11,0.32]	[0.1,0.2,0.3]	0.9677269
2	6	[0.2,0.1,0.3]	[0.22,0.01,0.39]	0.9666862
6	2	[0.22,0.01,0.39]	[0.2,0.1,0.3]	0.9666862
3	4	[0.15,0.25,0.32]	[0.12,0.11,0.32]	0.95160544
4	3	[0.12,0.11,0.32]	[0.15,0.25,0.32]	0.95160544
4	6	[0.12,0.11,0.32]	[0.22,0.01,0.39]	0.94712645
6	4	[0.22,0.01,0.39]	[0.12,0.11,0.32]	0.94712645
3	2	[0.15,0.25,0.32]	[0.2,0.1,0.3]	0.9322407
2	3	[0.2,0.1,0.3]	[0.15,0.25,0.32]	0.9322407
1	2	[0.1,0.2,0.3]	[0.2,0.1,0.3]	0.9285713
2	1	[0.2,0.1,0.3]	[0.1,0.2,0.3]	0.9285713
5	2	[0.25,0.12,0.13]	[0.2,0.1,0.3]	0.8813651
2	5	[0.2,0.1,0.3]	[0.25,0.12,0.13]	0.8813651
1	6	[0.1,0.2,0.3]	[0.22,0.01,0.39]	0.841375
6	1	[0.22,0.01,0.39]	[0.1,0.2,0.3]	0.841375
3	6	[0.15,0.25,0.32]	[0.22,0.01,0.39]	0.8267672
6	3	[0.22,0.01,0.39]	[0.15,0.25,0.32]	0.8267672
3	5	[0.15,0.25,0.32]	[0.25,0.12,0.13]	0.82288384
5	3	[0.25,0.12,0.13]	[0.15,0.25,0.32]	0.82288384
5	6	[0.25,0.12,0.13]	[0.22,0.01,0.39]	0.779311
6	5	[0.22,0.01,0.39]	[0.25,0.12,0.13]	0.779311
5	4	[0.25,0.12,0.13]	[0.12,0.11,0.32]	0.77120167
4	5	[0.12,0.11,0.32]	[0.25,0.12,0.13]	0.77120167
1	5	[0.1,0.2,0.3]	[0.25,0.12,0.13]	0.76792216
5	1	[0.25,0.12,0.13]	[0.1,0.2,0.3]	0.76792216
-- !result