-- name: test_regex
CREATE TABLE `ts` (
  `str` varchar(65533) NULL COMMENT "",
  `regex` varchar(65533) NULL COMMENT "",
  `replaced` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP 
DUPLICATE KEY(`str`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`str`) BUCKETS 1 PROPERTIES ("replication_num" = "1");
-- result:
-- !result
insert into ts values ('abcd', '.*', 'xx'), ('abcd', 'a.*', 'xx'), ('abcd', '.*abc.*', 'xx'), ('abcd', '.*cd', 'xx'), ('abcd', 'bc', 'xx'), ('', '', 'xx'), (NULL, '', 'xx'), ('abc中文def', '[\\p{Han}]+', 'xx');
-- result:
-- !result
insert into ts values ('a b c', " ", "-"), ('           XXXX', '       ', '');
-- result:
-- !result
insert into ts values ('xxxx', "x", "-"), ('xxxx', "xx", "-"), ('xxxx', "xxx", "-"), ('xxxx', "xxxx", "-");
-- result:
-- !result
insert into ts values ('xxxx', "not", "xxxxxxxx"), ('xxaxx', 'xx', 'aaa'), ('xaxaxax', 'xax', '-');
-- result:
-- !result
select regexp_replace('abcd', '.*', 'xx');
-- result:
xx
-- !result
select regexp_replace('abcd', 'a.*', 'xx');
-- result:
xx
-- !result
select regexp_replace('abcd', '.*abc.*', 'xx');
-- result:
xx
-- !result
select regexp_replace('abcd', '.*cd', 'xx');
-- result:
xx
-- !result
select regexp_replace('abcd', 'bc', 'xx');
-- result:
axxd
-- !result
select regexp_replace('', '', 'xx');
-- result:
-- !result
select regexp_replace(NULL, '', 'xx');
-- result:
None
-- !result
select regexp_replace('abc中文def', '中文', 'xx');
-- result:
abcxxdef
-- !result
select regexp_replace('abc中文def', '[\\p{Han}]+', 'xx');
-- result:
abcxxdef
-- !result
select regexp_replace('a b c', " ", "-");
-- result:
a-b-c
-- !result
select regexp_replace('           XXXX', '       ', '');
-- result:
    XXXX
-- !result
select regexp_replace('xxxx', "x", "-");
-- result:
----
-- !result
select regexp_replace('xxxx', "xx", "-"); 
select regexp_replace('xxxx', "xxx", "-");
-- result:
--
-- !result
select regexp_replace('xxxx', "xxxx", "-");
-- result:
-
-- !result
select regexp_replace('xxxx', "not", "xxxxxxxx");
-- result:
xxxx
-- !result
select regexp_replace('xxaxx', 'xx', 'aaa'); 
select regexp_replace('xaxaxax', 'xax', '-');
-- result:
aaaaaaa
-- !result
select str, regex, replaced, regexp_replace(str, regex, replaced) from ts order by str, regex, replaced;
-- result:
None		xx	None
		xx	xx
           XXXX	       		    XXXX
a b c	 	-	a-b-c
abcd	.*	xx	xx
abcd	.*abc.*	xx	xx
abcd	.*cd	xx	xx
abcd	a.*	xx	xx
abcd	bc	xx	axxd
abc中文def	[\p{Han}]+	xx	abcxxdef
xaxaxax	xax	-	-a-
xxaxx	xx	aaa	aaaaaaa
xxxx	not	xxxxxxxx	xxxx
xxxx	x	-	----
xxxx	xx	-	--
xxxx	xxx	-	-x
xxxx	xxxx	-	-
-- !result
-- name: test_regexp
CREATE TABLE `tsr` (
  `str` varchar(65533) NULL COMMENT "",
  `regex` varchar(65533) NULL COMMENT "",
  `pos` int NULL COMMENT ""
) ENGINE=OLAP 
DUPLICATE KEY(`str`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`str`) BUCKETS 1 PROPERTIES ("replication_num" = "1");
-- result:
-- !result
insert into tsr values ("AbCdExCeF", "([[:lower:]]+)C([[:lower:]]+)", 3), ("AbCdExCeF", "([[:lower:]]+)C([[:lower:]]+)", 0);
-- result:
-- !result
SELECT regexp_extract_all("AbCdExCeF", "([[:lower:]]+)C([[:lower:]]+)", 0);
-- result:
[]
-- !result
SELECT regexp_extract_all(str, "([[:lower:]]+)C([[:lower:]]+)", 0) from tsr;
-- result:
[]
[]
-- !result
SELECT regexp_extract_all(str, regex, 0) from tsr;
-- result:
[]
[]
-- !result
SELECT regexp_extract_all(str, regex, pos) from tsr;
-- result:
[]
[]
-- !result
SELECT regexp_extract_all(str, "([[:lower:]]+)C([[:lower:]]+)", pos) from tsr;
-- result:
[]
[]
-- !result
SELECT regexp_extract_all("AbCdExCeF", "([[:lower:]]+)C([[:lower:]]+)", pos) from tsr;
-- result:
[]
[]
-- !result
SELECT regexp_extract_all("AbCdExCeF", regex, pos) from tsr;
-- result:
[]
[]
-- !result
SELECT regexp_extract_all(str, "([[:lower:]]+)C([[:lower:]]+)", pos) from tsr;
-- result:
[]
[]
-- !result
SELECT regexp_extract_all("AbCdExCeF", "([[:lower:]]+)C([[:lower:]]+)", 3);
-- result:
[]
-- !result
SELECT regexp_extract_all(str, "([[:lower:]]+)C([[:lower:]]+)", 3) from tsr;
-- result:
[]
[]
-- !result
SELECT regexp_extract_all(str, regex, 3) from tsr;
-- result:
[]
[]
-- !result