-- name: test_time_slice
set disable_function_fold_constants=off;
-- result:
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 year, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 month, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 day, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 quarter, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 week, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 hour, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 minute, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 second, ceil);
-- result:
None
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 year);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 month);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 day);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 quarter);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 week);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 hour);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 minute);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 second);
-- result:
1974-06-25 21:49:23
-- !result
select time_slice('0000-01-01',interval 5 year);
-- result:
E: (1064, "time used with time_slice can't before 0001-01-01 00:00:00")
-- !result
select time_slice('0000-01-01',interval 5 month);
-- result:
E: (1064, "time used with time_slice can't before 0001-01-01 00:00:00")
-- !result
select time_slice('0000-01-01',interval 5 day);
-- result:
E: (1064, "time used with time_slice can't before 0001-01-01 00:00:00")
-- !result
select time_slice('0000-01-01',interval 5 quarter);
-- result:
E: (1064, "time used with time_slice can't before 0001-01-01 00:00:00")
-- !result
select time_slice('0000-01-01',interval 5 week);
-- result:
E: (1064, "time used with time_slice can't before 0001-01-01 00:00:00")
-- !result
select time_slice('9999-12-31',interval 5 year, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31',interval 5 month, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31',interval 5 day, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31',interval 5 quarter, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31',interval 5 week, ceil);
-- result:
None
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 year);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 month);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 day);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 quarter);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 week);
-- result:
0001-01-01 00:00:00
-- !result
select date_slice('2023-12-31 03:12:04',interval 3.2 year);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 57. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
select date_slice('2023-12-31 03:12:04',interval 3.2 month);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 58. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
select date_slice('2023-12-31 03:12:04',interval 3.2 day);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 56. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
select date_slice('2023-12-31 03:12:04',interval 3.2 quarter);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 60. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
select date_slice('2023-12-31 03:12:04',interval 3.2 week);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 57. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
select date_slice('2023-12-31 03:12:04',interval -3.2 week);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 58. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
set disable_function_fold_constants=on;
-- result:
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 year, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 month, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 day, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 quarter, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 week, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 hour, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 minute, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31 23:59:59',interval 5 second, ceil);
-- result:
None
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 year);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 month);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 day);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 quarter);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 week);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 hour);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 minute);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 second);
-- result:
1974-06-25 21:49:23
-- !result
select time_slice('0000-01-01',interval 5 year);
-- result:
E: (1064, "time used with time_slice can't before 0001-01-01 00:00:00")
-- !result
select time_slice('0000-01-01',interval 5 month);
-- result:
E: (1064, "time used with time_slice can't before 0001-01-01 00:00:00")
-- !result
select time_slice('0000-01-01',interval 5 day);
-- result:
E: (1064, "time used with time_slice can't before 0001-01-01 00:00:00")
-- !result
select time_slice('0000-01-01',interval 5 quarter);
-- result:
E: (1064, "time used with time_slice can't before 0001-01-01 00:00:00")
-- !result
select time_slice('0000-01-01',interval 5 week);
-- result:
E: (1064, "time used with time_slice can't before 0001-01-01 00:00:00")
-- !result
select time_slice('9999-12-31',interval 5 year, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31',interval 5 month, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31',interval 5 day, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31',interval 5 quarter, ceil);
-- result:
None
-- !result
select time_slice('9999-12-31',interval 5 week, ceil);
-- result:
None
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 year);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 month);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 day);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 quarter);
-- result:
0001-01-01 00:00:00
-- !result
select time_slice('2023-12-31 03:12:04',interval 2147483647 week);
-- result:
0001-01-01 00:00:00
-- !result
select date_slice('2023-12-31 03:12:04',interval 3.2 year);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 57. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
select date_slice('2023-12-31 03:12:04',interval 3.2 month);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 58. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
select date_slice('2023-12-31 03:12:04',interval 3.2 day);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 56. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
select date_slice('2023-12-31 03:12:04',interval 3.2 quarter);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 60. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
select date_slice('2023-12-31 03:12:04',interval 3.2 week);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 57. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
select date_slice('2023-12-31 03:12:04',interval -3.2 week);
-- result:
E: (1064, 'Getting analyzing error from line 1, column 7 to line 1, column 58. Detail message: date_slice requires second parameter must be a constant interval.')
-- !result
-- name: test_time_slice_2
select time_slice('2023-10-31 23:59:59',interval 1 millisecond);
-- result:
2023-10-31 23:59:59
-- !result
select time_slice('2023-10-31 23:59:59',interval 1 millisecond, ceil);
-- result:
2023-10-31 23:59:59.001000
-- !result
select time_slice('2023-10-31 23:59:59',interval 17 millisecond);
-- result:
2023-10-31 23:59:58.984000
-- !result
select time_slice('2023-10-31 23:59:59',interval 17 millisecond, ceil);
-- result:
2023-10-31 23:59:59.001000
-- !result
select time_slice('2023-10-31 23:59:59',interval 1000 millisecond);
-- result:
2023-10-31 23:59:59
-- !result
select time_slice('2023-10-31 23:59:59',interval 1000 millisecond, ceil);
-- result:
2023-11-01 00:00:00
-- !result
select time_slice('2023-10-31 23:59:59',interval 1001 millisecond);
-- result:
2023-10-31 23:59:58.976000
-- !result
select time_slice('2023-10-31 23:59:59',interval 1001 millisecond, ceil);
-- result:
2023-10-31 23:59:59.977000
-- !result
select time_slice('2023-10-31 23:59:59',interval 86400000 millisecond);
-- result:
2023-10-31 00:00:00
-- !result
select time_slice('2023-10-31 23:59:59',interval 86400000 millisecond, ceil);
-- result:
2023-11-01 00:00:00
-- !result
select time_slice('2023-10-31 23:59:59',interval 172800000 millisecond);
-- result:
2023-10-30 00:00:00
-- !result
select time_slice('2023-10-31 23:59:59',interval 172800000 millisecond, ceil);
-- result:
2023-11-01 00:00:00
-- !result
select time_slice('2023-10-31 23:59:59',interval 1 microsecond);
-- result:
2023-10-31 23:59:59
-- !result
select time_slice('2023-10-31 23:59:59',interval 1 microsecond, ceil);
-- result:
2023-10-31 23:59:59.000001
-- !result
select time_slice('2023-10-31 23:59:59',interval 17 microsecond);
-- result:
2023-10-31 23:59:58.999997
-- !result
select time_slice('2023-10-31 23:59:59',interval 17 microsecond, ceil);
-- result:
2023-10-31 23:59:59.000014
-- !result
select time_slice('2023-10-31 23:59:59',interval 1000 microsecond);
-- result:
2023-10-31 23:59:59
-- !result
select time_slice('2023-10-31 23:59:59',interval 1000 microsecond, ceil);
-- result:
2023-10-31 23:59:59.001000
-- !result
select time_slice('2023-10-31 23:59:59',interval 1001 microsecond);
-- result:
2023-10-31 23:59:58.999023
-- !result
select time_slice('2023-10-31 23:59:59',interval 1001 microsecond, ceil);
-- result:
2023-10-31 23:59:59.000024
-- !result
select time_slice('2023-10-31 23:59:59',interval 1000000 microsecond);
-- result:
2023-10-31 23:59:59
-- !result
select time_slice('2023-10-31 23:59:59',interval 1000000 microsecond, ceil);
-- result:
2023-11-01 00:00:00
-- !result
select time_slice('2023-10-31 23:59:59',interval 1000001 microsecond);
-- result:
2023-10-31 23:59:58.329764
-- !result
select time_slice('2023-10-31 23:59:59',interval 1000001 microsecond, ceil);
-- result:
2023-10-31 23:59:59.329765
-- !result