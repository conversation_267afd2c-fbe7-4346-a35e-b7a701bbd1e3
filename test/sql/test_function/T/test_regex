-- name: test_regex

CREATE TABLE `ts` (
  `str` varchar(65533) NULL COMMENT "",
  `regex` varchar(65533) NULL COMMENT "",
  `replaced` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP 
DUPLICATE KEY(`str`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`str`) BUCKETS 1 PROPERTIES ("replication_num" = "1");

insert into ts values ('abcd', '.*', 'xx'), ('abcd', 'a.*', 'xx'), ('abcd', '.*abc.*', 'xx'), ('abcd', '.*cd', 'xx'), ('abcd', 'bc', 'xx'), ('', '', 'xx'), (NULL, '', 'xx'), ('abc中文def', '[\\p{Han}]+', 'xx');
insert into ts values ('a b c', " ", "-"), ('           XXXX', '       ', '');
insert into ts values ('xxxx', "x", "-"), ('xxxx', "xx", "-"), ('xxxx', "xxx", "-"), ('xxxx', "xxxx", "-");
insert into ts values ('xxxx', "not", "xxxxxxxx"), ('xxaxx', 'xx', 'aaa'), ('xaxaxax', 'xax', '-');

select regexp_replace('abcd', '.*', 'xx');
select regexp_replace('abcd', 'a.*', 'xx');
select regexp_replace('abcd', '.*abc.*', 'xx');
select regexp_replace('abcd', '.*cd', 'xx');
select regexp_replace('abcd', 'bc', 'xx');
select regexp_replace('', '', 'xx');
select regexp_replace(NULL, '', 'xx');
select regexp_replace('abc中文def', '中文', 'xx');
select regexp_replace('abc中文def', '[\\p{Han}]+', 'xx');
select regexp_replace('a b c', " ", "-");
select regexp_replace('           XXXX', '       ', '');
select regexp_replace('xxxx', "x", "-");
select regexp_replace('xxxx', "xx", "-"); 
select regexp_replace('xxxx', "xxx", "-");
select regexp_replace('xxxx', "xxxx", "-");
select regexp_replace('xxxx', "not", "xxxxxxxx");
select regexp_replace('xxaxx', 'xx', 'aaa'); 
select regexp_replace('xaxaxax', 'xax', '-');

select str, regex, replaced, regexp_replace(str, regex, replaced) from ts order by str, regex, replaced;


-- name: test_regexp

CREATE TABLE `tsr` (
  `str` varchar(65533) NULL COMMENT "",
  `regex` varchar(65533) NULL COMMENT "",
  `pos` int NULL COMMENT ""
) ENGINE=OLAP 
DUPLICATE KEY(`str`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`str`) BUCKETS 1 PROPERTIES ("replication_num" = "1");

insert into tsr values ("AbCdExCeF", "([[:lower:]]+)C([[:lower:]]+)", 3), ("AbCdExCeF", "([[:lower:]]+)C([[:lower:]]+)", 0);

SELECT regexp_extract_all("AbCdExCeF", "([[:lower:]]+)C([[:lower:]]+)", 0);
SELECT regexp_extract_all(str, "([[:lower:]]+)C([[:lower:]]+)", 0) from tsr;
SELECT regexp_extract_all(str, regex, 0) from tsr;
SELECT regexp_extract_all(str, regex, pos) from tsr;
SELECT regexp_extract_all(str, "([[:lower:]]+)C([[:lower:]]+)", pos) from tsr;
SELECT regexp_extract_all("AbCdExCeF", "([[:lower:]]+)C([[:lower:]]+)", pos) from tsr;
SELECT regexp_extract_all("AbCdExCeF", regex, pos) from tsr;
SELECT regexp_extract_all(str, "([[:lower:]]+)C([[:lower:]]+)", pos) from tsr;

SELECT regexp_extract_all("AbCdExCeF", "([[:lower:]]+)C([[:lower:]]+)", 3);
SELECT regexp_extract_all(str, "([[:lower:]]+)C([[:lower:]]+)", 3) from tsr;
SELECT regexp_extract_all(str, regex, 3) from tsr;

