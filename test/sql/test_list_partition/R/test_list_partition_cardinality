-- name: test_list_partition_cardinality
DROP DATABASE IF EXISTS test_list_partition_cardinality;
-- result:
-- !result
CREATE DATABASE test_list_partition_cardinality;
-- result:
-- !result
USE test_list_partition_cardinality;
-- result:
-- !result
CREATE TABLE partitions_multi_column_1 (
    c1 int NOT NULL,
    c2 int NOT NULL,
    c3 int
)
PARTITION BY (c1, c2);
-- result:
-- !result
INSERT INTO partitions_multi_column_1 VALUES
    (1,1,1),
    (1,2,4),
    (1,2,4),
    (1,2,4),
    (2,3,2),
    (2,4,5),
    (3,5,3),
    (3,6,6);
-- result:
-- !result
INSERT INTO partitions_multi_column_1 
SELECT 4, 7, generate_series FROM TABLE(generate_series(1, 1000));
-- result:
-- !result
ANALYZE FULL TABLE partitions_multi_column_1 WITH SYNC MODE;
-- result:
test_list_partition_cardinality.partitions_multi_column_1	analyze	status	OK
-- !result
SELECT count(*) FROM partitions_multi_column_1;
-- result:
1008
-- !result
function: assert_explain_verbose_contains('SELECT COUNT(*) FROM partitions_multi_column_1 WHERE c1=0', 'EMPTYSET')
-- result:
None
-- !result
function: assert_explain_verbose_contains('SELECT COUNT(*) FROM partitions_multi_column_1 WHERE c1=1', 'cardinality: 4')
-- result:
None
-- !result
function: assert_explain_verbose_contains('SELECT COUNT(*) FROM partitions_multi_column_1 WHERE c1=2', 'cardinality: 1')
-- result:
None
-- !result
function: assert_explain_verbose_contains('SELECT COUNT(*) FROM partitions_multi_column_1 WHERE c1=3', 'cardinality: 1')
-- result:
None
-- !result
function: assert_explain_verbose_contains('SELECT COUNT(*) FROM partitions_multi_column_1 WHERE c1=4', 'cardinality: 1000')
-- result:
None
-- !result
function: assert_explain_verbose_contains('SELECT COUNT(*) FROM partitions_multi_column_1 WHERE c2=0', 'EMPTYSET')
-- result:
None
-- !result
function: assert_explain_verbose_contains('SELECT COUNT(*) FROM partitions_multi_column_1 WHERE c2=1', 'cardinality: 1')
-- result:
None
-- !result
function: assert_explain_verbose_contains('SELECT COUNT(*) FROM partitions_multi_column_1 WHERE c2=2', 'cardinality: 3')
-- result:
None
-- !result
function: assert_explain_verbose_contains('SELECT COUNT(*) FROM partitions_multi_column_1 WHERE c2=3', 'cardinality: 1')
-- result:
None
-- !result
function: assert_explain_verbose_contains('SELECT COUNT(*) FROM partitions_multi_column_1 WHERE c2=7', 'cardinality: 1000')
-- result:
None
-- !result