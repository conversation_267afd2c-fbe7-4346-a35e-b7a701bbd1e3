-- name: test_mv_refresh_with_event_trigger
create database db_${uuid0};
-- result:
-- !result
use db_${uuid0};
-- result:
-- !result
CREATE TABLE `t1` (
    `k1`  date not null, 
    `k2`  datetime not null, 
    `k3`  char(20), 
    `k4`  varchar(20), 
    `k5`  boolean, 
    `k6`  tinyint, 
    `k7`  smallint, 
    `k8`  int, 
    `k9`  bigint, 
    `k10` largeint, 
    `k11` float, 
    `k12` double, 
    `k13` decimal(27,9) ) 
DUPLICATE KEY(`k1`, `k2`, `k3`, `k4`, `k5`) 
PARTITION BY RANGE(`k1`) 
(
PARTITION p20201022 VALUES [("2020-10-22"), ("2020-10-23")), 
PARTITION p20201023 VALUES [("2020-10-23"), ("2020-10-24")), 
PARTITION p20201024 VALUES [("2020-10-24"), ("2020-10-25"))
)
DISTRIBUTED BY HASH(`k1`, `k2`, `k3`) BUCKETS 3 ;
-- result:
-- !result
CREATE TABLE `t2` (
    `k1`  date not null, 
    `k2`  datetime not null, 
    `k3`  char(20), 
    `k4`  varchar(20), 
    `k5`  boolean, 
    `k6`  tinyint, 
    `k7`  smallint, 
    `k8`  int, 
    `k9`  bigint, 
    `k10` largeint, 
    `k11` float, 
    `k12` double, 
    `k13` decimal(27,9) ) 
DUPLICATE KEY(`k1`, `k2`, `k3`, `k4`, `k5`) 
PARTITION BY RANGE(`k1`) 
(
PARTITION p20201010 VALUES [("2020-10-10"), ("2020-10-11")), 
PARTITION p20201011 VALUES [("2020-10-11"), ("2020-10-12")), 
PARTITION p20201012 VALUES [("2020-10-12"), ("2020-10-13")), 
PARTITION p20201021 VALUES [("2020-10-21"), ("2020-10-22")), 
PARTITION p20201022 VALUES [("2020-10-22"), ("2020-10-23"))
)
DISTRIBUTED BY HASH(`k1`, `k2`, `k3`) BUCKETS 3;
-- result:
-- !result
INSERT INTO t1 VALUES ('2020-10-22','2020-10-23 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t1 VALUES ('2020-10-23','2020-10-24 12:12:12','k3','k4',0,0,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t1 VALUES ('2020-10-24','2020-10-25 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t2 VALUES ('2020-10-10','2020-10-23 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t2 VALUES ('2020-10-11','2020-10-24 12:12:12','k3','k4',0,0,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t2 VALUES ('2020-10-12','2020-10-25 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t2 VALUES ('2020-10-21','2020-10-24 12:12:12','k3','k4',0,0,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t2 VALUES ('2020-10-22','2020-10-25 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
CREATE MATERIALIZED VIEW IF NOT EXISTS test_mv1
PARTITION BY `k1`
DISTRIBUTED BY HASH(`k1`)
REFRESH DEFERRED ASYNC
as 
select * from t1
union all
select * from t2;
-- result:
-- !result
CREATE MATERIALIZED VIEW IF NOT EXISTS test_mv2
PARTITION BY `k1`
DISTRIBUTED BY HASH(`k1`)
REFRESH DEFERRED ASYNC
as 
select * from t2
union all
select * from t1;
-- result:
-- !result
INSERT INTO t1 VALUES ('2020-10-22','2020-10-23 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t1 VALUES ('2020-10-23','2020-10-24 12:12:12','k3','k4',0,0,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t1 VALUES ('2020-10-24','2020-10-25 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t2 VALUES ('2020-10-10','2020-10-23 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t2 VALUES ('2020-10-11','2020-10-24 12:12:12','k3','k4',0,0,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t2 VALUES ('2020-10-12','2020-10-25 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t2 VALUES ('2020-10-21','2020-10-24 12:12:12','k3','k4',0,0,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
INSERT INTO t2 VALUES ('2020-10-22','2020-10-25 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1")
-- result:
None
-- !result
select count(1) from test_mv1;
-- result:
8
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2")
-- result:
None
-- !result
select count(1) from test_mv2;
-- result:
12
-- !result
drop materialized view test_mv1;
-- result:
-- !result
drop materialized view test_mv2;
-- result:
-- !result
drop table t1;
-- result:
-- !result
drop table t2;
-- result:
-- !result