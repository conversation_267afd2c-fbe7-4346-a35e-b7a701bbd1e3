-- name: test_mv_refresh_with_event_trigger_and_nested_mvs @slow
create database db_${uuid0};
-- result:
-- !result
use db_${uuid0};
-- result:
-- !result
CREATE TABLE `t1` (
    `k1`  date not null, 
    `k2`  datetime not null, 
    `k3`  char(20), 
    `k4`  varchar(20), 
    `k5`  boolean, 
    `k6`  tinyint, 
    `k7`  smallint, 
    `k8`  int, 
    `k9`  bigint, 
    `k10` largeint, 
    `k11` float, 
    `k12` double, 
    `k13` decimal(27,9) ) 
DUPLICATE KEY(`k1`, `k2`, `k3`, `k4`, `k5`) 
PARTITION BY RANGE(`k1`) 
(
PARTITION p20201022 VALUES [("2020-10-22"), ("2020-10-23")), 
PARTITION p20201023 VALUES [("2020-10-23"), ("2020-10-24")), 
PARTITION p20201024 VALUES [("2020-10-24"), ("2020-10-25"))
)
DISTRIBUTED BY HASH(`k1`, `k2`, `k3`) BUCKETS 3 ;
-- result:
-- !result
CREATE MATERIALIZED VIEW IF NOT EXISTS test_mv1
PARTITION BY `k1`
DISTRIBUTED BY HASH(`k1`)
REFRESH DEFERRED ASYNC
as select * from t1;
-- result:
-- !result
CREATE MATERIALIZED VIEW IF NOT EXISTS test_mv2
PARTITION BY `k1`
DISTRIBUTED BY HASH(`k1`)
REFRESH DEFERRED ASYNC
as select * from test_mv1;
-- result:
-- !result
CREATE MATERIALIZED VIEW IF NOT EXISTS test_mv3
PARTITION BY `k1`
DISTRIBUTED BY HASH(`k1`)
REFRESH DEFERRED ASYNC
as select * from test_mv2;
-- result:
-- !result
CREATE MATERIALIZED VIEW IF NOT EXISTS test_mv4
PARTITION BY `k1`
DISTRIBUTED BY HASH(`k1`)
REFRESH DEFERRED ASYNC
as select * from test_mv3;
-- result:
-- !result
set enable_materialized_view_rewrite = false;
-- result:
-- !result
INSERT INTO t1 VALUES ('2020-10-22','2020-10-22 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 1)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 1)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 1)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 1)
-- result:
None
-- !result
select max(k2) from test_mv1;
-- result:
2020-10-22 12:12:12
-- !result
select max(k2) from test_mv2;
-- result:
2020-10-22 12:12:12
-- !result
select max(k2) from test_mv3;
-- result:
2020-10-22 12:12:12
-- !result
select max(k2) from test_mv4;
-- result:
2020-10-22 12:12:12
-- !result
INSERT INTO t1 VALUES ('2020-10-24','2020-10-24 12:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 2)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 2)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 2)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 2)
-- result:
None
-- !result
select max(k2) from test_mv1;
-- result:
2020-10-24 12:13:13
-- !result
select max(k2) from test_mv2;
-- result:
2020-10-24 12:13:13
-- !result
select max(k2) from test_mv3;
-- result:
2020-10-24 12:13:13
-- !result
select max(k2) from test_mv4;
-- result:
2020-10-24 12:13:13
-- !result
INSERT INTO t1 VALUES ('2020-10-24','2020-10-24 13:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 3)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 3)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 3)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 3)
-- result:
None
-- !result
select max(k2) from test_mv1;
-- result:
2020-10-24 13:13:13
-- !result
select max(k2) from test_mv2;
-- result:
2020-10-24 13:13:13
-- !result
select max(k2) from test_mv3;
-- result:
2020-10-24 13:13:13
-- !result
select max(k2) from test_mv4;
-- result:
2020-10-24 13:13:13
-- !result
INSERT INTO t1 VALUES ('2020-10-24','2020-10-24 14:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 4)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 4)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 4)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 4)
-- result:
None
-- !result
select max(k2) from test_mv1;
-- result:
2020-10-24 14:13:13
-- !result
select max(k2) from test_mv2;
-- result:
2020-10-24 14:13:13
-- !result
select max(k2) from test_mv3;
-- result:
2020-10-24 14:13:13
-- !result
select max(k2) from test_mv4;
-- result:
2020-10-24 14:13:13
-- !result
INSERT INTO t1 VALUES ('2020-10-24','2020-10-24 15:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 5)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 5)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 5)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 5)
-- result:
None
-- !result
select max(k2) from test_mv1;
-- result:
2020-10-24 15:13:13
-- !result
select max(k2) from test_mv2;
-- result:
2020-10-24 15:13:13
-- !result
select max(k2) from test_mv3;
-- result:
2020-10-24 15:13:13
-- !result
select max(k2) from test_mv4;
-- result:
2020-10-24 15:13:13
-- !result
INSERT INTO t1 VALUES ('2020-10-24','2020-10-24 16:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889),  ('2020-10-24','2020-10-24 17:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889),  ('2020-10-24','2020-10-24 18:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
-- result:
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 6)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 6)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 6)
-- result:
None
-- !result
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 6)
-- result:
None
-- !result
select max(k2) from test_mv1;
-- result:
2020-10-24 18:13:13
-- !result
select max(k2) from test_mv2;
-- result:
2020-10-24 18:13:13
-- !result
select max(k2) from test_mv3;
-- result:
2020-10-24 18:13:13
-- !result
select max(k2) from test_mv4;
-- result:
2020-10-24 18:13:13
-- !result
drop materialized view test_mv1;
-- result:
-- !result
drop materialized view test_mv2;
-- result:
-- !result
drop materialized view test_mv3;
-- result:
-- !result
drop materialized view test_mv4;
-- result:
-- !result
drop table t1;
-- result:
-- !result