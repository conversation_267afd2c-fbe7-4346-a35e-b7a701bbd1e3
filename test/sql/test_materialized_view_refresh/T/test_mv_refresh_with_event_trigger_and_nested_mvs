-- name: test_mv_refresh_with_event_trigger_and_nested_mvs @slow

create database db_${uuid0};
use db_${uuid0};
CREATE TABLE `t1` (
    `k1`  date not null, 
    `k2`  datetime not null, 
    `k3`  char(20), 
    `k4`  varchar(20), 
    `k5`  boolean, 
    `k6`  tinyint, 
    `k7`  smallint, 
    `k8`  int, 
    `k9`  bigint, 
    `k10` largeint, 
    `k11` float, 
    `k12` double, 
    `k13` decimal(27,9) ) 
DUPLICATE KEY(`k1`, `k2`, `k3`, `k4`, `k5`) 
PARTITION BY RANGE(`k1`) 
(
PARTITION p20201022 VALUES [("2020-10-22"), ("2020-10-23")), 
PARTITION p20201023 VALUES [("2020-10-23"), ("2020-10-24")), 
PARTITION p20201024 VALUES [("2020-10-24"), ("2020-10-25"))
)
DISTRIBUTED BY HASH(`k1`, `k2`, `k3`) BUCKETS 3 ;

CREATE MATERIALIZED VIEW IF NOT EXISTS test_mv1
PARTITION BY `k1`
DISTRIBUTED BY HASH(`k1`)
REFRESH DEFERRED ASYNC
as select * from t1;

CREATE MATERIALIZED VIEW IF NOT EXISTS test_mv2
PARTITION BY `k1`
DISTRIBUTED BY HASH(`k1`)
REFRESH DEFERRED ASYNC
as select * from test_mv1;

CREATE MATERIALIZED VIEW IF NOT EXISTS test_mv3
PARTITION BY `k1`
DISTRIBUTED BY HASH(`k1`)
REFRESH DEFERRED ASYNC
as select * from test_mv2;

CREATE MATERIALIZED VIEW IF NOT EXISTS test_mv4
PARTITION BY `k1`
DISTRIBUTED BY HASH(`k1`)
REFRESH DEFERRED ASYNC
as select * from test_mv3;

set enable_materialized_view_rewrite = false;
INSERT INTO t1 VALUES ('2020-10-22','2020-10-22 12:12:12','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 1)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 1)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 1)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 1)
select max(k2) from test_mv1;
select max(k2) from test_mv2;
select max(k2) from test_mv3;
select max(k2) from test_mv4;

INSERT INTO t1 VALUES ('2020-10-24','2020-10-24 12:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 2)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 2)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 2)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 2)
select max(k2) from test_mv1;
select max(k2) from test_mv2;
select max(k2) from test_mv3;
select max(k2) from test_mv4;

INSERT INTO t1 VALUES ('2020-10-24','2020-10-24 13:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 3)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 3)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 3)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 3)
select max(k2) from test_mv1;
select max(k2) from test_mv2;
select max(k2) from test_mv3;
select max(k2) from test_mv4;

INSERT INTO t1 VALUES ('2020-10-24','2020-10-24 14:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 4)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 4)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 4)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 4)
select max(k2) from test_mv1;
select max(k2) from test_mv2;
select max(k2) from test_mv3;
select max(k2) from test_mv4;

INSERT INTO t1 VALUES ('2020-10-24','2020-10-24 15:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 5)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 5)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 5)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 5)
select max(k2) from test_mv1;
select max(k2) from test_mv2;
select max(k2) from test_mv3;
select max(k2) from test_mv4;

INSERT INTO t1 VALUES ('2020-10-24','2020-10-24 16:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889),  ('2020-10-24','2020-10-24 17:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889),  ('2020-10-24','2020-10-24 18:13:13','k3','k4',0,1,2,3,4,5,1.1,1.12,2.889);
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv1", 6)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv2", 6)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv3", 6)
function: wait_async_materialized_view_finish("db_${uuid0}", "test_mv4", 6)
select max(k2) from test_mv1;
select max(k2) from test_mv2;
select max(k2) from test_mv3;
select max(k2) from test_mv4;

drop materialized view test_mv1;
drop materialized view test_mv2;
drop materialized view test_mv3;
drop materialized view test_mv4;
drop table t1;