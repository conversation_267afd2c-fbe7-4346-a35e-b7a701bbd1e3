-- name: test_http_output_correct
use ${db[0]};
-- result:
-- !result
CREATE TABLE `duplicate_table_with_null` (
    `k1`  date,
    `k2`  datetime,
    `k3`  char(20),
    `k4`  varchar(20),
    `k5`  boolean,
    `k6`  tinyint,
    `k7`  smallint,
    `k8`  int,
    `k9`  bigint,
    `k10` largeint,
    `k11` float,
    `k12` double,
    `k13` decimal(27,9)
) 
DUPLICATE KEY(`k1`, `k2`, `k3`, `k4`, `k5`)
COMMENT "OLAP"
PROPERTIES (
    "replication_num" = "1"
);
-- result:
-- !result
INSERT INTO duplicate_table_with_null VALUES(null,null,null,null,null,null,null,null,null,null,null,null,null);
-- result:
-- !result
INSERT INTO duplicate_table_with_null VALUES(
"2020-01-27",
                    "2022-12-26 09:06:11",
                    "cheng<PERSON>",
                    "cheng<PERSON>",
                    2,
                    -125,
                    3,
                    -2147450877,
                    -9223372036854743037,
                    -18446744073709518845,
                    3274.1,
                    324.57,
                    29.628000002);
-- result:
-- !result
INSERT INTO duplicate_table_with_null VALUES("2020-01-25",
                    "2022-12-26 09:06:09",
                    "anhui",
                    "anhui",
                    0,
                    -127,
                    1,
                    -2147450879,
                    -9223372036854743039,
                    -18446744073709518847,
                    3273.8,
                    324.55,
                    29.628000000);
-- result:
-- !result
INSERT INTO duplicate_table_with_null VALUES("2020-01-26",
                    "2022-12-26 09:06:10",
                    "beijin",
                    "beijin",
                    1,
                    -126,
                    2,
                    -2147450878,
                    -9223372036854743038,
                    -18446744073709518846,
                    3273.9,
                    324.56,
                    29.628000001);
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sql' -u 'root:' -d '{"query": "select * from duplicate_table_with_null order by k6;", "onlyOutputResultRaw":true}' --header "Content-Type: application/json" 
-- result:
0
{"meta":[{"name":"k1","type":"date"},{"name":"k2","type":"datetime"},{"name":"k3","type":"varchar"},{"name":"k4","type":"varchar"},{"name":"k5","type":"boolean"},{"name":"k6","type":"tinyint(4)"},{"name":"k7","type":"smallint(6)"},{"name":"k8","type":"int(11)"},{"name":"k9","type":"bigint(20)"},{"name":"k10","type":"largeint(40)"},{"name":"k11","type":"float"},{"name":"k12","type":"double"},{"name":"k13","type":"decimal(27, 9)"}]}
{"data":[null,null,null,null,null,null,null,null,null,null,null,null,null]}
{"data":["2020-01-25","2022-12-26 09:06:09","anhui","anhui",0,-127,1,-2147450879,-9223372036854743039,"-18446744073709518847",3273.800048828125,324.55,"29.628000000"]}
{"data":["2020-01-26","2022-12-26 09:06:10","beijin","beijin",1,-126,2,-2147450878,-9223372036854743038,"-18446744073709518846",3273.89990234375,324.56,"29.628000001"]}
{"data":["2020-01-27","2022-12-26 09:06:11","chengdu","chengdu",1,-125,3,-2147450877,-9223372036854743037,"-18446744073709518845",3274.10009765625,324.57,"29.628000002"]}
-- !result
sync;
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sql' -u 'root:' -d '{"query": "show variables like \"broadcast_row_limit\";", "sessionVariables":{"broadcast_row_limit":14000000}}' --header "Content-Type: application/json"  
-- result:
0
{"meta":[{"name":"Variable_name","type":"varchar(20)"},{"name":"Value","type":"varchar(20)"}],"data":[{"Variable_name":"broadcast_row_limit","Value":"14000000"}],"statistics":{"scanRows":0,"scanBytes":0,"returnRows":1}}
-- !result
sync;
-- result:
-- !result
-- name: test_http_output_error
use ${db[0]};
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sqll' -u 'root:' -d '{"query": "select 1;"}' --header "Content-Type: application/json"
-- result:
0
{"status":"FAILED","code":"1","msg":"Not implemented","message":"Not implemented"}
-- !result
sync;
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sql' -d '{"query": "select 1;"}' --header "Content-Type: application/json"  
-- result:
0
{"status":"FAILED","code":"1","msg":"Need auth information.","message":"Need auth information."}
-- !result
sync;
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sql' -u 'root:' -d '{"query": "select 1;":"xxx"}' --header "Content-Type: application/json"  
-- result:
0
{"status":"FAILED","code":"1","msg":"malformed json [ {\"query\": \"select 1;\":\"xxx\"} ]","message":"malformed json [ {\"query\": \"select 1;\":\"xxx\"} ]"}
-- !result
sync;
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sql' -u 'root:' -d '{"sessionVariables":{"broadcast_row_limit":14000000}}' --header "Content-Type: application/json"  
-- result:
0
{"status":"FAILED","code":"1","msg":"\"query can not be empty\"","message":"\"query can not be empty\""}
-- !result
sync;
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sql' -u 'root:' -d '{"query": "select from duplicate_table_with_null;", "onlyOutputResultRaw":true}' --header "Content-Type: application/json" 
-- result:
0
{"status":"FAILED","code":"1","msg":"Getting syntax error at line 1, column 7. Detail message: Unexpected input \u0027from\u0027, the most similar input is {a legal identifier}.","message":"Getting syntax error at line 1, column 7. Detail message: Unexpected input \u0027from\u0027, the most similar input is {a legal identifier}."}
-- !result
sync;
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sql' -u 'root:' -d '{"query": "select * from duplicate_table_with_null;select 1 from duplicate_table_with_null;", "onlyOutputResultRaw":true}' --header "Content-Type: application/json" 
-- result:
0
{"status":"FAILED","code":"1","msg":"http query does not support execute multiple query","message":"http query does not support execute multiple query"}
-- !result
sync;
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sql' -u 'root:' -d '{"query": "set profile_timeout =60", "onlyOutputResultRaw":true}' --header "Content-Type: application/json" 
-- result:
0
{"status":"FAILED","code":"1","msg":"http query only support SELECT, SHOW, EXPLAIN, DESC, KILL statement","message":"http query only support SELECT, SHOW, EXPLAIN, DESC, KILL statement"}
-- !result
sync;
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sql' -u 'root:' -d '{"query": "kill 314159265432222;", "onlyOutputResultRaw":true}' --header "Content-Type: application/json" 
-- result:
0
{"status":"FAILED","code":"1","msg":"Unknown thread id: 314159265432222","message":"Unknown thread id: 314159265432222"}
-- !result
sync;
-- result:
-- !result