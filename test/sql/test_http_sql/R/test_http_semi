-- name: test_http_output_array
use ${db[0]};
-- result:
-- !result
CREATE TABLE array_test ( 
pk bigint not null ,
s_1   Array<String>, 
i_1   Array<BigInt>,
f_1   Array<Double>,
d_1   Array<DECIMAL(26, 2)>,
d_2   Array<DECIMAL64(4, 3)>,
d_3   Array<DECIMAL128(25, 19)>,
d_4   Array<DECIMAL32(8, 5)> ,
d_5   Array<DECIMAL(16, 3)>,
d_6   Array<DECIMAL128(18, 6)> ,
ai_1  Array<Array<BigInt>>,
as_1  Array<Array<String>>,
aas_1 Array<Array<Array<String>>>,
aad_1 Array<Array<Array<DECIMAL(26, 2)>>>
) ENGINE=OLAP
DUPLICATE KEY(`pk`)
DISTRIBUTED BY HASH(`pk`) BUCKETS 3
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false"
);
-- result:
-- !result
insert into array_test values
(1, ['a', 'b', 'c'], [1.0, 2.0, 3.0, 4.0, 10.0], [1.0, 2.0, 3.0, 4.0, 10.0, 1.1, 2.1, 3.2, 4.3, -1, -10, 100], [4.0, 10.0, 1.1, 2.1, 3.2, 4.3, -1, -10, 100, 1.0, 2.0, 3.0], [4.0, 10.0, 1.1, -10, 100, 1.0, 2.0, 3.0, 2.1, 3.2, 4.3, -1], [4.0, 2.1, 3.2, 10.0, 1.1, -10, 100, -1, 1.0, 2.0, 3.0, 4.3], [4.0, 2.1, 3.2, 10.0, 2.0, 3.0, 1.1, -1, -10, 100, 1.0, 4.3], [4.0, 2.1, 3.0, 1.1, 4.3, 3.2, -10, 100, 1.0, 10.0, -1, 2.0], [4.0, 2.1, 100, 1.0, 4.3, 3.2, 10.0, 2.0, 3.0, 1.1, -1, -10], [[1, 2, 3, 4], [5, 2, 6, 4], [100, -1, 92, 8], [66, 4, 32, -10]], [['1', '2', '3', '4'], ['-1', 'a', '-100', '100'], ['a', 'b', 'c']], [[['1'],['2'],['3']], [['6'],['5'],['4']], [['-1', '-2'],['-2', '10'],['100','23']]], [[[1],[2],[3]], [[6],[5],[4]], [[-1, -2],[-2, 10],[100,23]]]),
(2, ['-1', '10', '1', '100', '2'], NULL, [10.0, 20.0, 30.0, 4.0, 100.0, 10.1, 2.1, 30.2, 40.3, -1, -10, 100], [40.0, 100.0, 01.1, 2.1, 30.2, 40.3, -1, -100, 1000, 1.0, 2.0, 3.0], [40.0, 100.0, 01.1, -10, 1000, 10.0, 2.0, 30.0, 20.1, 3.2, 4.3, -1], NULL, NULL, [40.0, 20.1, 30.0, 10.1, 40.30, 30.20, -100, 1000, 1.0, 100.0, -10, 2.0], [40.0, 20.1, 1000, 10.0, 40.30, 30.20, 100.0, 20.0, 3.0, 10.1, -10, -10], NULL, NULL, [[['10'],['20'],['30']], [['60'],['5'],['4']], [['-100', '-2'],['-20', '10'],['100','23']]], [[[10],[20],[30]], [[60],[50],[4]], [[-1, -2],[-2, 100],[100,23]]]),
(4, ['a', NULL, 'c', 'e', 'd'], [1.0, 2.0, 3.0, 4.0, 10.0], [1.0, 2.0, 3.0, 4.0, 10.0, NULL, 1.1, 2.1, 3.2, NULL, 4.3, -1, -10, 100], [4.0, 10.0, 1.1, 2.1,NULL, 3.2, 4.3, -1, -10, 100, 1.0, 2.0, 3.0], [4.0, 10.0, 1.1, -10, 100, 1.0, 2.0, 3.0, 2.1, 3.2, 4.3, -1], [4.0, 2.1, 3.2, 10.0, 1.1, -10, 100, -1, 1.0, 2.0, 3.0, 4.3], [4.0, 2.1, 3.2, 10.0, 2.0, 3.0, 1.1, -1, -10, 100, 1.0, 4.3], [4.0, 2.1, 3.0, 1.1, 4.3, 3.2, -10, 100, 1.0, 10.0, -1, 2.0], [4.0, 2.1, 100, NULL, 1.0, 4.3, 3.2, 10.0, 2.0, 3.0, 1.1, -1, -10], [[1, 2, 3, NULL, 4], [5, 2, 6, 4], NULL, [100, -1, 92, 8], [66, 4, 32, -10]], [['1', '2', '3', '4'], ['-1', 'a', '-100', '100'], ['a', 'b', 'c']], [[['1'],['2'],['3']], [['6'],['5'],['4']], [['-1', '-2'],NULL,['-2', '10'],['100','23']]], [[[1],NULL,[2],[3]], [[6],[5],[4]], NULL, [[-1, -2],[-2, 10],[100,23]]]),
(3, NULL, [1.0, 2.0, 3.0, 4.0, 10.0], NULL, [40.0, 10.0, 1.1, 2.1, 3.2, 4.3, -10, -10, 100, 10.0, 20.0, 3.0], [4.0, 10.0, 1.1, -10, 100, 1.0, 20.0, 3.0, 2.1, 3.2, 4.3, -1], [40.0, 20.1, 3.2, 10.0, 10.1, -10, 100, -1, 10.0, 2.0, 30.0, 4.3], [4.0, 2.1, 3.2, 10.0, 20.0, 3.0, 1.1, -10, -100, 100, 10.0, 4.3], NULL, NULL, [[1, 2, 30, 4], [50, 2, 6, 4], [100, -10, 92, 8], [66, 40, 32, -100]], [['1', '20', '3', '4'], ['-1', 'a00', '-100', '100'], ['a', 'b0', 'c']], NULL, NULL);
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sql' -u 'root:' -d '{"query": "select * from array_test order by pk;", "onlyOutputResultRaw":true}' --header "Content-Type: application/json"
-- result:
0
{"meta":[{"name":"pk","type":"bigint(20)"},{"name":"s_1","type":"array<varchar(65533)>"},{"name":"i_1","type":"array<bigint(20)>"},{"name":"f_1","type":"array<double>"},{"name":"d_1","type":"array<DECIMAL128(26,2)>"},{"name":"d_2","type":"array<DECIMAL64(4,3)>"},{"name":"d_3","type":"array<DECIMAL128(25,19)>"},{"name":"d_4","type":"array<DECIMAL32(8,5)>"},{"name":"d_5","type":"array<DECIMAL64(16,3)>"},{"name":"d_6","type":"array<DECIMAL128(18,6)>"},{"name":"ai_1","type":"array<array<bigint(20)>>"},{"name":"as_1","type":"array<array<varchar(65533)>>"},{"name":"aas_1","type":"array<array<array<varchar(65533)>>>"},{"name":"aad_1","type":"array<array<array<DECIMAL128(26,2)>>>"}]}
{"data":[1,["a", "b", "c"],[1, 2, 3, 4, 10],[1, 2, 3, 4, 10, 1.1, 2.1, 3.2, 4.3, -1, -10, 100],["4.00", "10.00", "1.10", "2.10", "3.20", "4.30", "-1.00", "-10.00", "100.00", "1.00", "2.00", "3.00"],["4.000", "10.000", "1.100", "-10.000", "100.000", "1.000", "2.000", "3.000", "2.100", "3.200", "4.300", "-1.000"],["4.0000000000000000000", "2.1000000000000000000", "3.2000000000000000000", "10.0000000000000000000", "1.1000000000000000000", "-10.0000000000000000000", "100.0000000000000000000", "-1.0000000000000000000", "1.0000000000000000000", "2.0000000000000000000", "3.0000000000000000000", "4.3000000000000000000"],["4.00000", "2.10000", "3.20000", "10.00000", "2.00000", "3.00000", "1.10000", "-1.00000", "-10.00000", "100.00000", "1.00000", "4.30000"],["4.000", "2.100", "3.000", "1.100", "4.300", "3.200", "-10.000", "100.000", "1.000", "10.000", "-1.000", "2.000"],["4.000000", "2.100000", "100.000000", "1.000000", "4.300000", "3.200000", "10.000000", "2.000000", "3.000000", "1.100000", "-1.000000", "-10.000000"],[[1, 2, 3, 4], [5, 2, 6, 4], [100, -1, 92, 8], [66, 4, 32, -10]],[["1", "2", "3", "4"], ["-1", "a", "-100", "100"], ["a", "b", "c"]],[[["1"], ["2"], ["3"]], [["6"], ["5"], ["4"]], [["-1", "-2"], ["-2", "10"], ["100", "23"]]],[[["1.00"], ["2.00"], ["3.00"]], [["6.00"], ["5.00"], ["4.00"]], [["-1.00", "-2.00"], ["-2.00", "10.00"], ["100.00", "23.00"]]]]}
{"data":[2,["-1", "10", "1", "100", "2"],null,[10, 20, 30, 4, 100, 10.1, 2.1, 30.2, 40.3, -1, -10, 100],["40.00", "100.00", "1.10", "2.10", "30.20", "40.30", "-1.00", "-100.00", "1000.00", "1.00", "2.00", "3.00"],["40.000", "100.000", "1.100", "-10.000", "1000.000", "10.000", "2.000", "30.000", "20.100", "3.200", "4.300", "-1.000"],null,null,["40.000", "20.100", "30.000", "10.100", "40.300", "30.200", "-100.000", "1000.000", "1.000", "100.000", "-10.000", "2.000"],["40.000000", "20.100000", "1000.000000", "10.000000", "40.300000", "30.200000", "100.000000", "20.000000", "3.000000", "10.100000", "-10.000000", "-10.000000"],null,null,[[["10"], ["20"], ["30"]], [["60"], ["5"], ["4"]], [["-100", "-2"], ["-20", "10"], ["100", "23"]]],[[["10.00"], ["20.00"], ["30.00"]], [["60.00"], ["50.00"], ["4.00"]], [["-1.00", "-2.00"], ["-2.00", "100.00"], ["100.00", "23.00"]]]]}
{"data":[3,null,[1, 2, 3, 4, 10],null,["40.00", "10.00", "1.10", "2.10", "3.20", "4.30", "-10.00", "-10.00", "100.00", "10.00", "20.00", "3.00"],["4.000", "10.000", "1.100", "-10.000", "100.000", "1.000", "20.000", "3.000", "2.100", "3.200", "4.300", "-1.000"],["40.0000000000000000000", "20.1000000000000000000", "3.2000000000000000000", "10.0000000000000000000", "10.1000000000000000000", "-10.0000000000000000000", "100.0000000000000000000", "-1.0000000000000000000", "10.0000000000000000000", "2.0000000000000000000", "30.0000000000000000000", "4.3000000000000000000"],["4.00000", "2.10000", "3.20000", "10.00000", "20.00000", "3.00000", "1.10000", "-10.00000", "-100.00000", "100.00000", "10.00000", "4.30000"],null,null,[[1, 2, 30, 4], [50, 2, 6, 4], [100, -10, 92, 8], [66, 40, 32, -100]],[["1", "20", "3", "4"], ["-1", "a00", "-100", "100"], ["a", "b0", "c"]],null,null]}
{"data":[4,["a", null, "c", "e", "d"],[1, 2, 3, 4, 10],[1, 2, 3, 4, 10, null, 1.1, 2.1, 3.2, null, 4.3, -1, -10, 100],["4.00", "10.00", "1.10", "2.10", null, "3.20", "4.30", "-1.00", "-10.00", "100.00", "1.00", "2.00", "3.00"],["4.000", "10.000", "1.100", "-10.000", "100.000", "1.000", "2.000", "3.000", "2.100", "3.200", "4.300", "-1.000"],["4.0000000000000000000", "2.1000000000000000000", "3.2000000000000000000", "10.0000000000000000000", "1.1000000000000000000", "-10.0000000000000000000", "100.0000000000000000000", "-1.0000000000000000000", "1.0000000000000000000", "2.0000000000000000000", "3.0000000000000000000", "4.3000000000000000000"],["4.00000", "2.10000", "3.20000", "10.00000", "2.00000", "3.00000", "1.10000", "-1.00000", "-10.00000", "100.00000", "1.00000", "4.30000"],["4.000", "2.100", "3.000", "1.100", "4.300", "3.200", "-10.000", "100.000", "1.000", "10.000", "-1.000", "2.000"],["4.000000", "2.100000", "100.000000", null, "1.000000", "4.300000", "3.200000", "10.000000", "2.000000", "3.000000", "1.100000", "-1.000000", "-10.000000"],[[1, 2, 3, null, 4], [5, 2, 6, 4], null, [100, -1, 92, 8], [66, 4, 32, -10]],[["1", "2", "3", "4"], ["-1", "a", "-100", "100"], ["a", "b", "c"]],[[["1"], ["2"], ["3"]], [["6"], ["5"], ["4"]], [["-1", "-2"], null, ["-2", "10"], ["100", "23"]]],[[["1.00"], null, ["2.00"], ["3.00"]], [["6.00"], ["5.00"], ["4.00"]], null, [["-1.00", "-2.00"], ["-2.00", "10.00"], ["100.00", "23.00"]]]]}
-- !result
sync;
-- result:
-- !result
-- name: test_http_output_map_struct
use ${db[0]};
-- result:
-- !result
CREATE TABLE `sc2` (
  `v1` bigint(20) NULL COMMENT "",
  `map1` MAP<int(11),MAP<int(11),MAP<int(11),int(11)>>> NULL COMMENT "",
  `st1` STRUCT<s1 int(11), sm2 MAP<int(11),int(11)>, sm3 MAP<int(11),MAP<int(11),int(11)>>> NULL COMMENT "",
  `st2` STRUCT<s1 int(11), sa2 ARRAY<int(11)>, ss3 STRUCT<sss1 int(11), sss2 STRUCT<ssss1 int(11), ssss2 int(11)>>> NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`v1`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`v1`) BUCKETS 3
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);
-- result:
-- !result
insert into sc2 values (1, map{1: map{10:map{100: 101}}}, row(1, map{10: 101}, map{10: map{100: 101}}), row(1, [1,10,101], row(1, row(10, 11))));
-- result:
-- !result
insert into sc2 values (2, map{2: map{20:map{200: 202}}}, row(2, map{20: 202}, map{20: map{200: 202}}), row(2, [2,20,202], row(2, row(20, 22))));
-- result:
-- !result
insert into sc2 values (3, map{3: map{30:map{300: 303}}}, row(3, map{30: 303}, map{30: map{300: 303}}), row(3, [3,30,303], row(3, row(30, 33))));
-- result:
-- !result
insert into sc2 values (4, map{4: map{40:map{400: 404}}}, row(4, map{40: 404}, map{40: map{400: 404}}), row(4, [4,40,404], row(4, row(40, 44))));
-- result:
-- !result
insert into sc2 values (5, map{5: map{50:map{500: 505}}}, row(5, map{50: 505}, map{50: map{500: 505}}), row(5, [5,50,505], row(5, row(50, 55))));
-- result:
-- !result
shell: curl -X POST '${url}/api/v1/catalogs/default_catalog/databases/${db[0]}/sql' -u 'root:' -d '{"query": "select * from sc2 order by v1;", "onlyOutputResultRaw":true}' --header "Content-Type: application/json"
-- result:
0
{"meta":[{"name":"v1","type":"bigint(20)"},{"name":"map1","type":"map<int(11),map<int(11),map<int(11),int(11)>>>"},{"name":"st1","type":"struct<s1 int(11), sm2 map<int(11),int(11)>, sm3 map<int(11),map<int(11),int(11)>>>"},{"name":"st2","type":"struct<s1 int(11), sa2 array<int(11)>, ss3 struct<sss1 int(11), sss2 struct<ssss1 int(11), ssss2 int(11)>>>"}]}
{"data":[1,{"1": {"10": {"100": 101}}},{"s1": 1, "sm2": {"10": 101}, "sm3": {"10": {"100": 101}}},{"s1": 1, "sa2": [1, 10, 101], "ss3": {"sss1": 1, "sss2": {"ssss1": 10, "ssss2": 11}}}]}
{"data":[2,{"2": {"20": {"200": 202}}},{"s1": 2, "sm2": {"20": 202}, "sm3": {"20": {"200": 202}}},{"s1": 2, "sa2": [2, 20, 202], "ss3": {"sss1": 2, "sss2": {"ssss1": 20, "ssss2": 22}}}]}
{"data":[3,{"3": {"30": {"300": 303}}},{"s1": 3, "sm2": {"30": 303}, "sm3": {"30": {"300": 303}}},{"s1": 3, "sa2": [3, 30, 303], "ss3": {"sss1": 3, "sss2": {"ssss1": 30, "ssss2": 33}}}]}
{"data":[4,{"4": {"40": {"400": 404}}},{"s1": 4, "sm2": {"40": 404}, "sm3": {"40": {"400": 404}}},{"s1": 4, "sa2": [4, 40, 404], "ss3": {"sss1": 4, "sss2": {"ssss1": 40, "ssss2": 44}}}]}
{"data":[5,{"5": {"50": {"500": 505}}},{"s1": 5, "sm2": {"50": 505}, "sm3": {"50": {"500": 505}}},{"s1": 5, "sa2": [5, 50, 505], "ss3": {"sss1": 5, "sss2": {"ssss1": 50, "ssss2": 55}}}]}
-- !result
sync;
-- result:
-- !result