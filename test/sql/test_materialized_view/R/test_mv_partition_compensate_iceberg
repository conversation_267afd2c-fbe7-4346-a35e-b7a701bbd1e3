-- name: test_mv_iceberg_rewrite2
create external catalog mv_iceberg_${uuid0}
properties
(
    "type" = "iceberg",
    "iceberg.catalog.type" = "hive",
    "hive.metastore.uris" = "${iceberg_catalog_hive_metastore_uris}"
);
-- result:
-- !result
set catalog mv_iceberg_${uuid0};
-- result:
-- !result
create database mv_ice_db_${uuid0};
-- result:
-- !result
use mv_ice_db_${uuid0};
-- result:
-- !result
CREATE TABLE t1 (
  num int,
  dt date
)
PARTITION BY (dt);
-- result:
-- !result
INSERT INTO t1 VALUES 
  (1,"2020-06-15"),(2,"2020-06-18"),(3,"2020-06-21"),(4,"2020-06-24"),
  (1,"2020-07-02"),(2,"2020-07-05"),(3,"2020-07-08"),(4,"2020-07-11"),
  (1,"2020-07-16"),(2,"2020-07-19"),(3,"2020-07-22"),(4,"2020-07-25"),
  (2,"2020-06-15"),(3,"2020-06-18"),(4,"2020-06-21"),(5,"2020-06-24"),
  (2,"2020-07-02"),(3,"2020-07-05"),(4,"2020-07-08"),(5,"2020-07-11");
-- result:
-- !result
CREATE TABLE t2 (
  num int,
  dt string
)
PARTITION BY (dt);
-- result:
-- !result
INSERT INTO t2 VALUES 
  (1,"2020-06-15"),(2,"2020-06-18"),(3,"2020-06-21"),(4,"2020-06-24"),
  (1,"2020-07-02"),(2,"2020-07-05"),(3,"2020-07-08"),(4,"2020-07-11"),
  (1,"2020-07-16"),(2,"2020-07-19"),(3,"2020-07-22"),(4,"2020-07-25"),
  (2,"2020-06-15"),(3,"2020-06-18"),(4,"2020-06-21"),(5,"2020-06-24"),
  (2,"2020-07-02"),(3,"2020-07-05"),(4,"2020-07-08"),(5,"2020-07-11");
-- result:
-- !result
set catalog default_catalog;
-- result:
-- !result
create database db_${uuid0};
-- result:
-- !result
use db_${uuid0};
-- result:
-- !result
CREATE MATERIALIZED VIEW test_mv1 
PARTITION BY dt 
REFRESH DEFERRED MANUAL AS 
  SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 GROUP BY dt;
-- result:
-- !result
REFRESH MATERIALIZED VIEW test_mv1 WITH SYNC MODE;
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt !='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt !='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	3
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
2020-06-15	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt order by dt;
-- result:
2020-06-15	3
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt order by dt;
-- result:
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 GROUP BY dt order by dt;
-- result:
2020-06-15	3
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
INSERT INTO mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 VALUES (3, "2020-06-15");
-- result:
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('day', dt) ='2020-06-21' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt !='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-21', '2020-07-25') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt between '2020-06-16' and '2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>='2020-06-15' GROUP BY dt;", "test_mv1", "UNION")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt;", "test_mv1", "UNION")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 GROUP BY dt;", "test_mv1", "UNION")
-- result:
None
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt !='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt between '2020-06-15' and '2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt order by dt;
-- result:
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
drop materialized view default_catalog.db_${uuid0}.test_mv1;
-- result:
-- !result
CREATE MATERIALIZED VIEW test_mv1 
PARTITION BY date_trunc('day', dt)
REFRESH DEFERRED MANUAL AS 
  SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 GROUP BY dt;
-- result:
-- !result
REFRESH MATERIALIZED VIEW test_mv1 WITH SYNC MODE;
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt !='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt !='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt order by dt;
-- result:
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
INSERT INTO mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 VALUES (3, "2020-06-15");
-- result:
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('day', dt) ='2020-06-21' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt !='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-21', '2020-07-25') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt between '2020-06-16' and '2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>='2020-06-15' GROUP BY dt;", "test_mv1", "UNION")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 GROUP BY dt;", "test_mv1", "UNION")
-- result:
None
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt !='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	9
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
2020-06-15	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where dt between '2020-06-15' and '2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-15	9
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt order by dt;
-- result:
2020-06-15	9
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt order by dt;
-- result:
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 GROUP BY dt order by dt;
-- result:
2020-06-15	9
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
drop materialized view default_catalog.db_${uuid0}.test_mv1;
-- result:
-- !result
CREATE MATERIALIZED VIEW test_mv1 
PARTITION BY str2date(dt, '%Y-%m-%d')
REFRESH DEFERRED MANUAL AS 
  SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 GROUP BY dt;
-- result:
-- !result
REFRESH MATERIALIZED VIEW test_mv1 WITH SYNC MODE;
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt !='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt !='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	3
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
2020-06-15	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt order by dt;
-- result:
2020-06-15	3
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt order by dt;
-- result:
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 GROUP BY dt order by dt;
-- result:
2020-06-15	3
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
INSERT INTO mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 VALUES (3, "2020-06-15");
-- result:
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('day', dt) ='2020-06-21' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt !='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-21', '2020-07-25') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt between '2020-06-16' and '2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>='2020-06-15' GROUP BY dt;", "test_mv1", "UNION")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 GROUP BY dt;", "test_mv1", "UNION")
-- result:
None
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt !='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt between '2020-06-15' and '2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt order by dt;
-- result:
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
drop materialized view default_catalog.db_${uuid0}.test_mv1;
-- result:
-- !result
CREATE MATERIALIZED VIEW test_mv1 
PARTITION BY date_trunc('day', format_dt)
REFRESH DEFERRED MANUAL AS 
  SELECT dt, str2date(dt, '%Y-%m-%d') as format_dt, sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 GROUP BY dt;
-- result:
-- !result
REFRESH MATERIALIZED VIEW test_mv1 WITH SYNC MODE;
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt !='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt !='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	6
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt order by dt;
-- result:
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 GROUP BY dt order by dt;
-- result:
2020-06-15	6
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
INSERT INTO mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 VALUES (3, "2020-06-15");
-- result:
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('day', dt) ='2020-06-21' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt !='2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-21', '2020-07-25') GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt between '2020-06-16' and '2020-07-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>='2020-06-15' GROUP BY dt;", "test_mv1", "UNION")
-- result:
None
-- !result
function: check_no_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt;", "test_mv1")
-- result:
None
-- !result
function: check_hit_materialized_view("SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 GROUP BY dt;", "test_mv1", "UNION")
-- result:
None
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt !='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	9
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt < '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt>'2020-06-15' and dt <= '2020-07-22' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where (dt>'2020-06-15' and dt <= '2020-06-22') or dt>'2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-18	5
2020-06-21	7
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt in ('2020-06-15', '2020-06-22', '2020-07-01') GROUP BY dt order by dt;
-- result:
2020-06-15	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where dt between '2020-06-15' and '2020-07-01' GROUP BY dt order by dt;
-- result:
2020-06-15	9
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('day', dt) ='2020-06-15' GROUP BY dt order by dt;
-- result:
2020-06-15	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-06-01' GROUP BY dt order by dt;
-- result:
2020-06-15	9
2020-06-18	5
2020-06-21	7
2020-06-24	9
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 where date_trunc('month', dt) ='2020-07-01' GROUP BY dt order by dt;
-- result:
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
SELECT dt,sum(num) FROM mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 GROUP BY dt order by dt;
-- result:
2020-06-15	9
2020-06-18	5
2020-06-21	7
2020-06-24	9
2020-07-02	3
2020-07-05	5
2020-07-08	7
2020-07-11	9
2020-07-16	1
2020-07-19	2
2020-07-22	3
2020-07-25	4
-- !result
drop materialized view default_catalog.db_${uuid0}.test_mv1;
-- result:
-- !result
drop table mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t1 force;
-- result:
-- !result
drop table mv_iceberg_${uuid0}.mv_ice_db_${uuid0}.t2 force;
-- result:
-- !result