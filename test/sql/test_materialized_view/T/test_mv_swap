-- name: test_mv_swap

create database db_${uuid0};
use db_${uuid0};

CREATE TABLE ss( event_day DATE, pv BIGINT) DUPLICATE KEY(event_day) DISTRIBUTED BY HASH(event_day) BUCKETS 8 PROPERTIES("replication_num" = "1");
CREATE TABLE jj( event_day DATE, pv BIGINT) DUPLICATE KEY(event_day) DISTRIBUTED BY HASH(event_day) BUCKETS 8 PROPERTIES("replication_num" = "1");
insert into ss values('2020-01-14', 2);
insert into ss values('2020-01-14', 3);
insert into ss values('2020-01-15', 2);
insert into jj values('2020-01-14', 2);
insert into jj values('2020-01-14', 3);
insert into jj values('2020-01-15', 2);

CREATE MATERIALIZED VIEW mv1 DISTRIBUTED BY hash(event_day) AS SELECT event_day, sum(pv) as sum_pv FROM ss GROUP BY event_day;
[UC]REFRESH MATERIALIZED VIEW mv1 with sync mode ;
CREATE MATERIALIZED VIEW mv2 DISTRIBUTED BY hash(event_day) AS SELECT event_day, count(pv) as count_pv FROM ss GROUP BY event_day;
[UC]REFRESH MATERIALIZED VIEW mv2 with sync mode ;
SELECT * FROM mv1 ORDER BY event_day;
SELECT * FROM mv2 ORDER BY event_day;

ALTER MATERIALIZED VIEW mv1 SWAP WITH mv2;
SELECT * FROM mv1 ORDER BY event_day;
SELECT * FROM mv2 ORDER BY event_day;
DESC mv1;
DESC mv2;

-- refresh again
INSERT INTO ss values('2020-01-15', 2);
[UC]REFRESH MATERIALIZED VIEW mv1 with sync mode;
[UC]REFRESH MATERIALIZED VIEW mv2 with sync mode;
SELECT * FROM mv1 ORDER BY event_day;
SELECT * FROM mv2 ORDER BY event_day;

-- Try to swap with a table
ALTER MATERIALIZED VIEW mv1 SWAP WITH ss;
ALTER TABLE ss SWAP WITH mv1;

-- Try to swap with self
ALTER MATERIALIZED VIEW mv1 SWAP WITH mv1;

-- MV on MV
ALTER MATERIALIZED VIEW mv1 SWAP WITH mv2;
CREATE MATERIALIZED VIEW mv_on_mv_1 REFRESH ASYNC 
AS SELECT sum(sum_pv) as sum_sum_pv FROM mv1;
CREATE MATERIALIZED VIEW mv_on_mv_2 REFRESH ASYNC 
AS SELECT sum_sum_pv + 1 FROM mv_on_mv_1;
[UC]REFRESH MATERIALIZED VIEW mv_on_mv_1 with sync mode;
[UC]REFRESH MATERIALIZED VIEW mv_on_mv_2 with sync mode;
-- swap intermediate MV
ALTER MATERIALIZED VIEW mv1 SWAP WITH mv2;
SELECT IS_ACTIVE, INACTIVE_REASON FROM information_schema.materialized_views WHERE table_name = 'mv_on_mv_1';
SELECT IS_ACTIVE, INACTIVE_REASON FROM information_schema.materialized_views WHERE table_name = 'mv_on_mv_2';
ALTER MATERIALIZED VIEW mv_on_mv_1 ACTIVE;
ALTER MATERIALIZED VIEW mv_on_mv_2 ACTIVE;
SELECT IS_ACTIVE, INACTIVE_REASON FROM information_schema.materialized_views WHERE table_name = 'mv_on_mv_1';
SELECT IS_ACTIVE, INACTIVE_REASON FROM information_schema.materialized_views WHERE table_name = 'mv_on_mv_2';
-- swap back
ALTER MATERIALIZED VIEW mv1 SWAP WITH mv2;
ALTER MATERIALIZED VIEW mv_on_mv_1 ACTIVE;
ALTER MATERIALIZED VIEW mv_on_mv_2 ACTIVE;
SELECT IS_ACTIVE, INACTIVE_REASON FROM information_schema.materialized_views WHERE table_name = 'mv_on_mv_1';
SELECT IS_ACTIVE, INACTIVE_REASON FROM information_schema.materialized_views WHERE table_name = 'mv_on_mv_2';

-- swap base table
CREATE MATERIALIZED VIEW mv_on_table_1 REFRESH ASYNC 
AS SELECT ss.event_day, sum(ss.pv) as ss_sum_pv, sum(jj.pv) as jj_sum_pv
    FROM ss JOIN jj on (ss.event_day = jj.event_day) 
    GROUP BY ss.event_day;
[UC]REFRESH MATERIALIZED VIEW mv_on_table_1 with sync mode ;
ALTER TABLE ss SWAP WITH jj;
SELECT IS_ACTIVE, INACTIVE_REASON FROM information_schema.materialized_views WHERE table_name = 'mv_on_table_1';
ALTER MATERIALIZED VIEW mv_on_table_1 ACTIVE;
SELECT IS_ACTIVE, INACTIVE_REASON FROM information_schema.materialized_views WHERE table_name = 'mv_on_table_1';

drop database db_${uuid0};