-- name: test_materialized_view_union

create database db_${uuid0};
use db_${uuid0};

CREATE TABLE `t1` (
  `k1` int(11) NULL COMMENT "",
  `v1` int(11) NULL COMMENT "",
  `v2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`k1`)
COMMENT "OLAP"
PARTITION BY RANGE(`k1`)
(
PARTITION p2 VALUES [("202301"), ("202302")),
PARTITION p3 VALUES [("202302"), ("202303")),
PARTITION p4 VALUES [("202303"), ("202304")),
PARTITION p5 VALUES [("202304"), ("202305")),
PARTITION p6 VALUES [("202305"), ("202306")))
DISTRIBUTED BY HASH(`k1`) BUCKETS 2
PROPERTIES (
"replication_num" = "1"
);
CREATE TABLE `t2` (
  `k1` int(11) NULL COMMENT "",
  `v1` int(11) NULL COMMENT "",
  `v2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`k1`)
COMMENT "OLAP"
PARTITION BY RANGE(`k1`)
(
PARTITION p2 VALUES [("202301"), ("202302")),
PARTITION p3 VALUES [("202302"), ("202303")),
PARTITION p4 VALUES [("202303"), ("202304")),
PARTITION p5 VALUES [("202304"), ("202305")),
PARTITION p6 VALUES [("202305"), ("202306")))
DISTRIBUTED BY HASH(`k1`) BUCKETS 2
PROPERTIES (
"replication_num" = "1"
);
CREATE MATERIALIZED VIEW `mv1`
PARTITION BY (`k1`)
DISTRIBUTED BY HASH(`k1`) BUCKETS 2
REFRESH ASYNC
PROPERTIES (
"replication_num" = "1",
"storage_medium" = "HDD"
)
AS select k1, v1, v2 from t1
union all
select k1, v1, v2 from t2;

insert into t1 values ("202301",1,1),("202305",1,2);
function: wait_async_materialized_view_finish("db_${uuid0}", "mv1")
select * from mv1 order by k1, v1;
drop table t1;
drop table t2;
drop materialized view mv1;

CREATE TABLE `t1` (
  `k1` int(11) NULL COMMENT "",
  `v1` int(11) NULL COMMENT "",
  `v2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`k1`)
COMMENT "OLAP"
PARTITION BY RANGE(`k1`)
(
PARTITION p2 VALUES [("202301"), ("202302")),
PARTITION p3 VALUES [("202302"), ("202303")),
PARTITION p4 VALUES [("202303"), ("202304")),
PARTITION p5 VALUES [("202304"), ("202305")),
PARTITION p6 VALUES [("202305"), ("202306")))
DISTRIBUTED BY HASH(`k1`) BUCKETS 2
PROPERTIES (
"replication_num" = "1"
);
CREATE TABLE `t2` (
  `k1` int(11) NULL COMMENT "",
  `v1` int(11) NULL COMMENT "",
  `v2` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`k1`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`k1`) BUCKETS 2
PROPERTIES (
"replication_num" = "1"
);
insert into t1 values (202301,1,1),(202305,1,2);
CREATE MATERIALIZED VIEW `mv1`
PARTITION BY (`k1`)
DISTRIBUTED BY HASH(`k1`) BUCKETS 2
REFRESH ASYNC
PROPERTIES (
"replication_num" = "1",
"storage_medium" = "HDD",
"auto_refresh_partitions_limit" = "1"
)
AS select k1, v1, v2 from t1
union
select k1, v1, v2 from t2;
function: wait_async_materialized_view_finish("db_${uuid0}", "mv1")
select * from mv1 order by k1, v1;
drop table t1;
drop table t2;
drop materialized view mv1;