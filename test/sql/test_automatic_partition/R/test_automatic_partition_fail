-- name: test_create_partition_fail @sequential
CREATE TABLE ss( event_day DATE, pv BIGINT, cc int) DUPLICATE KEY(event_day) PARTITION BY date_trunc('month', event_day) DISTRIBUTED BY HASH(event_day) BUCKETS 1 PROPERTIES("replication_num" = "1");
-- result:
[]
-- !result
admin set frontend config ("max_automatic_partition_number"="0");
-- result:
[]
-- !result
insert into ss values('2002-01-01', 1, 2);
-- result:
[REGEX].*Automatically created partitions exceeded the maximum limit: 0.*
-- !result
admin set frontend config ("max_automatic_partition_number"="4096");
-- result:
[]
-- !result
insert into ss values('2002-01-01', 1, 2);
-- result:
[]
-- !result
