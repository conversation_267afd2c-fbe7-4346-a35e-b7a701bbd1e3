-- name: test_generated_column_rewrite
CREATE DATABASE test_generated_column_rewrite;
USE test_generated_column_rewrite;

CREATE TABLE t (id BIGINT NOT NULL, g1 bigint null as id + 1) DUPLICATE KEY (id) DISTRIBUTED BY HASH(id) BUCKETS 1 PROPERTIES("replication_num" = "1");
insert into t values(1);
 explain select * from (select id + 1 as v from t) t2;
 explain select v from (select id + 1 as v from t) t2;

DROP DATABASE test_generated_column_rewrite;

-- name: test_generated_column_subquery_rewrite
CREATE TABLE `t_generated_column_subquery_rewrite_1` (
  `id` bigint(20) NOT NULL COMMENT "",
  `col` STRING AS CONCAT(CAST(id AS STRING), "_abc")
) ENGINE=OLAP 
DUPLICATE KEY(`id`)
DISTRIBUTED BY RANDOM BUCKETS 1
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"fast_schema_evolution" = "true",
"compression" = "LZ4"
);

CREATE TABLE `t_generated_column_subquery_rewrite_2` (
  `id` bigint(20) NOT NULL COMMENT "",
  `col` STRING AS CONCAT(CAST(id AS STRING), "_abc")
) ENGINE=OLAP 
DUPLICATE KEY(`id`)
DISTRIBUTED BY RANDOM BUCKETS 1
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"fast_schema_evolution" = "true",
"compression" = "LZ4"
);

INSERT INTO t_generated_column_subquery_rewrite_1 VALUES (1);
INSERT INTO t_generated_column_subquery_rewrite_2 VALUES (1);
INSERT INTO t_generated_column_subquery_rewrite_2 VALUES (2);

function: assert_explain_not_contains('SELECT CONCAT(CAST(id AS STRING), "_abc") FROM t_generated_column_subquery_rewrite_1', "abc")
function: assert_explain_not_contains('SELECT COUNT(*) FROM (SELECT * FROM t_generated_column_subquery_rewrite_1) t_generated_column_subquery_rewrite_1 WHERE CONCAT(CAST(id AS STRING), "_abc") IS NOT NULL', "abc")
function: assert_explain_not_contains('SELECT COUNT(*) FROM (SELECT col, id FROM t_generated_column_subquery_rewrite_1) t_generated_column_subquery_rewrite_1 WHERE CONCAT(CAST(id AS STRING), "_abc") IS NOT NULL', "abc")
function: assert_explain_not_contains('SELECT COUNT(*) FROM (SELECT * FROM t_generated_column_subquery_rewrite_1 where id = 1) t_generated_column_subquery_rewrite_1 WHERE CONCAT(CAST(id AS STRING), "_abc") IS NOT NULL', "abc")
function: assert_explain_not_contains('SELECT COUNT(*) FROM (SELECT col, id FROM t_generated_column_subquery_rewrite_1 where id = 1) t_generated_column_subquery_rewrite_1 WHERE CONCAT(CAST(id AS STRING), "_abc") IS NOT NULL', "abc")
function: assert_explain_not_contains('SELECT COUNT(*) FROM (SELECT col AS col1, id AS id1 FROM t_generated_column_subquery_rewrite_1) t_generated_column_subquery_rewrite_1 WHERE CONCAT(CAST(id1 AS STRING), "_abc") IS NOT NULL', "abc")
function: assert_explain_not_contains('SELECT COUNT(*) FROM (SELECT * FROM (SELECT * FROM t_generated_column_subquery_rewrite_1) t_generated_column_subquery_rewrite_1) t_generated_column_subquery_rewrite_1 WHERE CONCAT(CAST(id AS STRING), "_abc") IS NOT NULL', 'abc')
function: assert_explain_contains('SELECT COUNT(*) FROM (SELECT col AS id FROM t_generated_column_subquery_rewrite_1) t_generated_column_subquery_rewrite_1 WHERE CONCAT(CAST(id AS STRING), "_abc") IS NOT NULL', 'abc')
function: assert_explain_not_contains('SELECT COUNT(*) FROM (SELECT * FROM t_generated_column_subquery_rewrite_1 where id = 1) result WHERE CONCAT(CAST(result.id AS STRING), "_abc") IS NOT NULL', "abc")
function: assert_explain_not_contains('SELECT COUNT(*) FROM (SELECT col, id FROM t_generated_column_subquery_rewrite_1 where id = 1) result WHERE CONCAT(CAST(result.id AS STRING), "_abc") IS NOT NULL', "abc")
function: assert_explain_not_contains('SELECT COUNT(*) FROM (SELECT t1.id as col1, t1.col as col2, t2.id as col3, t2.col as col4 FROM t_generated_column_subquery_rewrite_1 t1, t_generated_column_subquery_rewrite_2 t2 WHERE t1.id = t2.id) result WHERE CONCAT(CAST(result.col1 AS STRING), "_abc") = CONCAT(CAST(result.col3 AS STRING), "_abc")', "abc")
function: assert_explain_not_contains('SELECT COUNT(*) FROM (SELECT t1.id as col1, t1.col as col2, t2.id as col3, t2.col as col4 FROM t_generated_column_subquery_rewrite_1 t1, t_generated_column_subquery_rewrite_2 t2 WHERE CONCAT(CAST(t1.id AS STRING), "_abc") = CONCAT(CAST(t2.id AS STRING), "_abc")) result where CONCAT(CAST(result.col1 AS STRING), "_abc") = CONCAT(CAST(result.col3 AS STRING), "_abc")', "abc")
