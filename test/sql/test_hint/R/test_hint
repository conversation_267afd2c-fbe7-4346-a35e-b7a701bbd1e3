-- name: test_hint
CREATE TABLE `t1` (
  `c0` int(11) NULL COMMENT "",
  `c1` varchar(20) NULL COMMENT "",
  `c2` varchar(200) NULL COMMENT "",
  `c3` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`c0`, `c1`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`c0`, `c1`) BUCKETS 5
PROPERTIES (
"colocate_with" = "group1",
"replication_num" = "1",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);
-- result:
-- !result
CREATE TABLE `t2` (
  `c0` int(11) NULL COMMENT "",
  `c1` varchar(20) NULL COMMENT "",
  `c2` varchar(200) NULL COMMENT "",
  `c3` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`c0`, `c1`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`c0`, `c1`) BUCKETS 5
PROPERTIES (
"colocate_with" = "group1",
"replication_num" = "1",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4");
-- result:
-- !result
CREATE TABLE all_type (
    date_col DATE,
    datetime_col DATETIME,
    int_col int,
    float_col FLOAT,
    double_col DOUBLE,
    decimal_col DECIMAL(10, 2),
    varchar_col VARCHAR(255),
    char_col CHAR(10),
    array_col ARRAY<STRING>
) ENGINE=OLAP
  DUPLICATE KEY(date_col)
  COMMENT "OLAP"
  DISTRIBUTED BY HASH(date_col) BUCKETS 5
  PROPERTIES ("replication_num" = "1");
-- result:
-- !result
INSERT INTO t1 (c0, c1, c2, c3) VALUES
  (1, 'a', 'Value1', 10),
  (2, 'b', 'Value2', 20),
  (null, 'c', 'Value3', 30),
  (4, 'd', 'Value4', 40),
  (5, null, 'Value5', 50),
  (5, 'f', 'Value6', 60);
-- result:
-- !result
insert into all_type values('2021-01-01', '2021-01-01 12:00:01.123', 111111, -1.23, 1.54654, 120.26, '测试 test"', '测试\'', ['测试', '测试', 'abc'] );
-- result:
-- !result
insert /*+ set_user_variable(@a = (select max(c3) from t1)) */ into t2 select /*+ set_user_variable(@b = (select min(c1) from t1)) */
c0, @b, c2, @a from t1;
-- result:
-- !result
select @a, @b from t1;
-- result:
None	None
None	None
None	None
None	None
None	None
None	None
-- !result
select * from t2;
-- result:
None	a	Value3	60
2	a	Value2	60
5	a	Value6	60
5	a	Value5	60
1	a	Value1	60
4	a	Value4	60
-- !result
select /*+ set_user_variable(@a = (select max(c3) from t1)) */ /*+ set_user_variable(@b = (select min(c3) from t1)) */ @a, @b from t1;
-- result:
60	10
60	10
60	10
60	10
60	10
60	10
-- !result
with cte_1 as (select * from t2) select /*+ set_user_variable(@a = (select max(c3) from t1)) */ /*+ set_user_variable(@b = (select min(c3) from t1)) */ @a, @b from cte_1;
-- result:
60	10
60	10
60	10
60	10
60	10
60	10
-- !result
with cte_1 as (select * from t1)
select /*+ set_user_variable(@a = (select max(c3) from t1)), @b = 1) */ /*+ set_user_variable(@c = (select min(c3) from t1)) */  /*+ set_var(sql_mode ='GROUP_CONCAT_LEGACY') */
group_concat(@a) from cte_1;
-- result:
60, 60, 60, 60, 60, 60
-- !result
with cte_1 as (select * from t1)
select /*+ set_user_variable(@a = (select max(c3) from t1)), @b = 1) */ /*+ set_user_variable(@c = (select min(c3) from t1)) */
group_concat(@a) from cte_1;
-- result:
60,60,60,60,60,60
-- !result
select /*+ set_user_variable(@a = select from t0) */ @a from t1;
-- result:
E: (1064, "Getting syntax error at line 1, column 1. Detail message: Invalid hint value '/*+ set_user_variable(@a = select from t0) */'.")
-- !result
set @a = 1;
-- result:
-- !result
select /*+ set_user_variable(@a = 1) */ @a from t1;
-- result:
E: (1064, "Getting analyzing error. Detail message: Failed to evaluate user variable hint 'a', because the user variable name in the hint must not match any existing variable names.")
-- !result
select /*+ set_user_variable(@b = 1) */ @a, @b from t1;
-- result:
1	1
1	1
1	1
1	1
1	1
1	1
-- !result
select  @a, @b from t1;
-- result:
1	None
1	None
1	None
1	None
1	None
1	None
-- !result
with cte_1 as (select * from t1)
select /*+ set_user_variable(@a = (select max(c3) from t1)), @b = 1) */ /*+ set_user_variable(@c = (select min(c3) from t1)) */
group_concat(@a, @b, @c) from cte_1;
-- result:
E: (1064, "Getting analyzing error. Detail message: Failed to evaluate user variable hint 'a', because the user variable name in the hint must not match any existing variable names.")
-- !result
CREATE MATERIALIZED VIEW test_mv
REFRESH DEFERRED MANUAL
properties (
    "replication_num" = "1",
    "partition_refresh_number" = "1"
) as select /*+ set_user_variable(@c = (select min(c3) from t1)) */ * from t1;
-- result:
E: (1064, 'Unexpected exception: unsupported user variable hint in Materialized view for now.')
-- !result
set @test = (select date_col from all_type);
-- result:
-- !result
select @test;
-- result:
2021-01-01
-- !result
set @test = (select datetime_col from all_type);
-- result:
-- !result
select @test;
-- result:
2021-01-01 12:00:01.123000
-- !result
set @test = (select int_col from all_type);
-- result:
-- !result
select @test;
-- result:
111111
-- !result
set @test = (select float_col from all_type);
-- result:
-- !result
select @test;
-- result:
-1.23
-- !result
set @test = (select double_col from all_type);
-- result:
-- !result
select @test;
-- result:
1.54654
-- !result
set @test = (select decimal_col from all_type);
-- result:
-- !result
select @test;
-- result:
120.26
-- !result
set @test = (select varchar_col from all_type);
-- result:
-- !result
select @test;
-- result:
测试 test"
-- !result
set @test = (select char_col from all_type);
-- result:
-- !result
select @test;
-- result:
测试'
-- !result
set @test = (select array_col from all_type);
-- result:
-- !result
select @test;
-- result:
["测试","测试","abc"]
-- !result
set @test = (select array_col from all_type where date_col > '2021-01-01');
-- result:
-- !result
select @test;
-- result:
None
-- !result
set @test= (select JSON_OBJECT(' Daniel Smith', 26, 'Lily Smith', 25));
-- result:
-- !result
select @test;
-- result:
{" Daniel Smith": 26, "Lily Smith": 25}
-- !result
set @test = (select array_agg(c2) from (select t2.c2 from t2 join t2 tt join t2 ttt join t2 tttt) t);
-- result:
-- !result
select array_length(@test);
-- result:
1296
-- !result
set @test= (select cast(50 as boolean));
-- result:
-- !result
select @test;
-- result:
1
-- !result
set @test= (select cast(50 as time));
-- result:
E: (1064, "Getting analyzing error. Detail message: Can't set variable with type TIME.")
-- !result
set @test= ["{\"m\":{\"cal\":[{\"thur\":{\"use\":true,\"shift\":{\"begin\":0,\"end\":36}},\"id\":\"ID1\"}]}}", "{\"\\a\"}"];
-- result:
-- !result
select @test = ["{\"m\":{\"cal\":[{\"thur\":{\"use\":true,\"shift\":{\"begin\":0,\"end\":36}},\"id\":\"ID1\"}]}}", "{\"\\a\"}"];
-- result:
1
-- !result
set @test= concat(upper('a'), '\\', '"', '\'', 'b');
-- result:
-- !result
select @test = concat(upper('a'), '\\', '"', '\'', 'b');
-- result:
1
-- !result
set @test = ["abc\\"];
-- result:
-- !result
select @test = ["abc\\"];
-- result:
1
-- !result
set @test = ["abc'"];
-- result:
-- !result
select @test = ["abc'"];
-- result:
1
-- !result
set @test = ["abc\\'"];
-- result:
-- !result
select @test = ["abc\\'"];
-- result:
1
-- !result
set @test = concat(upper('a'), '\'', '\\' 'b', '"');
-- result:
E: (1064, "Getting syntax error at line 1, column 42. Detail message: Unexpected input ''b'', the most similar input is {',', ')'}.")
-- !result
select @test = concat(upper('a'), '\'', '\\' 'b', '"');
-- result:
E: (1064, "Getting syntax error at line 1, column 45. Detail message: Unexpected input ''b'', the most similar input is {',', ')'}.")
-- !result
SELECT /*+ set_user_variable(@c = 1, @d = 1) */c1,  lag(c1, @c, @d) over (ORDER BY c2), lead(c1, 1, @d) over (ORDER BY c2), lead(@c, 1, @d) over (ORDER BY c2) from t1;
-- result:
a	1	b	1
b	a	c	1
c	b	d	1
d	c	None	1
None	d	f	1
f	None	1	1
-- !result
SELECT /*+ set_user_variable(@c = 1, @d = 1) */c1,  lag(c1, @c, @d) over (ORDER BY c2), lead(c1, 1, @d) over (ORDER BY c2), lead(@c, 1, @d) over (ORDER BY c2) from t1;
-- result:
a	1	b	1
b	a	c	1
c	b	d	1
d	c	None	1
None	d	f	1
f	None	1	1
-- !result
select /*+ set_user_variable(@c = 1, @d = 100000) */ APPROX_TOP_K(c1, @c), APPROX_TOP_K(c2, @a, @d) from (select c1, c2 from t1 where c0 = 1) t1;
-- result:
[{"item":"a","count":1}]	[{"item":"Value1","count":1}]
-- !result
select /*+ set_user_variable(@c = 1, @d = 10) */ c1, ntile(@c) over (partition by c2 order by c3) as bucket_id from t1;
-- result:
a	1
d	1
c	1
f	1
b	1
None	1
-- !result