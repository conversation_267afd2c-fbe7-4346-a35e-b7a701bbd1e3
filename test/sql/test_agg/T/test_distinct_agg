-- name: test_distinct_agg

CREATE TABLE `skew_agg` (
  `c0` int DEFAULT NULL,
  `c1` bigint DEFAULT NULL,
  `c2` bigint DEFAULT NULL,
  `c3` bigint DEFAULT NULL,
  `c4` varchar(100) DEFAULT NULL,
  `c5` varchar(100) DEFAULT NULL
) ENGINE=OLAP
DUPLICATE KEY(`c0`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`c0`, `c1`) BUCKETS 6
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


INSERT INTO `skew_agg` VALUES (480816,2,3,4,'a','0.8368445176974354'),(649665,2,3,4,'a','1.9861146043856763'),(16292,2,3,4,'a','0.20457737725654374'),(462567,2,3,4,'a','0.01193928160885282'),(642147,2,3,4,'a','0.3856505084649199'),(37686,2,3,4,'a','1.219906679559412'),(936709,2,3,4,'a','1.7073728886501611'),(458304,2,3,4,'a','1.460925321523962'),(277400,2,3,4,'a','0.3912256643094361'),(145864,2,3,4,'a','0.28496028136923934'),(274810,2,3,4,'a','1.8932149204362323'),(908609,2,3,4,'a','1.4064415091708689'),(790278,2,3,4,'a','1.6834549435260286'),(837804,2,3,4,'a','1.327670897662333'),(805767,2,3,4,'a','0.07465311705568164'),(769333,2,3,4,'a','1.469371698302563'),(365430,2,3,4,'a','1.246184767453172'),(19172,2,3,4,'a','0.45316585009281135'),(75399,2,3,4,'a','1.3944887978904776'),(260026,2,3,4,'a','0.4167950622893824'),(261909,2,3,4,'a','1.3687075556895767'),(636041,2,3,4,'a','0.25428855070312373'),(727598,2,3,4,'a','0.5131150153587712'),(99993,2,3,4,'a','1.4605888886941494'),(351492,2,3,4,'a','1.1331543001655064'),(778410,2,3,4,'a','0.3846322581028866'),(626352,2,3,4,'a','1.1096247556708985'),(895005,2,3,4,'a','1.6211805880266992'),(367935,2,3,4,'a','0.8158089246748098'),(935717,2,3,4,'a','0.9097448297867038'),(467211,2,3,4,'a','1.9428719970796928'),(455548,2,3,4,'a','0.7268650296394388'),(450518,2,3,4,'a','0.32458814263771113'),(459915,2,3,4,'a','1.625389996567173'),(683729,2,3,4,'a','1.9611172042424951'),(851607,2,3,4,'a','0.63271746470846'),(26973,2,3,4,'a','0.3715775444838941'),(848025,2,3,4,'a','1.365516716023457'),(869717,2,3,4,'a','0.6006202572962458'),(892400,2,3,4,'a','1.122134997623167'),(128139,2,3,4,'a','1.9149831923795708'),(403042,2,3,4,'a','0.28546622980895103'),(504541,2,3,4,'a','0.18901018815954235'),(958903,2,3,4,'a','1.0219980599563552'),(678287,2,3,4,'a','1.7168719989404753'),(257320,2,3,4,'a','1.4225859934674445'),(784504,2,3,4,'a','1.5772864386227787'),(589703,2,3,4,'a','1.165168945831404'),(143814,2,3,4,'a','1.942632576397278'),(425140,2,3,4,'a','0.42349899227125476'),(783329,2,3,4,'a','0.5627882187839488'),(56985,2,3,4,'a','0.8814857796593437'),(32759,2,3,4,'a','1.6831363976776008'),(109563,2,3,4,'a','0.04622009214593106'),(786861,2,3,4,'a','1.7299547248798932'),(964302,2,3,4,'a','0.45315949661020144'),(239992,2,3,4,'a','1.040438464880398'),(881121,2,3,4,'a','1.6898959164413585'),(581376,2,3,4,'a','0.7440767816678404'),(116063,2,3,4,'a','0.9283961569223517'),(972802,2,3,4,'a','0.9428364997197283'),(438684,2,3,4,'a','1.5583286355792811'),(579770,2,3,4,'a','1.1227161317343974'),(67480,2,3,4,'a','1.30664933594563'),(64184,2,3,4,'a','0.7218927505630001'),(612180,2,3,4,'a','1.9561187903919433'),(53758,2,3,4,'a','0.6692228155855302'),(511783,2,3,4,'a','1.1101649358031944'),(240062,2,3,4,'a','1.070128993196533'),(955135,2,3,4,'a','0.34096700171098765'),(987011,2,3,4,'a','0.8472126469455777'),(156998,2,3,4,'a','1.0283378297726975'),(99852,2,3,4,'a','1.913505496376665'),(484208,2,3,4,'a','1.1015645164079633'),(301287,2,3,4,'a','1.7081768975669303'),(366581,2,3,4,'a','0.5412809593056152'),(253459,2,3,4,'a','0.9107467019099246'),(516490,2,3,4,'a','0.4326592557436407'),(532178,2,3,4,'a','0.02380609514546217'),(462980,2,3,4,'a','0.5583830592766247'),(7017,2,3,4,'a','0.39502382315231843'),(966508,2,3,4,'a','0.4800056596100383'),(300491,2,3,4,'a','1.5648936774254718'),(10761,2,3,4,'a','1.4129294877992287'),(500041,2,3,4,'a','0.7616177245617078'),(403923,2,3,4,'a','1.7543719296850002'),(174162,2,3,4,'a','0.47850769616524474'),(673782,2,3,4,'a','1.3022994131802577'),(234402,2,3,4,'a','0.43712013814330114'),(389595,2,3,4,'a','0.5845884593060132'),(292687,2,3,4,'a','1.1731000367301516'),(54691,2,3,4,'a','1.0276051173243719'),(404941,2,3,4,'a','0.9665974517973116'),(201670,2,3,4,'a','1.1169060814351832'),(187256,2,3,4,'a','0.5218390342982849'),(742830,2,3,4,'a','1.8627874719563755'),(428477,2,3,4,'a','0.6964112116921798'),(455596,2,3,4,'a','0.4667263277496456'),(800028,2,3,4,'a','0.6000998752192593'),(100166,2,3,4,'a','1.2013612326247256'),(702905,2,3,4,'a','1.4249650961020637'),(453698,2,3,4,'a','0.26208628552228797'),(294121,2,3,4,'a','0.1549541132105087'),(505021,2,3,4,'a','0.5853521345046835'),(948316,2,3,4,'a','1.727104089900017'),(472812,2,3,4,'a','1.5468090507637793'),(448586,2,3,4,'a','1.845435708617266'),(267830,2,3,4,'a','1.1419975600596495'),(51503,2,3,4,'a','1.0890328167835557'),(568074,2,3,4,'a','0.41364603155632135'),(329892,2,3,4,'a','0.05797980172371473'),(155274,2,3,4,'a','1.3788025019492978'),(981184,2,3,4,'a','1.675429874728834'),(245024,2,3,4,'a','1.423945655509779'),(824794,2,3,4,'a','1.976099135331902'),(465867,2,3,4,'a','0.7303732249237348'),(428332,2,3,4,'a','0.0921996087694537'),(945503,2,3,4,'a','1.1784340955116173'),(109575,2,3,4,'a','1.560451889001254'),(572404,2,3,4,'a','1.0426795846248786'),(889488,2,3,4,'a','1.7668456600670197'),(748649,2,3,4,'a','0.18595183285507544'),(218933,2,3,4,'a','1.6314744368488663'),(421887,2,3,4,'a','1.3244483576383892'),(45459,2,3,4,'a','0.48124941669520865'),(66745,2,3,4,'a','1.223704251669072'),(859025,2,3,4,'a','0.9191365846610969'),(720767,2,3,4,'a','0.4502573967448039'),(963343,2,3,4,'a','0.28266182940701196'),(816624,2,3,4,'a','1.3182587412356015'),(845774,2,3,4,'a','0.5029601720189305'),(720080,2,3,4,'a','1.6919151504448755'),(69549,2,3,4,'a','1.6197468634878722'),(840720,2,3,4,'a','1.5479546054713005'),(347728,2,3,4,'a','0.8334162857694704'),(40357,2,3,4,'a','1.903318533583841'),(637226,2,3,4,'a','0.6623061193733533'),(744087,2,3,4,'a','1.453950019044755'),(402615,2,3,4,'a','1.664295840695776'),(952896,2,3,4,'a','0.5360713550225564'),(481491,2,3,4,'a','1.2066937323721998'),(572262,2,3,4,'a','0.102539273074399'),(539562,2,3,4,'a','1.088003150269392'),(101322,2,3,4,'a','1.7492069506544685'),(69053,2,3,4,'a','1.4429042632159816'),(400103,2,3,4,'a','1.6723189984190454'),(980488,2,3,4,'a','0.7879231542236387'),(28344,2,3,4,'a','1.9196694976833364'),(714142,2,3,4,'a','1.3824126211762546'),(313605,2,3,4,'a','0.9888148764044185'),(531221,2,3,4,'a','0.34576961430327'),(270760,2,3,4,'a','1.6702893373279732'),(363444,2,3,4,'a','0.6235714337076725'),(468597,2,3,4,'a','0.815253757699629'),(632344,2,3,4,'a','1.877679046129509'),(797166,2,3,4,'a','0.3386186103696177'),(455050,2,3,4,'a','1.5346427350627656'),(471457,2,3,4,'a','0.11064502607159785'),(862241,2,3,4,'a','0.29047143300107814'),(139457,2,3,4,'a','0.5231525455817139'),(889511,2,3,4,'a','1.3256560222484692'),(645606,2,3,4,'a','0.47909206569110235'),(260912,2,3,4,'a','1.1718456011003309'),(146878,2,3,4,'a','1.953238382891974'),(442463,2,3,4,'a','0.5649194182501356'),(84908,2,3,4,'a','1.1543240539304205'),(631085,2,3,4,'a','0.8478828136323792'),(226451,2,3,4,'a','1.7208575268470288'),(622792,2,3,4,'a','1.0653482406077424'),(794994,2,3,4,'a','0.7538993831294583'),(499765,2,3,4,'a','0.7359546671956355'),(340591,2,3,4,'a','1.1980447817575604'),(973339,2,3,4,'a','0.13925940183853674'),(428130,2,3,4,'a','1.8635261523197648'),(374424,2,3,4,'a','0.15366283073431145'),(260885,2,3,4,'a','0.14785896627964357'),(586993,2,3,4,'a','1.4263554359100343'),(804909,2,3,4,'a','1.7700231389794714'),(10331,2,3,4,'a','0.7932412706252572'),(952110,2,3,4,'a','1.1413764442702536'),(997111,2,3,4,'a','0.5469815587131135'),(376121,2,3,4,'a','0.12026217032248357'),(172293,2,3,4,'a','1.3621477609147763'),(888489,2,3,4,'a','0.7984484776840065'),(330654,2,3,4,'a','0.9111917809687479'),(286018,2,3,4,'a','0.12660809245575974'),(458465,2,3,4,'a','0.20482828114631427'),(136675,2,3,4,'a','0.7522650628837432'),(470638,2,3,4,'a','0.44958221767990125'),(712042,2,3,4,'a','1.7716771231700452'),(293066,2,3,4,'a','1.6156259343173596'),(159868,2,3,4,'a','0.7517991967050351'),(399895,2,3,4,'a','1.7435518817450404'),(159195,2,3,4,'a','0.3612935360160596'),(425649,2,3,4,'a','1.1726108018053796'),(654580,2,3,4,'a','1.027963963363286'),(606171,2,3,4,'a','0.9778186408596287'),(626033,2,3,4,'a','1.3268790592689803'),(439097,2,3,4,'a','0.4103349115795781'),(708546,2,3,4,'a','1.8544523397967707'),(510494,2,3,4,'a','1.54158080512805'),(322471,2,3,4,'a','0.5999647924676209'),(532500,2,3,4,'a','1.5251043639379593'),(215261,2,3,4,'a','1.57730033581825'),(297467,2,3,4,'a','0.24276765831072597'),(714519,2,3,4,'a','0.4168830387451528'),(898652,2,3,4,'a','1.7358693645669794'),(643718,2,3,4,'a','1.229571131271842'),(142774,2,3,4,'a','1.7390277662677949'),(919247,2,3,4,'a','1.9753879308471343'),(180729,2,3,4,'a','1.881121342891009'),(160618,2,3,4,'a','1.96281469051057'),(425183,2,3,4,'a','0.3633875868873555'),(632920,2,3,4,'a','1.2390324894702365'),(198823,2,3,4,'a','0.27112957115390296'),(81356,2,3,4,'a','0.00016999989763833573'),(756357,2,3,4,'a','1.5630644108756113'),(638589,2,3,4,'a','1.6966943607634812'),(325970,2,3,4,'a','0.16961370983125057'),(446125,2,3,4,'a','1.9524117931280396'),(542654,2,3,4,'a','1.569301234157105'),(295292,2,3,4,'a','0.24501786403825307'),(726668,2,3,4,'a','0.5316288625184715'),(149067,2,3,4,'a','1.8957881647122914'),(292268,2,3,4,'a','1.235315920072902'),(211486,2,3,4,'a','0.40890955218012404'),(387817,2,3,4,'a','0.6514387639718491'),(465147,2,3,4,'a','0.6971510990496269'),(347438,2,3,4,'a','1.3829225594018797'),(414994,2,3,4,'a','0.0011692903946799137'),(757942,2,3,4,'a','1.5759140360876118'),(665959,2,3,4,'a','1.9318436253181133'),(831733,2,3,4,'a','0.5218031094612583'),(809308,2,3,4,'a','0.5276670051064967'),(891245,2,3,4,'a','1.3294456650777213'),(649880,2,3,4,'a','0.5104651437238465'),(326522,2,3,4,'a','1.7338272982592018'),(355001,2,3,4,'a','0.3485329266158239'),(806328,2,3,4,'a','1.0176804745734487'),(125217,2,3,4,'a','0.19913323055872062'),(122181,2,3,4,'a','0.6244076626602632'),(194477,2,3,4,'a','0.07154706872212428'),(595437,2,3,4,'a','1.7397258949836025'),(563005,2,3,4,'a','0.41086893939494057'),(338158,2,3,4,'a','0.14897734685705727'),(357968,2,3,4,'a','1.132749688935233'),(757970,2,3,4,'a','0.18144852126152117'),(179712,2,3,4,'a','1.252775256757415'),(592802,2,3,4,'a','0.16969374024280695'),(645829,2,3,4,'a','1.9492043070022056'),(935525,2,3,4,'a','1.5076387240622553'),(962521,2,3,4,'a','1.1022975473686099'),(868180,2,3,4,'a','1.374905436648899'),(832724,2,3,4,'a','0.20252592321720525'),(8142,2,3,4,'a','1.4738441644924172'),(660184,2,3,4,'a','0.18030636960669083'),(470214,2,3,4,'a','0.16122409530098'),(992417,2,3,4,'a','1.4405010374640124'),(624001,2,3,4,'a','1.918503407313026'),(924257,2,3,4,'a','1.4870559549769908'),(944870,2,3,4,'a','0.9875330319512011'),(634222,2,3,4,'a','1.3796245654855153'),(546394,2,3,4,'a','1.3250700098695884'),(673492,2,3,4,'a','0.7597096513581552'),(878798,2,3,4,'a','0.5088538401842618'),(635740,2,3,4,'a','0.8308366544831886'),(169872,2,3,4,'a','1.2062124267278356'),(505914,2,3,4,'a','1.440506770685787'),(83524,2,3,4,'a','0.5137173556850454'),(33722,2,3,4,'a','0.7960693750493875'),(889007,2,3,4,'a','0.5018624779785634'),(587635,2,3,4,'a','0.370763259353827'),(164003,2,3,4,'a','0.5277401791212523'),(827342,2,3,4,'a','0.6901946223249609'),(243462,2,3,4,'a','0.3640367652885958'),(179706,2,3,4,'a','0.7049474927642825'),(223251,2,3,4,'a','0.11767202812961491'),(624426,2,3,4,'a','1.8912416527897506'),(854827,2,3,4,'a','0.8745437030443397'),(621879,2,3,4,'a','1.595156035940308'),(122254,2,3,4,'a','0.4370719086724072'),(725918,2,3,4,'a','1.947962494518573'),(692153,2,3,4,'a','1.0776407710068308'),(617644,2,3,4,'a','0.9435127516682379'),(505851,2,3,4,'a','0.2279758958406522'),(52386,2,3,4,'a','1.839927809163861'),(442663,2,3,4,'a','0.9068458647530953'),(939126,2,3,4,'a','0.6707217010396734'),(859427,2,3,4,'a','0.5821000901815482'),(876971,2,3,4,'a','1.023406957297965'),(927605,2,3,4,'a','0.2058319302330091'),(731764,2,3,4,'a','0.7001446119510985'),(555069,2,3,4,'a','1.450260808179398'),(960444,2,3,4,'a','1.253654401054284'),(252805,2,3,4,'a','0.7670868996224244'),(159302,2,3,4,'a','1.291762036543118'),(751498,2,3,4,'a','1.6396971509230296'),(844748,2,3,4,'a','1.5283880918551125'),(286726,2,3,4,'a','0.28209994014548134'),(845071,2,3,4,'a','1.604406589273742'),(475805,2,3,4,'a','1.9448262880973763'),(434652,2,3,4,'a','0.5120407235920809'),(976146,2,3,4,'a','0.22533770857875954'),(634906,2,3,4,'a','1.6730505038733132'),(277907,2,3,4,'a','1.759920749589727'),(566080,2,3,4,'a','0.3810402605506035'),(254360,2,3,4,'a','1.4004787573595334'),(738117,2,3,4,'a','1.1797345980813119'),(734985,2,3,4,'a','1.8106491005147334'),(321667,2,3,4,'a','1.7847220802537371'),(496804,2,3,4,'a','1.6138781733940153'),(544282,2,3,4,'a','0.6011871254082649'),(870121,2,3,4,'a','0.8976523754165064'),(633767,2,3,4,'a','1.6447115574448476'),(210478,2,3,4,'a','1.1706487659091585'),(295187,2,3,4,'a','1.4399230884759902'),(714247,2,3,4,'a','0.8227003410669979'),(914010,2,3,4,'a','0.6719992371946566'),(937968,2,3,4,'a','1.3636829903011052'),(595303,2,3,4,'a','1.8619842341746988'),(869051,2,3,4,'a','1.1045540898149406'),(154233,2,3,4,'a','0.22867015211719102'),(108976,2,3,4,'a','0.40374663509777436'),(682440,2,3,4,'a','1.6131552510086031'),(985570,2,3,4,'a','1.0162306064890982'),(583868,2,3,4,'a','0.7899848174210496'),(223359,2,3,4,'a','1.8636366258036687'),(989014,2,3,4,'a','0.2992330866858671'),(781040,2,3,4,'a','0.9126995680040676'),(938629,2,3,4,'a','0.6481945651100898'),(804598,2,3,4,'a','0.10140117826070746'),(839708,2,3,4,'a','0.09287185416824358'),(713057,2,3,4,'a','0.8519551780558705'),(990717,2,3,4,'a','1.3513022487529573'),(406105,2,3,4,'a','0.0071466770089666145'),(799551,2,3,4,'a','1.9740671775993586'),(536516,2,3,4,'a','1.4429568345127226'),(997845,2,3,4,'a','1.6495758105531109'),(130406,2,3,4,'a','0.35532944310021536'),(497107,2,3,4,'a','1.9050776771317028'),(271374,2,3,4,'a','0.9985114308060253'),(682155,2,3,4,'a','1.8260186443347657'),(518581,2,3,4,'a','1.7077519183119312'),(713637,2,3,4,'a','0.013119202119409295'),(891886,2,3,4,'a','0.8794981286670064'),(523089,2,3,4,'a','0.5923921788040476'),(911715,2,3,4,'a','1.339968792479456'),(614778,2,3,4,'a','0.12787800107884967'),(475360,2,3,4,'a','0.3699634413886475'),(498829,2,3,4,'a','1.878403929880265'),(199522,2,3,4,'a','0.36000424843281903'),(301446,2,3,4,'a','1.9344459045067857'),(931777,2,3,4,'a','1.514434502939167'),(990755,2,3,4,'a','1.364242968488711'),(438344,2,3,4,'a','0.2907064280386143'),(411736,2,3,4,'a','1.2452359695408828'),(877883,2,3,4,'a','1.0431261221348551'),(974165,2,3,4,'a','0.6122731478999118'),(608187,2,3,4,'a','0.2450552640902393'),(788076,2,3,4,'a','1.14559223982095'),(499753,2,3,4,'a','1.5607557329915052'),(402630,2,3,4,'a','1.3440293216556585'),(152184,2,3,4,'a','1.4897560677395725'),(267837,2,3,4,'a','0.20910194535656082'),(719244,2,3,4,'a','0.5651329053241134'),(255101,2,3,4,'a','0.8556082461547183'),(373719,2,3,4,'a','1.1703610822282369'),(804747,2,3,4,'a','0.5363869001496461'),(926726,2,3,4,'a','1.6581019159947539'),(365076,2,3,4,'a','0.6764548557591205'),(595909,2,3,4,'a','1.929725568676112'),(36587,2,3,4,'a','0.5766926562196507'),(331971,2,3,4,'a','1.5896309032939662'),(978165,2,3,4,'a','1.012753809814112'),(597391,2,3,4,'a','0.9356448100280434'),(546940,2,3,4,'a','0.6624676554114256'),(15349,2,3,4,'a','0.16608271577030673'),(369161,2,3,4,'a','1.1933632541386068'),(875925,2,3,4,'a','1.1791579399063792'),(320121,2,3,4,'a','1.6637321241793521'),(198969,2,3,4,'a','0.9984894534558891'),(899318,2,3,4,'a','1.9977110512533327'),(296324,2,3,4,'a','0.9701054459159313'),(536292,2,3,4,'a','0.4526045643283097'),(522635,2,3,4,'a','1.8685382622001119'),(103440,2,3,4,'a','1.4287864560529464'),(261646,2,3,4,'a','0.3300988584105846'),(40310,2,3,4,'a','1.4128007585302003'),(411073,2,3,4,'a','1.8723252395841528'),(447595,2,3,4,'a','0.8589732785327111'),(804649,2,3,4,'a','1.4695656890697457'),(259969,2,3,4,'a','0.19098913873637946'),(697567,2,3,4,'a','0.40270298756910766'),(914056,2,3,4,'a','1.9324550683912403'),(88969,2,3,4,'a','1.092319964517206'),(463894,2,3,4,'a','1.36198426351136'),(13277,2,3,4,'a','0.046821879266595354'),(77222,2,3,4,'a','0.6317578876686821'),(347728,2,3,4,'a','1.5820035055112127'),(911826,2,3,4,'a','0.37224616890051043'),(195138,2,3,4,'a','0.8346462127143948'),(501200,2,3,4,'a','0.5080630746745161'),(766557,2,3,4,'a','0.1413830575918621'),(53786,2,3,4,'a','0.11371097538034522'),(122919,2,3,4,'a','0.8880615671054102'),(851396,2,3,4,'a','1.849771549785297'),(70242,2,3,4,'a','1.15310589517756'),(672039,2,3,4,'a','1.261068613511574'),(136556,2,3,4,'a','1.5823511533274792'),(546211,2,3,4,'a','0.7150549448235473'),(149005,2,3,4,'a','1.3448841249056944'),(915196,2,3,4,'a','1.1173044230018783'),(47674,2,3,4,'a','1.124827466090049'),(669046,2,3,4,'a','1.3159826018996374'),(282817,2,3,4,'a','0.8802227944882799'),(352106,2,3,4,'a','0.8803902313861905'),(144658,2,3,4,'a','0.8054097991487102'),(579550,2,3,4,'a','1.3792746545553902'),(709535,2,3,4,'a','0.9575307974196327'),(265221,2,3,4,'a','1.7796138131801167'),(653373,2,3,4,'a','1.1948858268511349'),(27096,2,3,4,'a','0.6863069447561232'),(634478,2,3,4,'a','0.28586006191173574'),(811216,2,3,4,'a','1.2545792267234803'),(702800,2,3,4,'a','1.264265303746113'),(52262,2,3,4,'a','0.7298282838704327'),(667784,2,3,4,'a','0.4883502130288167'),(217525,2,3,4,'a','0.7102000459192321'),(122925,2,3,4,'a','1.0986495084116696'),(377849,2,3,4,'a','0.4825407997542441'),(72805,2,3,4,'a','1.2804301728312206'),(982660,2,3,4,'a','1.9853066392106067'),(15287,2,3,4,'a','0.1969551427261486'),(446525,2,3,4,'a','1.874388512106974'),(346395,2,3,4,'a','1.8407859987027813'),(562780,2,3,4,'a','0.10543791959568664'),(575256,2,3,4,'a','1.4362470371986247'),(864849,2,3,4,'a','0.3397529836182976'),(254834,2,3,4,'a','1.5290839071656428'),(58207,2,3,4,'a','1.9948179367881436'),(812424,2,3,4,'a','0.13978618768955225'),(912194,2,3,4,'a','0.7025766882138109'),(19861,2,3,4,'a','0.09088194006204786'),(167621,2,3,4,'a','1.4035672204602205'),(6054,2,3,4,'a','1.8498402813913675'),(606438,2,3,4,'a','0.5148609154996098'),(467838,2,3,4,'a','1.1337954151758882'),(430975,2,3,4,'a','0.9083652020509962'),(977988,2,3,4,'a','1.0547806555915444'),(702989,2,3,4,'a','1.8655461909859872'),(554899,2,3,4,'a','1.9523522816154661'),(216183,2,3,4,'a','0.30477818502558224'),(113395,2,3,4,'a','0.21961553415247756'),(208854,2,3,4,'a','1.4296920368724428'),(947669,2,3,4,'a','1.187609446409726'),(126018,2,3,4,'a','1.6973517031384182'),(865325,2,3,4,'a','1.561197895148022'),(307019,2,3,4,'a','0.38659523277226393'),(45431,2,3,4,'a','1.2945289195464262'),(100028,2,3,4,'a','1.1166929296373325'),(491648,2,3,4,'a','1.5664059403970894'),(441069,2,3,4,'a','1.711477054014315'),(955484,2,3,4,'a','0.4204111159037902'),(184575,2,3,4,'a','0.5845182692487895'),(907570,2,3,4,'a','1.322147625798497'),(582658,2,3,4,'a','1.860141020137948'),(902377,2,3,4,'a','1.4433513036401489'),(901246,2,3,4,'a','0.6824061411269067'),(2277,2,3,4,'a','1.9755555018554958'),(932057,2,3,4,'a','1.3938998183178715'),(688580,2,3,4,'a','0.7040975845457033'),(694505,2,3,4,'a','0.8327562947131286'),(998376,2,3,4,'a','1.485491891843725'),(718602,2,3,4,'a','0.7295398458182252'),(668045,2,3,4,'a','0.49183059715836364'),(225442,2,3,4,'a','0.7789232868504927'),(270984,2,3,4,'a','0.373067628939699'),(119718,2,3,4,'a','0.0779759214054569'),(835786,2,3,4,'a','0.12393439945199937'),(802477,2,3,4,'a','1.6529708501444857'),(724995,2,3,4,'a','0.2910371108828458'),(552608,2,3,4,'a','0.652968008679308'),(974596,2,3,4,'a','1.7870587201668497'),(543858,2,3,4,'a','0.07740415826198101'),(561936,2,3,4,'a','1.3871510786816021'),(782069,2,3,4,'a','1.6592336237982228'),(801878,2,3,4,'a','1.0410796972374243'),(197065,2,3,4,'a','0.8474121865326653'),(527335,2,3,4,'a','0.7311148221894306'),(245782,2,3,4,'a','0.26447249601080314'),(923836,2,3,4,'a','0.44494457770599477'),(340853,2,3,4,'a','0.07369506552228244'),(161679,2,3,4,'a','1.3957055987750233'),(4227,2,3,4,'a','1.855151340230509'),(625198,2,3,4,'a','0.6865256286100723'),(840720,2,3,4,'a','0.34762543472240254'),(346903,2,3,4,'a','0.4261531554406073'),(24674,2,3,4,'a','0.9682815940755248'),(346682,2,3,4,'a','0.5619724416751158'),(364886,2,3,4,'a','1.9629425983530866'),(812699,2,3,4,'a','0.2381579468382131'),(157299,2,3,4,'a','0.858518157953879'),(674398,2,3,4,'a','0.16842403278539333'),(397867,2,3,4,'a','1.473393754543172'),(489885,2,3,4,'a','0.47866559445733725'),(727010,2,3,4,'a','1.83410305700647'),(404228,2,3,4,'a','0.5399682862125041'),(137238,2,3,4,'a','1.7524704781849594'),(969464,2,3,4,'a','0.43722506280730017'),(184672,2,3,4,'a','0.5350424428796791'),(783591,2,3,4,'a','0.23078140265381095'),(226181,2,3,4,'a','1.5694652177109059'),(245120,2,3,4,'a','1.7428070658285237'),(621657,2,3,4,'a','0.9881478445494062'),(605399,2,3,4,'a','1.0895460071876142'),(907668,2,3,4,'a','1.8080440888256246'),(797106,2,3,4,'a','0.5469241762039477'),(975994,2,3,4,'a','0.11916310164999505'),(369927,2,3,4,'a','1.3417818987199868'),(244673,2,3,4,'a','0.4213887140354036'),(319452,2,3,4,'a','1.930349031398398'),(867518,2,3,4,'a','0.8841337234565371'),(607780,2,3,4,'a','1.4254000162942335'),(740160,2,3,4,'a','1.1253973516872129'),(593014,2,3,4,'a','0.5539500886145514'),(605833,2,3,4,'a','0.39647607169717186'),(173692,2,3,4,'a','0.547495117920912'),(847661,2,3,4,'a','0.8341231484283909'),(542325,2,3,4,'a','0.9208843120568286'),(675235,2,3,4,'a','1.989694146429835'),(948531,2,3,4,'a','1.5162305268610181'),(944982,2,3,4,'a','0.9011332456964378'),(417886,2,3,4,'a','1.475457479688765'),(434987,2,3,4,'a','1.9234934560242047'),(503774,2,3,4,'a','1.2672589004666162'),(656826,2,3,4,'a','0.7664785578534739'),(945720,2,3,4,'a','1.1577630482220678'),(57248,2,3,4,'a','1.0991914487436334'),(576234,2,3,4,'a','0.4647706453360344'),(433223,2,3,4,'a','0.9379197758975623'),(45130,2,3,4,'a','1.6375404872349841'),(958461,2,3,4,'a','0.6719918275922461'),(804596,2,3,4,'a','0.02998089234342882'),(661165,2,3,4,'a','0.5217110594005427'),(320782,2,3,4,'a','1.6426826507250616'),(144362,2,3,4,'a','0.5155713823768975'),(855843,2,3,4,'a','1.0117129618411074'),(961754,2,3,4,'a','0.5824043867945712'),(570748,2,3,4,'a','1.9602672457324968'),(188424,2,3,4,'a','0.0034387409719068007'),(443325,2,3,4,'a','0.42293142007936857'),(727354,2,3,4,'a','0.0047488659664512295'),(829809,2,3,4,'a','0.28384622585386615'),(220188,2,3,4,'a','1.350339050731006'),(715284,2,3,4,'a','1.1018264844099306'),(608713,2,3,4,'a','0.7816523004152368'),(127991,2,3,4,'a','0.934955901405733'),(953416,2,3,4,'a','0.7292919761774056'),(962982,2,3,4,'a','1.4419449879247184'),(715916,2,3,4,'a','0.8333275381786074'),(935570,2,3,4,'a','0.8557188872766857'),(332587,2,3,4,'a','0.7587115678551715'),(899018,2,3,4,'a','0.7140503569636963'),(88070,2,3,4,'a','0.7385532248192962'),(582172,2,3,4,'a','1.606058612098972'),(268631,2,3,4,'a','1.8681351597124107'),(864445,2,3,4,'a','1.040041078850665'),(6769,2,3,4,'a','0.9475646139565526'),(348605,2,3,4,'a','0.6433585087241218'),(562581,2,3,4,'a','1.6957306933549519'),(551585,2,3,4,'a','0.4286571484325986'),(416888,2,3,4,'a','0.8829101332304162'),(956611,2,3,4,'a','0.9173787486901309'),(423614,2,3,4,'a','1.4840055792443507'),(439171,2,3,4,'a','1.9396978206370936'),(531730,2,3,4,'a','1.4982089321112344'),(150332,2,3,4,'a','1.008689877585219'),(70730,2,3,4,'a','1.6812278383236638'),(990880,2,3,4,'a','0.8651205830882495'),(190160,2,3,4,'a','1.3062410869693766'),(695122,2,3,4,'a','1.03249595782952'),(495874,2,3,4,'a','1.8612539692421015'),(165512,2,3,4,'a','0.07136234833985786'),(681869,2,3,4,'a','0.6045994484839956'),(465893,2,3,4,'a','0.8451289393428051'),(715144,2,3,4,'a','0.616055364362947'),(394706,2,3,4,'a','0.09889202201635765'),(63113,2,3,4,'a','0.3344506922498818'),(646789,2,3,4,'a','1.4645355078061442'),(720973,2,3,4,'a','0.8161189712734138'),(877380,2,3,4,'a','0.32544143714517487'),(181464,2,3,4,'a','0.8383171175032083'),(551400,2,3,4,'a','0.999052851459992'),(843431,2,3,4,'a','1.4371506734165835'),(62584,2,3,4,'a','0.3143876849807684'),(598217,2,3,4,'a','1.0390105070909583'),(802874,2,3,4,'a','0.9117110231069019'),(870655,2,3,4,'a','1.9714154284181218'),(316574,2,3,4,'a','1.2514944851878047'),(179014,2,3,4,'a','0.035656916010805326'),(552100,2,3,4,'a','1.414031590720668'),(878778,2,3,4,'a','0.5456885328010549'),(727886,2,3,4,'a','1.6417983599396406'),(920837,2,3,4,'a','0.2829725968492893'),(944921,2,3,4,'a','0.600294653885341'),(665973,2,3,4,'a','0.8588456053834833'),(149195,2,3,4,'a','0.9154151705237247'),(840952,2,3,4,'a','1.6632783447050288'),(635339,2,3,4,'a','1.3635525678876346'),(502865,2,3,4,'a','0.9379942407254076'),(836390,2,3,4,'a','1.549918015999643'),(365625,2,3,4,'a','1.0064946906701613'),(419362,2,3,4,'a','1.1741375319418847'),(677257,2,3,4,'a','1.250161444069968'),(93631,2,3,4,'a','1.1858302030580399'),(683681,2,3,4,'a','1.279321414678657'),(147260,2,3,4,'a','1.634635604577731'),(644809,2,3,4,'a','1.5441843080745863'),(926033,2,3,4,'a','0.6277819654231723'),(791354,2,3,4,'a','0.03019912357460626'),(701434,2,3,4,'a','0.923746212314578'),(205063,2,3,4,'a','1.279386475011135'),(583279,2,3,4,'a','1.9946276675859724'),(236733,2,3,4,'a','0.3834479622388705'),(248421,2,3,4,'a','1.3338628274703983'),(10480816,2,3,4,'a','xxa0.8368445176974354'),(10649665,2,3,4,'a','xxa1.9861146043856763'),(10016292,2,3,4,'a','xxa0.20457737725654374'),(10462567,2,3,4,'a','xxa0.01193928160885282'),(10642147,2,3,4,'a','xxa0.3856505084649199'),(10037686,2,3,4,'a','xxa1.219906679559412'),(10936709,2,3,4,'a','xxa1.7073728886501611'),(10458304,2,3,4,'a','xxa1.460925321523962'),(10277400,2,3,4,'a','xxa0.3912256643094361'),(10145864,2,3,4,'a','xxa0.28496028136923934'),(10274810,2,3,4,'a','xxa1.8932149204362323'),(10908609,2,3,4,'a','xxa1.4064415091708689'),(10790278,2,3,4,'a','xxa1.6834549435260286'),(10837804,2,3,4,'a','xxa1.327670897662333'),(10805767,2,3,4,'a','xxa0.07465311705568164'),(10769333,2,3,4,'a','xxa1.469371698302563'),(10365430,2,3,4,'a','xxa1.246184767453172'),(10019172,2,3,4,'a','xxa0.45316585009281135'),(10075399,2,3,4,'a','xxa1.3944887978904776'),(10260026,2,3,4,'a','xxa0.4167950622893824'),(10261909,2,3,4,'a','xxa1.3687075556895767'),(10636041,2,3,4,'a','xxa0.25428855070312373'),(10727598,2,3,4,'a','xxa0.5131150153587712'),(10099993,2,3,4,'a','xxa1.4605888886941494'),(10351492,2,3,4,'a','xxa1.1331543001655064'),(10778410,2,3,4,'a','xxa0.3846322581028866'),(10626352,2,3,4,'a','xxa1.1096247556708985'),(10895005,2,3,4,'a','xxa1.6211805880266992'),(10367935,2,3,4,'a','xxa0.8158089246748098'),(10935717,2,3,4,'a','xxa0.9097448297867038'),(10467211,2,3,4,'a','xxa1.9428719970796928'),(10455548,2,3,4,'a','xxa0.7268650296394388'),(10450518,2,3,4,'a','xxa0.32458814263771113'),(10459915,2,3,4,'a','xxa1.625389996567173'),(10683729,2,3,4,'a','xxa1.9611172042424951'),(10851607,2,3,4,'a','xxa0.63271746470846'),(10026973,2,3,4,'a','xxa0.3715775444838941'),(10848025,2,3,4,'a','xxa1.365516716023457'),(10869717,2,3,4,'a','xxa0.6006202572962458'),(10892400,2,3,4,'a','xxa1.122134997623167'),(10128139,2,3,4,'a','xxa1.9149831923795708'),(10403042,2,3,4,'a','xxa0.28546622980895103'),(10504541,2,3,4,'a','xxa0.18901018815954235'),(10958903,2,3,4,'a','xxa1.0219980599563552'),(10678287,2,3,4,'a','xxa1.7168719989404753'),(10257320,2,3,4,'a','xxa1.4225859934674445'),(10784504,2,3,4,'a','xxa1.5772864386227787'),(10589703,2,3,4,'a','xxa1.165168945831404'),(10143814,2,3,4,'a','xxa1.942632576397278'),(10425140,2,3,4,'a','xxa0.42349899227125476'),(10783329,2,3,4,'a','xxa0.5627882187839488'),(10056985,2,3,4,'a','xxa0.8814857796593437'),(10032759,2,3,4,'a','xxa1.6831363976776008'),(10109563,2,3,4,'a','xxa0.04622009214593106'),(10786861,2,3,4,'a','xxa1.7299547248798932'),(10964302,2,3,4,'a','xxa0.45315949661020144'),(10239992,2,3,4,'a','xxa1.040438464880398'),(10881121,2,3,4,'a','xxa1.6898959164413585'),(10581376,2,3,4,'a','xxa0.7440767816678404'),(10116063,2,3,4,'a','xxa0.9283961569223517'),(10972802,2,3,4,'a','xxa0.9428364997197283'),(10438684,2,3,4,'a','xxa1.5583286355792811'),(10579770,2,3,4,'a','xxa1.1227161317343974'),(10067480,2,3,4,'a','xxa1.30664933594563'),(10064184,2,3,4,'a','xxa0.7218927505630001'),(10612180,2,3,4,'a','xxa1.9561187903919433'),(10053758,2,3,4,'a','xxa0.6692228155855302'),(10511783,2,3,4,'a','xxa1.1101649358031944'),(10240062,2,3,4,'a','xxa1.070128993196533'),(10955135,2,3,4,'a','xxa0.34096700171098765'),(10987011,2,3,4,'a','xxa0.8472126469455777'),(10156998,2,3,4,'a','xxa1.0283378297726975'),(10099852,2,3,4,'a','xxa1.913505496376665'),(10484208,2,3,4,'a','xxa1.1015645164079633'),(10301287,2,3,4,'a','xxa1.7081768975669303'),(10366581,2,3,4,'a','xxa0.5412809593056152'),(10253459,2,3,4,'a','xxa0.9107467019099246'),(10516490,2,3,4,'a','xxa0.4326592557436407'),(10532178,2,3,4,'a','xxa0.02380609514546217'),(10462980,2,3,4,'a','xxa0.5583830592766247'),(10007017,2,3,4,'a','xxa0.39502382315231843'),(10966508,2,3,4,'a','xxa0.4800056596100383'),(10300491,2,3,4,'a','xxa1.5648936774254718'),(10010761,2,3,4,'a','xxa1.4129294877992287'),(10500041,2,3,4,'a','xxa0.7616177245617078'),(10403923,2,3,4,'a','xxa1.7543719296850002'),(10174162,2,3,4,'a','xxa0.47850769616524474'),(10673782,2,3,4,'a','xxa1.3022994131802577'),(10234402,2,3,4,'a','xxa0.43712013814330114'),(10389595,2,3,4,'a','xxa0.5845884593060132'),(10292687,2,3,4,'a','xxa1.1731000367301516'),(10054691,2,3,4,'a','xxa1.0276051173243719'),(10404941,2,3,4,'a','xxa0.9665974517973116'),(10201670,2,3,4,'a','xxa1.1169060814351832'),(10187256,2,3,4,'a','xxa0.5218390342982849'),(10742830,2,3,4,'a','xxa1.8627874719563755'),(10428477,2,3,4,'a','xxa0.6964112116921798'),(10455596,2,3,4,'a','xxa0.4667263277496456'),(10800028,2,3,4,'a','xxa0.6000998752192593'),(10100166,2,3,4,'a','xxa1.2013612326247256'),(10702905,2,3,4,'a','xxa1.4249650961020637'),(10453698,2,3,4,'a','xxa0.26208628552228797'),(10294121,2,3,4,'a','xxa0.1549541132105087'),(10505021,2,3,4,'a','xxa0.5853521345046835'),(10948316,2,3,4,'a','xxa1.727104089900017'),(10472812,2,3,4,'a','xxa1.5468090507637793'),(10448586,2,3,4,'a','xxa1.845435708617266'),(10267830,2,3,4,'a','xxa1.1419975600596495'),(10051503,2,3,4,'a','xxa1.0890328167835557'),(10568074,2,3,4,'a','xxa0.41364603155632135'),(10329892,2,3,4,'a','xxa0.05797980172371473'),(10155274,2,3,4,'a','xxa1.3788025019492978'),(10981184,2,3,4,'a','xxa1.675429874728834'),(10245024,2,3,4,'a','xxa1.423945655509779'),(10824794,2,3,4,'a','xxa1.976099135331902'),(10465867,2,3,4,'a','xxa0.7303732249237348'),(10428332,2,3,4,'a','xxa0.0921996087694537'),(10945503,2,3,4,'a','xxa1.1784340955116173'),(10109575,2,3,4,'a','xxa1.560451889001254'),(10572404,2,3,4,'a','xxa1.0426795846248786'),(10889488,2,3,4,'a','xxa1.7668456600670197'),(10748649,2,3,4,'a','xxa0.18595183285507544'),(10218933,2,3,4,'a','xxa1.6314744368488663'),(10421887,2,3,4,'a','xxa1.3244483576383892'),(10045459,2,3,4,'a','xxa0.48124941669520865'),(10066745,2,3,4,'a','xxa1.223704251669072'),(10859025,2,3,4,'a','xxa0.9191365846610969'),(10720767,2,3,4,'a','xxa0.4502573967448039'),(10963343,2,3,4,'a','xxa0.28266182940701196'),(10816624,2,3,4,'a','xxa1.3182587412356015'),(10845774,2,3,4,'a','xxa0.5029601720189305'),(10720080,2,3,4,'a','xxa1.6919151504448755'),(10069549,2,3,4,'a','xxa1.6197468634878722'),(10840720,2,3,4,'a','xxa1.5479546054713005'),(10347728,2,3,4,'a','xxa0.8334162857694704'),(10040357,2,3,4,'a','xxa1.903318533583841'),(10637226,2,3,4,'a','xxa0.6623061193733533'),(10744087,2,3,4,'a','xxa1.453950019044755'),(10402615,2,3,4,'a','xxa1.664295840695776'),(10952896,2,3,4,'a','xxa0.5360713550225564'),(10481491,2,3,4,'a','xxa1.2066937323721998'),(10572262,2,3,4,'a','xxa0.102539273074399'),(10539562,2,3,4,'a','xxa1.088003150269392'),(10101322,2,3,4,'a','xxa1.7492069506544685'),(10069053,2,3,4,'a','xxa1.4429042632159816'),(10400103,2,3,4,'a','xxa1.6723189984190454'),(10980488,2,3,4,'a','xxa0.7879231542236387'),(10028344,2,3,4,'a','xxa1.9196694976833364'),(10714142,2,3,4,'a','xxa1.3824126211762546'),(10313605,2,3,4,'a','xxa0.9888148764044185'),(10531221,2,3,4,'a','xxa0.34576961430327'),(10270760,2,3,4,'a','xxa1.6702893373279732'),(10363444,2,3,4,'a','xxa0.6235714337076725'),(10468597,2,3,4,'a','xxa0.815253757699629'),(10632344,2,3,4,'a','xxa1.877679046129509'),(10797166,2,3,4,'a','xxa0.3386186103696177'),(10455050,2,3,4,'a','xxa1.5346427350627656'),(10471457,2,3,4,'a','xxa0.11064502607159785'),(10862241,2,3,4,'a','xxa0.29047143300107814'),(10139457,2,3,4,'a','xxa0.5231525455817139'),(10889511,2,3,4,'a','xxa1.3256560222484692'),(10645606,2,3,4,'a','xxa0.47909206569110235'),(10260912,2,3,4,'a','xxa1.1718456011003309'),(10146878,2,3,4,'a','xxa1.953238382891974'),(10442463,2,3,4,'a','xxa0.5649194182501356'),(10084908,2,3,4,'a','xxa1.1543240539304205'),(10631085,2,3,4,'a','xxa0.8478828136323792'),(10226451,2,3,4,'a','xxa1.7208575268470288'),(10622792,2,3,4,'a','xxa1.0653482406077424'),(10794994,2,3,4,'a','xxa0.7538993831294583'),(10499765,2,3,4,'a','xxa0.7359546671956355'),(10340591,2,3,4,'a','xxa1.1980447817575604'),(10973339,2,3,4,'a','xxa0.13925940183853674'),(10428130,2,3,4,'a','xxa1.8635261523197648'),(10374424,2,3,4,'a','xxa0.15366283073431145'),(10260885,2,3,4,'a','xxa0.14785896627964357'),(10586993,2,3,4,'a','xxa1.4263554359100343'),(10804909,2,3,4,'a','xxa1.7700231389794714'),(10010331,2,3,4,'a','xxa0.7932412706252572'),(10952110,2,3,4,'a','xxa1.1413764442702536'),(10997111,2,3,4,'a','xxa0.5469815587131135'),(10376121,2,3,4,'a','xxa0.12026217032248357'),(10172293,2,3,4,'a','xxa1.3621477609147763'),(10888489,2,3,4,'a','xxa0.7984484776840065'),(10330654,2,3,4,'a','xxa0.9111917809687479'),(10286018,2,3,4,'a','xxa0.12660809245575974'),(10458465,2,3,4,'a','xxa0.20482828114631427'),(10136675,2,3,4,'a','xxa0.7522650628837432'),(10470638,2,3,4,'a','xxa0.44958221767990125'),(10712042,2,3,4,'a','xxa1.7716771231700452'),(10293066,2,3,4,'a','xxa1.6156259343173596'),(10159868,2,3,4,'a','xxa0.7517991967050351'),(10399895,2,3,4,'a','xxa1.7435518817450404'),(10159195,2,3,4,'a','xxa0.3612935360160596'),(10425649,2,3,4,'a','xxa1.1726108018053796'),(10654580,2,3,4,'a','xxa1.027963963363286'),(10606171,2,3,4,'a','xxa0.9778186408596287'),(10626033,2,3,4,'a','xxa1.3268790592689803'),(10439097,2,3,4,'a','xxa0.4103349115795781'),(10708546,2,3,4,'a','xxa1.8544523397967707'),(10510494,2,3,4,'a','xxa1.54158080512805'),(10322471,2,3,4,'a','xxa0.5999647924676209'),(10532500,2,3,4,'a','xxa1.5251043639379593'),(10215261,2,3,4,'a','xxa1.57730033581825'),(10297467,2,3,4,'a','xxa0.24276765831072597'),(10714519,2,3,4,'a','xxa0.4168830387451528'),(10898652,2,3,4,'a','xxa1.7358693645669794'),(10643718,2,3,4,'a','xxa1.229571131271842'),(10142774,2,3,4,'a','xxa1.7390277662677949'),(10919247,2,3,4,'a','xxa1.9753879308471343'),(10180729,2,3,4,'a','xxa1.881121342891009'),(10160618,2,3,4,'a','xxa1.96281469051057'),(10425183,2,3,4,'a','xxa0.3633875868873555'),(10632920,2,3,4,'a','xxa1.2390324894702365'),(10198823,2,3,4,'a','xxa0.27112957115390296'),(10081356,2,3,4,'a','xxa0.00016999989763833573'),(10756357,2,3,4,'a','xxa1.5630644108756113'),(10638589,2,3,4,'a','xxa1.6966943607634812'),(10325970,2,3,4,'a','xxa0.16961370983125057'),(10446125,2,3,4,'a','xxa1.9524117931280396'),(10542654,2,3,4,'a','xxa1.569301234157105'),(10295292,2,3,4,'a','xxa0.24501786403825307'),(10726668,2,3,4,'a','xxa0.5316288625184715'),(10149067,2,3,4,'a','xxa1.8957881647122914'),(10292268,2,3,4,'a','xxa1.235315920072902'),(10211486,2,3,4,'a','xxa0.40890955218012404'),(10387817,2,3,4,'a','xxa0.6514387639718491'),(10465147,2,3,4,'a','xxa0.6971510990496269'),(10347438,2,3,4,'a','xxa1.3829225594018797'),(10414994,2,3,4,'a','xxa0.0011692903946799137'),(10757942,2,3,4,'a','xxa1.5759140360876118'),(10665959,2,3,4,'a','xxa1.9318436253181133'),(10831733,2,3,4,'a','xxa0.5218031094612583'),(10809308,2,3,4,'a','xxa0.5276670051064967'),(10891245,2,3,4,'a','xxa1.3294456650777213'),(10649880,2,3,4,'a','xxa0.5104651437238465'),(10326522,2,3,4,'a','xxa1.7338272982592018'),(10355001,2,3,4,'a','xxa0.3485329266158239'),(10806328,2,3,4,'a','xxa1.0176804745734487'),(10125217,2,3,4,'a','xxa0.19913323055872062'),(10122181,2,3,4,'a','xxa0.6244076626602632'),(10194477,2,3,4,'a','xxa0.07154706872212428'),(10595437,2,3,4,'a','xxa1.7397258949836025'),(10563005,2,3,4,'a','xxa0.41086893939494057'),(10338158,2,3,4,'a','xxa0.14897734685705727'),(10357968,2,3,4,'a','xxa1.132749688935233'),(10757970,2,3,4,'a','xxa0.18144852126152117'),(10179712,2,3,4,'a','xxa1.252775256757415'),(10592802,2,3,4,'a','xxa0.16969374024280695'),(10645829,2,3,4,'a','xxa1.9492043070022056'),(10935525,2,3,4,'a','xxa1.5076387240622553'),(10962521,2,3,4,'a','xxa1.1022975473686099'),(10868180,2,3,4,'a','xxa1.374905436648899'),(10832724,2,3,4,'a','xxa0.20252592321720525'),(10008142,2,3,4,'a','xxa1.4738441644924172'),(10660184,2,3,4,'a','xxa0.18030636960669083'),(10470214,2,3,4,'a','xxa0.16122409530098'),(10992417,2,3,4,'a','xxa1.4405010374640124'),(10624001,2,3,4,'a','xxa1.918503407313026'),(10924257,2,3,4,'a','xxa1.4870559549769908'),(10944870,2,3,4,'a','xxa0.9875330319512011'),(10634222,2,3,4,'a','xxa1.3796245654855153'),(10546394,2,3,4,'a','xxa1.3250700098695884'),(10673492,2,3,4,'a','xxa0.7597096513581552'),(10878798,2,3,4,'a','xxa0.5088538401842618'),(10635740,2,3,4,'a','xxa0.8308366544831886'),(10169872,2,3,4,'a','xxa1.2062124267278356'),(10505914,2,3,4,'a','xxa1.440506770685787'),(10083524,2,3,4,'a','xxa0.5137173556850454'),(10033722,2,3,4,'a','xxa0.7960693750493875'),(10889007,2,3,4,'a','xxa0.5018624779785634'),(10587635,2,3,4,'a','xxa0.370763259353827'),(10164003,2,3,4,'a','xxa0.5277401791212523'),(10827342,2,3,4,'a','xxa0.6901946223249609'),(10243462,2,3,4,'a','xxa0.3640367652885958'),(10179706,2,3,4,'a','xxa0.7049474927642825'),(10223251,2,3,4,'a','xxa0.11767202812961491'),(10624426,2,3,4,'a','xxa1.8912416527897506'),(10854827,2,3,4,'a','xxa0.8745437030443397'),(10621879,2,3,4,'a','xxa1.595156035940308'),(10122254,2,3,4,'a','xxa0.4370719086724072'),(10725918,2,3,4,'a','xxa1.947962494518573'),(10692153,2,3,4,'a','xxa1.0776407710068308'),(10617644,2,3,4,'a','xxa0.9435127516682379'),(10505851,2,3,4,'a','xxa0.2279758958406522'),(10052386,2,3,4,'a','xxa1.839927809163861'),(10442663,2,3,4,'a','xxa0.9068458647530953'),(10939126,2,3,4,'a','xxa0.6707217010396734'),(10859427,2,3,4,'a','xxa0.5821000901815482'),(10876971,2,3,4,'a','xxa1.023406957297965'),(10927605,2,3,4,'a','xxa0.2058319302330091'),(10731764,2,3,4,'a','xxa0.7001446119510985'),(10555069,2,3,4,'a','xxa1.450260808179398'),(10960444,2,3,4,'a','xxa1.253654401054284'),(10252805,2,3,4,'a','xxa0.7670868996224244'),(10159302,2,3,4,'a','xxa1.291762036543118'),(10751498,2,3,4,'a','xxa1.6396971509230296'),(10844748,2,3,4,'a','xxa1.5283880918551125'),(10286726,2,3,4,'a','xxa0.28209994014548134'),(10845071,2,3,4,'a','xxa1.604406589273742'),(10475805,2,3,4,'a','xxa1.9448262880973763'),(10434652,2,3,4,'a','xxa0.5120407235920809'),(10976146,2,3,4,'a','xxa0.22533770857875954'),(10634906,2,3,4,'a','xxa1.6730505038733132'),(10277907,2,3,4,'a','xxa1.759920749589727'),(10566080,2,3,4,'a','xxa0.3810402605506035'),(10254360,2,3,4,'a','xxa1.4004787573595334'),(10738117,2,3,4,'a','xxa1.1797345980813119'),(10734985,2,3,4,'a','xxa1.8106491005147334'),(10321667,2,3,4,'a','xxa1.7847220802537371'),(10496804,2,3,4,'a','xxa1.6138781733940153'),(10544282,2,3,4,'a','xxa0.6011871254082649'),(10870121,2,3,4,'a','xxa0.8976523754165064'),(10633767,2,3,4,'a','xxa1.6447115574448476'),(10210478,2,3,4,'a','xxa1.1706487659091585'),(10295187,2,3,4,'a','xxa1.4399230884759902'),(10714247,2,3,4,'a','xxa0.8227003410669979'),(10914010,2,3,4,'a','xxa0.6719992371946566'),(10937968,2,3,4,'a','xxa1.3636829903011052'),(10595303,2,3,4,'a','xxa1.8619842341746988'),(10869051,2,3,4,'a','xxa1.1045540898149406'),(10154233,2,3,4,'a','xxa0.22867015211719102'),(10108976,2,3,4,'a','xxa0.40374663509777436'),(10682440,2,3,4,'a','xxa1.6131552510086031'),(10985570,2,3,4,'a','xxa1.0162306064890982'),(10583868,2,3,4,'a','xxa0.7899848174210496'),(10223359,2,3,4,'a','xxa1.8636366258036687'),(10989014,2,3,4,'a','xxa0.2992330866858671'),(10781040,2,3,4,'a','xxa0.9126995680040676'),(10938629,2,3,4,'a','xxa0.6481945651100898'),(10804598,2,3,4,'a','xxa0.10140117826070746'),(10839708,2,3,4,'a','xxa0.09287185416824358'),(10713057,2,3,4,'a','xxa0.8519551780558705'),(10990717,2,3,4,'a','xxa1.3513022487529573'),(10406105,2,3,4,'a','xxa0.0071466770089666145'),(10799551,2,3,4,'a','xxa1.9740671775993586'),(10536516,2,3,4,'a','xxa1.4429568345127226'),(10997845,2,3,4,'a','xxa1.6495758105531109'),(10130406,2,3,4,'a','xxa0.35532944310021536'),(10497107,2,3,4,'a','xxa1.9050776771317028'),(10271374,2,3,4,'a','xxa0.9985114308060253'),(10682155,2,3,4,'a','xxa1.8260186443347657'),(10518581,2,3,4,'a','xxa1.7077519183119312'),(10713637,2,3,4,'a','xxa0.013119202119409295'),(10891886,2,3,4,'a','xxa0.8794981286670064'),(10523089,2,3,4,'a','xxa0.5923921788040476'),(10911715,2,3,4,'a','xxa1.339968792479456'),(10614778,2,3,4,'a','xxa0.12787800107884967'),(10475360,2,3,4,'a','xxa0.3699634413886475'),(10498829,2,3,4,'a','xxa1.878403929880265'),(10199522,2,3,4,'a','xxa0.36000424843281903'),(10301446,2,3,4,'a','xxa1.9344459045067857'),(10931777,2,3,4,'a','xxa1.514434502939167'),(10990755,2,3,4,'a','xxa1.364242968488711'),(10438344,2,3,4,'a','xxa0.2907064280386143'),(10411736,2,3,4,'a','xxa1.2452359695408828'),(10877883,2,3,4,'a','xxa1.0431261221348551'),(10974165,2,3,4,'a','xxa0.6122731478999118'),(10608187,2,3,4,'a','xxa0.2450552640902393'),(10788076,2,3,4,'a','xxa1.14559223982095'),(10499753,2,3,4,'a','xxa1.5607557329915052'),(10402630,2,3,4,'a','xxa1.3440293216556585'),(10152184,2,3,4,'a','xxa1.4897560677395725'),(10267837,2,3,4,'a','xxa0.20910194535656082'),(10719244,2,3,4,'a','xxa0.5651329053241134'),(10255101,2,3,4,'a','xxa0.8556082461547183'),(10373719,2,3,4,'a','xxa1.1703610822282369'),(10804747,2,3,4,'a','xxa0.5363869001496461'),(10926726,2,3,4,'a','xxa1.6581019159947539'),(10365076,2,3,4,'a','xxa0.6764548557591205'),(10595909,2,3,4,'a','xxa1.929725568676112'),(10036587,2,3,4,'a','xxa0.5766926562196507'),(10331971,2,3,4,'a','xxa1.5896309032939662'),(10978165,2,3,4,'a','xxa1.012753809814112'),(10597391,2,3,4,'a','xxa0.9356448100280434'),(10546940,2,3,4,'a','xxa0.6624676554114256'),(10015349,2,3,4,'a','xxa0.16608271577030673'),(10369161,2,3,4,'a','xxa1.1933632541386068'),(10875925,2,3,4,'a','xxa1.1791579399063792'),(10320121,2,3,4,'a','xxa1.6637321241793521'),(10198969,2,3,4,'a','xxa0.9984894534558891'),(10899318,2,3,4,'a','xxa1.9977110512533327'),(10296324,2,3,4,'a','xxa0.9701054459159313'),(10536292,2,3,4,'a','xxa0.4526045643283097'),(10522635,2,3,4,'a','xxa1.8685382622001119'),(10103440,2,3,4,'a','xxa1.4287864560529464'),(10261646,2,3,4,'a','xxa0.3300988584105846'),(10040310,2,3,4,'a','xxa1.4128007585302003'),(10411073,2,3,4,'a','xxa1.8723252395841528'),(10447595,2,3,4,'a','xxa0.8589732785327111'),(10804649,2,3,4,'a','xxa1.4695656890697457'),(10259969,2,3,4,'a','xxa0.19098913873637946'),(10697567,2,3,4,'a','xxa0.40270298756910766'),(10914056,2,3,4,'a','xxa1.9324550683912403'),(10088969,2,3,4,'a','xxa1.092319964517206'),(10463894,2,3,4,'a','xxa1.36198426351136'),(10013277,2,3,4,'a','xxa0.046821879266595354'),(10077222,2,3,4,'a','xxa0.6317578876686821'),(10347728,2,3,4,'a','xxa1.5820035055112127'),(10911826,2,3,4,'a','xxa0.37224616890051043'),(10195138,2,3,4,'a','xxa0.8346462127143948'),(10501200,2,3,4,'a','xxa0.5080630746745161'),(10766557,2,3,4,'a','xxa0.1413830575918621'),(10053786,2,3,4,'a','xxa0.11371097538034522'),(10122919,2,3,4,'a','xxa0.8880615671054102'),(10851396,2,3,4,'a','xxa1.849771549785297'),(10070242,2,3,4,'a','xxa1.15310589517756'),(10672039,2,3,4,'a','xxa1.261068613511574'),(10136556,2,3,4,'a','xxa1.5823511533274792'),(10546211,2,3,4,'a','xxa0.7150549448235473'),(10149005,2,3,4,'a','xxa1.3448841249056944'),(10915196,2,3,4,'a','xxa1.1173044230018783'),(10047674,2,3,4,'a','xxa1.124827466090049'),(10669046,2,3,4,'a','xxa1.3159826018996374'),(10282817,2,3,4,'a','xxa0.8802227944882799'),(10352106,2,3,4,'a','xxa0.8803902313861905'),(10144658,2,3,4,'a','xxa0.8054097991487102'),(10579550,2,3,4,'a','xxa1.3792746545553902'),(10709535,2,3,4,'a','xxa0.9575307974196327'),(10265221,2,3,4,'a','xxa1.7796138131801167'),(10653373,2,3,4,'a','xxa1.1948858268511349'),(10027096,2,3,4,'a','xxa0.6863069447561232'),(10634478,2,3,4,'a','xxa0.28586006191173574'),(10811216,2,3,4,'a','xxa1.2545792267234803'),(10702800,2,3,4,'a','xxa1.264265303746113'),(10052262,2,3,4,'a','xxa0.7298282838704327'),(10667784,2,3,4,'a','xxa0.4883502130288167'),(10217525,2,3,4,'a','xxa0.7102000459192321'),(10122925,2,3,4,'a','xxa1.0986495084116696'),(10377849,2,3,4,'a','xxa0.4825407997542441'),(10072805,2,3,4,'a','xxa1.2804301728312206'),(10982660,2,3,4,'a','xxa1.9853066392106067'),(10015287,2,3,4,'a','xxa0.1969551427261486'),(10446525,2,3,4,'a','xxa1.874388512106974'),(10346395,2,3,4,'a','xxa1.8407859987027813'),(10562780,2,3,4,'a','xxa0.10543791959568664'),(10575256,2,3,4,'a','xxa1.4362470371986247'),(10864849,2,3,4,'a','xxa0.3397529836182976'),(10254834,2,3,4,'a','xxa1.5290839071656428'),(10058207,2,3,4,'a','xxa1.9948179367881436'),(10812424,2,3,4,'a','xxa0.13978618768955225'),(10912194,2,3,4,'a','xxa0.7025766882138109'),(10019861,2,3,4,'a','xxa0.09088194006204786'),(10167621,2,3,4,'a','xxa1.4035672204602205'),(10006054,2,3,4,'a','xxa1.8498402813913675'),(10606438,2,3,4,'a','xxa0.5148609154996098'),(10467838,2,3,4,'a','xxa1.1337954151758882'),(10430975,2,3,4,'a','xxa0.9083652020509962'),(10977988,2,3,4,'a','xxa1.0547806555915444'),(10702989,2,3,4,'a','xxa1.8655461909859872'),(10554899,2,3,4,'a','xxa1.9523522816154661'),(10216183,2,3,4,'a','xxa0.30477818502558224'),(10113395,2,3,4,'a','xxa0.21961553415247756'),(10208854,2,3,4,'a','xxa1.4296920368724428'),(10947669,2,3,4,'a','xxa1.187609446409726'),(10126018,2,3,4,'a','xxa1.6973517031384182'),(10865325,2,3,4,'a','xxa1.561197895148022'),(10307019,2,3,4,'a','xxa0.38659523277226393'),(10045431,2,3,4,'a','xxa1.2945289195464262'),(10100028,2,3,4,'a','xxa1.1166929296373325'),(10491648,2,3,4,'a','xxa1.5664059403970894'),(10441069,2,3,4,'a','xxa1.711477054014315'),(10955484,2,3,4,'a','xxa0.4204111159037902'),(10184575,2,3,4,'a','xxa0.5845182692487895'),(10907570,2,3,4,'a','xxa1.322147625798497'),(10582658,2,3,4,'a','xxa1.860141020137948'),(10902377,2,3,4,'a','xxa1.4433513036401489'),(10901246,2,3,4,'a','xxa0.6824061411269067'),(10002277,2,3,4,'a','xxa1.9755555018554958'),(10932057,2,3,4,'a','xxa1.3938998183178715'),(10688580,2,3,4,'a','xxa0.7040975845457033'),(10694505,2,3,4,'a','xxa0.8327562947131286'),(10998376,2,3,4,'a','xxa1.485491891843725'),(10718602,2,3,4,'a','xxa0.7295398458182252'),(10668045,2,3,4,'a','xxa0.49183059715836364'),(10225442,2,3,4,'a','xxa0.7789232868504927'),(10270984,2,3,4,'a','xxa0.373067628939699'),(10119718,2,3,4,'a','xxa0.0779759214054569'),(10835786,2,3,4,'a','xxa0.12393439945199937'),(10802477,2,3,4,'a','xxa1.6529708501444857'),(10724995,2,3,4,'a','xxa0.2910371108828458'),(10552608,2,3,4,'a','xxa0.652968008679308'),(10974596,2,3,4,'a','xxa1.7870587201668497'),(10543858,2,3,4,'a','xxa0.07740415826198101'),(10561936,2,3,4,'a','xxa1.3871510786816021'),(10782069,2,3,4,'a','xxa1.6592336237982228'),(10801878,2,3,4,'a','xxa1.0410796972374243'),(10197065,2,3,4,'a','xxa0.8474121865326653'),(10527335,2,3,4,'a','xxa0.7311148221894306'),(10245782,2,3,4,'a','xxa0.26447249601080314'),(10923836,2,3,4,'a','xxa0.44494457770599477'),(10340853,2,3,4,'a','xxa0.07369506552228244'),(10161679,2,3,4,'a','xxa1.3957055987750233'),(10004227,2,3,4,'a','xxa1.855151340230509'),(10625198,2,3,4,'a','xxa0.6865256286100723'),(10840720,2,3,4,'a','xxa0.34762543472240254'),(10346903,2,3,4,'a','xxa0.4261531554406073'),(10024674,2,3,4,'a','xxa0.9682815940755248'),(10346682,2,3,4,'a','xxa0.5619724416751158'),(10364886,2,3,4,'a','xxa1.9629425983530866'),(10812699,2,3,4,'a','xxa0.2381579468382131'),(10157299,2,3,4,'a','xxa0.858518157953879'),(10674398,2,3,4,'a','xxa0.16842403278539333'),(10397867,2,3,4,'a','xxa1.473393754543172'),(10489885,2,3,4,'a','xxa0.47866559445733725'),(10727010,2,3,4,'a','xxa1.83410305700647'),(10404228,2,3,4,'a','xxa0.5399682862125041'),(10137238,2,3,4,'a','xxa1.7524704781849594'),(10969464,2,3,4,'a','xxa0.43722506280730017'),(10184672,2,3,4,'a','xxa0.5350424428796791'),(10783591,2,3,4,'a','xxa0.23078140265381095'),(10226181,2,3,4,'a','xxa1.5694652177109059'),(10245120,2,3,4,'a','xxa1.7428070658285237'),(10621657,2,3,4,'a','xxa0.9881478445494062'),(10605399,2,3,4,'a','xxa1.0895460071876142'),(10907668,2,3,4,'a','xxa1.8080440888256246'),(10797106,2,3,4,'a','xxa0.5469241762039477'),(10975994,2,3,4,'a','xxa0.11916310164999505'),(10369927,2,3,4,'a','xxa1.3417818987199868'),(10244673,2,3,4,'a','xxa0.4213887140354036'),(10319452,2,3,4,'a','xxa1.930349031398398'),(10867518,2,3,4,'a','xxa0.8841337234565371'),(10607780,2,3,4,'a','xxa1.4254000162942335'),(10740160,2,3,4,'a','xxa1.1253973516872129'),(10593014,2,3,4,'a','xxa0.5539500886145514'),(10605833,2,3,4,'a','xxa0.39647607169717186'),(10173692,2,3,4,'a','xxa0.547495117920912'),(10847661,2,3,4,'a','xxa0.8341231484283909'),(10542325,2,3,4,'a','xxa0.9208843120568286'),(10675235,2,3,4,'a','xxa1.989694146429835'),(10948531,2,3,4,'a','xxa1.5162305268610181'),(10944982,2,3,4,'a','xxa0.9011332456964378'),(10417886,2,3,4,'a','xxa1.475457479688765'),(10434987,2,3,4,'a','xxa1.9234934560242047'),(10503774,2,3,4,'a','xxa1.2672589004666162'),(10656826,2,3,4,'a','xxa0.7664785578534739'),(10945720,2,3,4,'a','xxa1.1577630482220678'),(10057248,2,3,4,'a','xxa1.0991914487436334'),(10576234,2,3,4,'a','xxa0.4647706453360344'),(10433223,2,3,4,'a','xxa0.9379197758975623'),(10045130,2,3,4,'a','xxa1.6375404872349841'),(10958461,2,3,4,'a','xxa0.6719918275922461'),(10804596,2,3,4,'a','xxa0.02998089234342882'),(10661165,2,3,4,'a','xxa0.5217110594005427'),(10320782,2,3,4,'a','xxa1.6426826507250616'),(10144362,2,3,4,'a','xxa0.5155713823768975'),(10855843,2,3,4,'a','xxa1.0117129618411074'),(10961754,2,3,4,'a','xxa0.5824043867945712'),(10570748,2,3,4,'a','xxa1.9602672457324968'),(10188424,2,3,4,'a','xxa0.0034387409719068007'),(10443325,2,3,4,'a','xxa0.42293142007936857'),(10727354,2,3,4,'a','xxa0.0047488659664512295'),(10829809,2,3,4,'a','xxa0.28384622585386615'),(10220188,2,3,4,'a','xxa1.350339050731006'),(10715284,2,3,4,'a','xxa1.1018264844099306'),(10608713,2,3,4,'a','xxa0.7816523004152368'),(10127991,2,3,4,'a','xxa0.934955901405733'),(10953416,2,3,4,'a','xxa0.7292919761774056'),(10962982,2,3,4,'a','xxa1.4419449879247184'),(10715916,2,3,4,'a','xxa0.8333275381786074'),(10935570,2,3,4,'a','xxa0.8557188872766857'),(10332587,2,3,4,'a','xxa0.7587115678551715'),(10899018,2,3,4,'a','xxa0.7140503569636963'),(10088070,2,3,4,'a','xxa0.7385532248192962'),(10582172,2,3,4,'a','xxa1.606058612098972'),(10268631,2,3,4,'a','xxa1.8681351597124107'),(10864445,2,3,4,'a','xxa1.040041078850665'),(10006769,2,3,4,'a','xxa0.9475646139565526'),(10348605,2,3,4,'a','xxa0.6433585087241218'),(10562581,2,3,4,'a','xxa1.6957306933549519'),(10551585,2,3,4,'a','xxa0.4286571484325986'),(10416888,2,3,4,'a','xxa0.8829101332304162'),(10956611,2,3,4,'a','xxa0.9173787486901309'),(10423614,2,3,4,'a','xxa1.4840055792443507'),(10439171,2,3,4,'a','xxa1.9396978206370936'),(10531730,2,3,4,'a','xxa1.4982089321112344'),(10150332,2,3,4,'a','xxa1.008689877585219'),(10070730,2,3,4,'a','xxa1.6812278383236638'),(10990880,2,3,4,'a','xxa0.8651205830882495'),(10190160,2,3,4,'a','xxa1.3062410869693766'),(10695122,2,3,4,'a','xxa1.03249595782952'),(10495874,2,3,4,'a','xxa1.8612539692421015'),(10165512,2,3,4,'a','xxa0.07136234833985786'),(10681869,2,3,4,'a','xxa0.6045994484839956'),(10465893,2,3,4,'a','xxa0.8451289393428051'),(10715144,2,3,4,'a','xxa0.616055364362947'),(10394706,2,3,4,'a','xxa0.09889202201635765'),(10063113,2,3,4,'a','xxa0.3344506922498818'),(10646789,2,3,4,'a','xxa1.4645355078061442'),(10720973,2,3,4,'a','xxa0.8161189712734138'),(10877380,2,3,4,'a','xxa0.32544143714517487'),(10181464,2,3,4,'a','xxa0.8383171175032083'),(10551400,2,3,4,'a','xxa0.999052851459992'),(10843431,2,3,4,'a','xxa1.4371506734165835'),(10062584,2,3,4,'a','xxa0.3143876849807684'),(10598217,2,3,4,'a','xxa1.0390105070909583'),(10802874,2,3,4,'a','xxa0.9117110231069019'),(10870655,2,3,4,'a','xxa1.9714154284181218'),(10316574,2,3,4,'a','xxa1.2514944851878047'),(10179014,2,3,4,'a','xxa0.035656916010805326'),(10552100,2,3,4,'a','xxa1.414031590720668'),(10878778,2,3,4,'a','xxa0.5456885328010549'),(10727886,2,3,4,'a','xxa1.6417983599396406'),(10920837,2,3,4,'a','xxa0.2829725968492893'),(10944921,2,3,4,'a','xxa0.600294653885341'),(10665973,2,3,4,'a','xxa0.8588456053834833'),(10149195,2,3,4,'a','xxa0.9154151705237247'),(10840952,2,3,4,'a','xxa1.6632783447050288'),(10635339,2,3,4,'a','xxa1.3635525678876346'),(10502865,2,3,4,'a','xxa0.9379942407254076'),(10836390,2,3,4,'a','xxa1.549918015999643'),(10365625,2,3,4,'a','xxa1.0064946906701613'),(10419362,2,3,4,'a','xxa1.1741375319418847'),(10677257,2,3,4,'a','xxa1.250161444069968'),(10093631,2,3,4,'a','xxa1.1858302030580399'),(10683681,2,3,4,'a','xxa1.279321414678657'),(10147260,2,3,4,'a','xxa1.634635604577731'),(10644809,2,3,4,'a','xxa1.5441843080745863'),(10926033,2,3,4,'a','xxa0.6277819654231723'),(10791354,2,3,4,'a','xxa0.03019912357460626'),(10701434,2,3,4,'a','xxa0.923746212314578'),(10205063,2,3,4,'a','xxa1.279386475011135'),(10583279,2,3,4,'a','xxa1.9946276675859724'),(10236733,2,3,4,'a','xxa0.3834479622388705'),(10248421,2,3,4,'a','xxa1.3338628274703983');


select count(distinct c5), c2 from skew_agg group by c2;
select count(distinct c5), count(c3+c1+3) from skew_agg group by c2;
select count(distinct c0, c5), sum(c3) from skew_agg group by c2;
select count(distinct c1, c2, c3), avg(c3) from skew_agg group by c4;
select count(distinct c0), BITMAP_UNION_COUNT(TO_BITMAP(c0 + c3)) from skew_agg group by c2;

select sum(distinct c3), c2 from skew_agg group by c2;
select sum(distinct c0), c2 from skew_agg group by c2;
select sum(distinct c0), APPROX_COUNT_DISTINCT(c3) from skew_agg group by c2;
select sum(distinct c0), APPROX_COUNT_DISTINCT(c3) from skew_agg group by c1, c2;


select count(distinct c5), max(c2) from skew_agg group by 'abc';
select count(distinct c5), count(c3+c1+3) from skew_agg group by 'abc';
select count(distinct c0, c5), sum(c3) from skew_agg group by 'abc';
select count(distinct c1, c2, c3), avg(c3) from skew_agg group by 'abc';
select count(distinct c0), BITMAP_UNION_COUNT(TO_BITMAP(c0 + c3)) from skew_agg group by 'abc';

select sum(distinct c3), avg(c2) from skew_agg group by 'abc';
select sum(distinct c0), min(c2) from skew_agg group by 'abc';
select sum(distinct c0), APPROX_COUNT_DISTINCT(c3) from skew_agg group by 'abc';
select sum(distinct c0), APPROX_COUNT_DISTINCT(c3) from skew_agg group by 'abc';

select count(distinct c5), max(c2) from skew_agg;
select count(distinct c5), count(c3+c1+3) from skew_agg;
select count(distinct c0, c5), sum(c3) from skew_agg;
select count(distinct c1, c2, c3), avg(c3) from skew_agg;
select count(distinct c0), BITMAP_UNION_COUNT(TO_BITMAP(c0 + c3)) from skew_agg;

select sum(distinct c3), avg(c2) from skew_agg;
select sum(distinct c0), min(c2) from skew_agg;
select sum(distinct c0), APPROX_COUNT_DISTINCT(c3) from skew_agg;
select sum(distinct c0), APPROX_COUNT_DISTINCT(c3) from skew_agg;

select avg(distinct c1), count(c2) from skew_agg group by c4;
select avg(distinct c1), count(c2) from skew_agg group by 'abc';
select avg(distinct c1), count(c2) from skew_agg;

select     /*+ SET_VAR (streaming_preaggregation_mode = 'force_streaming',new_planner_agg_stage='3') */     (         ifnull(sum(murmur_hash3_32(week)), 0) + ifnull(sum(murmur_hash3_32(c0)), 0) + ifnull(sum(murmur_hash3_32(__col_0)), 0) + ifnull(sum(murmur_hash3_32(__col_1)), 0) + ifnull(sum(murmur_hash3_32(__col_2)), 0) + ifnull(sum(murmur_hash3_32(__col_3)), 0)     ) as fingerprint from     (         select             date_trunc('week', c4) as week,             c0, (array_length(array_agg(c1))) as __col_0,
(sum(distinct length(c2))) as __col_1, (length(group_concat(cast(c3 as VARCHAR)))) as __col_2, (min(c2)) as __col_3         from             skew_agg         group by             date_trunc('week', c4),             c0     ) as t;
select     /*+ SET_VAR (streaming_preaggregation_mode = 'force_streaming',new_planner_agg_stage='2') */     (         ifnull(sum(murmur_hash3_32(week)), 0) + ifnull(sum(murmur_hash3_32(c0)), 0) + ifnull(sum(murmur_hash3_32(__col_0)), 0) + ifnull(sum(murmur_hash3_32(__col_1)), 0) + ifnull(sum(murmur_hash3_32(__col_2)), 0) + ifnull(sum(murmur_hash3_32(__col_3)), 0)     ) as fingerprint from     (         select             date_trunc('week', c4) as week,             c0, (array_length(array_agg(c1))) as __col_0,
(sum(distinct length(c2))) as __col_1, (length(group_concat(cast(c3 as VARCHAR)))) as __col_2, (min(c2)) as __col_3         from             skew_agg         group by             date_trunc('week', c4),             c0     ) as t;
select     /*+ SET_VAR (streaming_preaggregation_mode = 'auto',new_planner_agg_stage='3') */     (         ifnull(sum(murmur_hash3_32(week)), 0) + ifnull(sum(murmur_hash3_32(c0)), 0) + ifnull(sum(murmur_hash3_32(__col_0)), 0) + ifnull(sum(murmur_hash3_32(__col_1)), 0) + ifnull(sum(murmur_hash3_32(__col_2)), 0) + ifnull(sum(murmur_hash3_32(__col_3)), 0)     ) as fingerprint from     (         select             date_trunc('week', c4) as week,             c0, (array_length(array_agg(c1))) as __col_0,
(sum(distinct length(c2))) as __col_1, (length(group_concat(cast(c3 as VARCHAR)))) as __col_2, (min(c2)) as __col_3         from             skew_agg         group by             date_trunc('week', c4),             c0     ) as t;
select     /*+ SET_VAR (streaming_preaggregation_mode = 'auto',new_planner_agg_stage='3') */     (         ifnull(sum(murmur_hash3_32(week)), 0) + ifnull(sum(murmur_hash3_32(c0)), 0) + ifnull(sum(murmur_hash3_32(__col_0)), 0) + ifnull(sum(murmur_hash3_32(__col_1)), 0) + ifnull(sum(murmur_hash3_32(__col_2)), 0) + ifnull(sum(murmur_hash3_32(__col_3)), 0)     ) as fingerprint from     (         select             date_trunc('week', c4) as week,             c0, (array_length(array_agg(c1))) as __col_0,
(sum(distinct length(c2))) as __col_1, (length(group_concat(cast(c3 as VARCHAR)))) as __col_2, (min(c2)) as __col_3         from             skew_agg         group by             date_trunc('week', c4),             c0     ) as t;

select     /*+ SET_VAR (streaming_preaggregation_mode = 'force_streaming',new_planner_agg_stage='2') */     (         ifnull(sum(murmur_hash3_32(week)), 0) + ifnull(sum(murmur_hash3_32(c0)), 0) + ifnull(sum(murmur_hash3_32(__col_0)), 0) + ifnull(sum(murmur_hash3_32(__col_1)), 0) + ifnull(sum(murmur_hash3_32(__col_2)), 0) + ifnull(sum(murmur_hash3_32(__col_3)), 0) + ifnull(sum(murmur_hash3_32(__col_4)), 0) + ifnull(sum(murmur_hash3_32(__col_5)), 0) + ifnull(sum(murmur_hash3_32(__col_6)), 0) + ifnull(sum(murmur_hash3_32(__col_7)), 0) + ifnull(sum(murmur_hash3_32(__col_8)), 0)     ) as fingerprint from     (         select             date_trunc('week', c2) as week,             c0, (array_length(array_agg(c1))) as __col_0, (sum(distinct length(c0))) as __col_1, (length(group_concat(cast(c1 as VARCHAR)))) as __col_2, (min(c3)) as __col_3, (left(variance(c1), 6)) as __col_4, (multi_distinct_count(length(c2))) as __col_5, (ndv(cast(c4 as BIGINT))) as __col_6, (hll_union_agg(hll_hash(c5))) as __col_7, (bitmap_union_int(cast(length(c1) as INT))) as __col_8         from
      skew_agg         group by             date_trunc('week', c2),             c0     ) as t;
select     /*+ SET_VAR (streaming_preaggregation_mode = 'force_streaming',new_planner_agg_stage='3') */     (         ifnull(sum(murmur_hash3_32(week)), 0) + ifnull(sum(murmur_hash3_32(c0)), 0) + ifnull(sum(murmur_hash3_32(__col_0)), 0) + ifnull(sum(murmur_hash3_32(__col_1)), 0) + ifnull(sum(murmur_hash3_32(__col_2)), 0) + ifnull(sum(murmur_hash3_32(__col_3)), 0) + ifnull(sum(murmur_hash3_32(__col_4)), 0) + ifnull(sum(murmur_hash3_32(__col_5)), 0) + ifnull(sum(murmur_hash3_32(__col_6)), 0) + ifnull(sum(murmur_hash3_32(__col_7)), 0) + ifnull(sum(murmur_hash3_32(__col_8)), 0)     ) as fingerprint from     (         select             date_trunc('week', c2) as week,             c0, (array_length(array_agg(c1))) as __col_0, (sum(distinct length(c0))) as __col_1, (length(group_concat(cast(c1 as VARCHAR)))) as __col_2, (min(c3)) as __col_3, (left(variance(c1), 6)) as __col_4, (multi_distinct_count(length(c2))) as __col_5, (ndv(cast(c4 as BIGINT))) as __col_6, (hll_union_agg(hll_hash(c5))) as __col_7, (bitmap_union_int(cast(length(c1) as INT))) as __col_8         from
      skew_agg         group by             date_trunc('week', c2),             c0     ) as t;
select     /*+ SET_VAR (streaming_preaggregation_mode = 'auto',new_planner_agg_stage='2') */     (         ifnull(sum(murmur_hash3_32(week)), 0) + ifnull(sum(murmur_hash3_32(c0)), 0) + ifnull(sum(murmur_hash3_32(__col_0)), 0) + ifnull(sum(murmur_hash3_32(__col_1)), 0) + ifnull(sum(murmur_hash3_32(__col_2)), 0) + ifnull(sum(murmur_hash3_32(__col_3)), 0) + ifnull(sum(murmur_hash3_32(__col_4)), 0) + ifnull(sum(murmur_hash3_32(__col_5)), 0) + ifnull(sum(murmur_hash3_32(__col_6)), 0) + ifnull(sum(murmur_hash3_32(__col_7)), 0) + ifnull(sum(murmur_hash3_32(__col_8)), 0)     ) as fingerprint from     (         select             date_trunc('week', c2) as week,             c0, (array_length(array_agg(c1))) as __col_0, (sum(distinct length(c0))) as __col_1, (length(group_concat(cast(c1 as VARCHAR)))) as __col_2, (min(c3)) as __col_3, (left(variance(c1), 6)) as __col_4, (multi_distinct_count(length(c2))) as __col_5, (ndv(cast(c4 as BIGINT))) as __col_6, (hll_union_agg(hll_hash(c5))) as __col_7, (bitmap_union_int(cast(length(c1) as INT))) as __col_8         from
      skew_agg         group by             date_trunc('week', c2),             c0     ) as t;
select     /*+ SET_VAR (streaming_preaggregation_mode = 'auto',new_planner_agg_stage='3') */     (         ifnull(sum(murmur_hash3_32(week)), 0) + ifnull(sum(murmur_hash3_32(c0)), 0) + ifnull(sum(murmur_hash3_32(__col_0)), 0) + ifnull(sum(murmur_hash3_32(__col_1)), 0) + ifnull(sum(murmur_hash3_32(__col_2)), 0) + ifnull(sum(murmur_hash3_32(__col_3)), 0) + ifnull(sum(murmur_hash3_32(__col_4)), 0) + ifnull(sum(murmur_hash3_32(__col_5)), 0) + ifnull(sum(murmur_hash3_32(__col_6)), 0) + ifnull(sum(murmur_hash3_32(__col_7)), 0) + ifnull(sum(murmur_hash3_32(__col_8)), 0)     ) as fingerprint from     (         select             date_trunc('week', c2) as week,             c0, (array_length(array_agg(c1))) as __col_0, (sum(distinct length(c0))) as __col_1, (length(group_concat(cast(c1 as VARCHAR)))) as __col_2, (min(c3)) as __col_3, (left(variance(c1), 6)) as __col_4, (multi_distinct_count(length(c2))) as __col_5, (ndv(cast(c4 as BIGINT))) as __col_6, (hll_union_agg(hll_hash(c5))) as __col_7, (bitmap_union_int(cast(length(c1) as INT))) as __col_8         from
      skew_agg         group by             date_trunc('week', c2),             c0     ) as t;

select     /*+ SET_VAR (streaming_preaggregation_mode = 'auto',new_planner_agg_stage='3') */  array_length(array_agg(a1)), count(distinct a1) as cnt from (select split('a,b,c', ',') as a1, 'aaa' as b1) t1 group by b1;
select     /*+ SET_VAR (streaming_preaggregation_mode = 'force_streaming',new_planner_agg_stage='3') */  array_length(array_agg(a1)), count(distinct a1) as cnt from (select split('a,b,c', ',') as a1, 'aaa' as b1) t1 group by b1;

select     /*+ SET_VAR (streaming_preaggregation_mode = 'force_streaming',new_planner_agg_stage='2') */  array_length(array_agg(a1)) as cnt from (select split('a,b,c', ',') as a1, 'aaa' as b1) t1 group by b1;
select     /*+ SET_VAR (streaming_preaggregation_mode = 'force_streaming',new_planner_agg_stage='3') */  array_length(array_agg(a1)) as cnt from (select split('a,b,c', ',') as a1, 'aaa' as b1) t1 group by b1;

select count(distinct c1, c2) from skew_agg limit 5;
select count(distinct c1, c2, c3) from skew_agg group by c4 limit 500;
select count(distinct c1, c2), count(distinct c2, c3), sum(c2) from skew_agg limit 5;
select count(distinct c1, c2, c3), count(distinct c2, c3), sum(c2) from skew_agg group by c4 limit 500;
select count(distinct t1.c1, if(t2.c3 is null, 1, 0)), sum(t1.c2 + if(t2.c3 is null, 1, 0)) from skew_agg t1 left join skew_agg t2 on t1.c4 > t2.c4 group by t1.c4 limit 500;

# streaming_preaggregation_mode = 'auto' new_planner_agg_stage = '0'
set streaming_preaggregation_mode = 'auto';
set new_planner_agg_stage = '0';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);


select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);

select group_concat(distinct 1), array_agg(distinct 2),  sum(c3) from skew_agg group by rollup(c1, c2);
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);

set streaming_preaggregation_mode = 'auto';
set new_planner_agg_stage = '2';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);


select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);

select group_concat(distinct 1), array_agg(distinct 2),  sum(c3) from skew_agg group by rollup(c1, c2);
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);


set streaming_preaggregation_mode = 'auto';
set new_planner_agg_stage = '3';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);


select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);

select group_concat(distinct 1), array_agg(distinct 2),  sum(c3) from skew_agg group by rollup(c1, c2);
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);


set streaming_preaggregation_mode = 'auto';
set new_planner_agg_stage = '4';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);


select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);

select group_concat(distinct 1), array_agg(distinct 2),  sum(c3) from skew_agg group by rollup(c1, c2);
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);
select array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);


# streaming_preaggregation_mode = 'force_streaming' new_planner_agg_stage = '2'
set streaming_preaggregation_mode = 'force_streaming';
set new_planner_agg_stage = '0';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);


select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);


select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);

set streaming_preaggregation_mode = 'force_streaming';
set new_planner_agg_stage = '2';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);


select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);


select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);

set streaming_preaggregation_mode = 'force_streaming';
set new_planner_agg_stage = '3';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);

select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);

select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);


set streaming_preaggregation_mode = 'force_streaming';
set new_planner_agg_stage = '4';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);

select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);

select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);
select array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);


# streaming_preaggregation_mode = 'force_preaggregation' new_planner_agg_stage = '2'
set streaming_preaggregation_mode = 'force_preaggregation';
set new_planner_agg_stage = '0';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);

select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);

select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);

set streaming_preaggregation_mode = 'force_preaggregation';
set new_planner_agg_stage = '2';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);

select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);

select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);


set streaming_preaggregation_mode = 'force_preaggregation';
set new_planner_agg_stage = '3';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);

select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);

select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);


set streaming_preaggregation_mode = 'force_preaggregation';
set new_planner_agg_stage = '4';
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by 1 +1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(c3 order by 1, c4),  ceil(sum(c5)) from (select * from skew_agg order by 1,2,3,4,5,6 limit 10) t group by rollup(c1, c2);

select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by 1+1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by c1;
select group_concat(distinct c2, upper(c4) order by abs(c2 + c3)), array_agg(distinct c3 order by 1, c4),  ceil(sum(c5)) from skew_agg group by rollup(c1, c2);

select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by 1 +1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by c1;
select group_concat(distinct c2), array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);
select array_agg(distinct c2), count(distinct c2), sum(c3) from skew_agg group by rollup(c1, c2);

select count(c2), count(distinct c3), 'a' as a from (select t1.* from skew_agg t0 join skew_agg t1 on t0.c4 = t1.c4) t group by a;
select count(c2), 'a' as a from (select t1.* from skew_agg t0 join skew_agg t1 on t0.c4 = t1.c4) t group by a;
select sum(c2), count(distinct c3), 'a' as a from (select t1.* from skew_agg t0 join skew_agg t1 on t0.c4 = t1.c4) t group by a;
select sum(c2),  'a' as a from (select t1.* from skew_agg t0 join skew_agg t1 on t0.c4 = t1.c4) t group by a;

select sum(c0), count(distinct c2, c3) from skew_agg where c0 = 480816 and c1 = 2 group by c4;

select count(distinct 1, 2), count(distinct 1), count(distinct null), group_concat(distinct 'a', 'b'), count(distinct c2) from skew_agg;
