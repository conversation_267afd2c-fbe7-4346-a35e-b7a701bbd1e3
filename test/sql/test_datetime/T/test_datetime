-- name: test_datetime_ingest 
CREATE TABLE test_datetime (
    c1 int,
    c2 datetime
) DUPLICATE KEY(c1)
DISTRIBUTED BY HASH(c1)
BUCKETS 3
PROPERTIES("replication_num" = "1");

insert into test_datetime values
(1, '2020-01-01 00:00:00'),
(2, '2020-01-01 00:00:00.0'),
(3, '2020-01-01 00:00:00.01'),
(4, '2020-01-01 00:00:00.012'),
(5, '2020-01-01 00:00:00.0123'),
(6, '2020-01-01 00:00:00.01234'),
(7, '2020-01-01 00:00:00.012345'),
(8, '2020-01-01 00:00:00.1'),
(9, '2020-01-01 00:00:00.12'),
(10, '2020-01-01 00:00:00.123'),
(11, '2020-01-01 00:00:00.1234'),
(12, '2020-01-01 00:00:00.12345'),
(13, '2020-01-01 00:00:00.123450');

select * from test_datetime order by c1;