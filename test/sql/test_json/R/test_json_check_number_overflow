-- name: test_json_check_number_overflow
create database test_json_check_number_overflow_${uuid0};
-- result:
-- !result
use test_json_check_number_overflow_${uuid0};
-- result:
-- !result
create table t1(k1 int, d1 bigint, f1 int) PROPERTIES (
    "replication_num" = "1"
);
-- result:
-- !result
shell: curl --location-trusted -u root: -T ${root_path}/lib/../common/data/stream_load/check_number_overflow.json  -H "format: JSON" --header "Expect: 100-continue"   -XPUT -H partial_update:false -H label:test_json_check_number_overflow_${uuid0} ${url}/api/test_json_check_number_overflow_${uuid0}/t1/_stream_load
-- result:
0
{
    "Status": "Success",
    "Message": "OK"
}
-- !result
sync;
-- result:
-- !result
select * from t1 order by k1;
-- result:
1	9223372036854774784	2147483000
2	None	2147483647
3	None	2147483647
4	None	None
5	None	None
6	-9223372036854675456	-2147473647
7	-9223372036854775808	-2147483647
8	-9223372036854775808	-2147483648
9	-9223372036854775808	None
10	None	None
11	32767	32767
12	32768	32768
13	32769	32769
14	-32765	-32765
15	-32767	-32767
16	-32768	-32768
17	-32770	-32770
-- !result