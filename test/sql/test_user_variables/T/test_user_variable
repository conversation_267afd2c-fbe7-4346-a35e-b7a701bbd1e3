-- name: test_user_variable
CREATE TABLE `t0` (
  `c0` int(11) NULL COMMENT "",
  `c1` varchar(20) NULL COMMENT "",
  `c2` varchar(200) NULL COMMENT "",
  `c3` int(11) NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`c0`, `c1`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`c0`, `c1`) BUCKETS 4
PROPERTIES (
"replication_num" = "1"
);

insert into t0 SELECT generate_series, generate_series, generate_series, generate_series FROM TABLE(generate_series(1,  40960));
insert into t0 values (null,null,null,null);

-- basic test
set @var = 1;
select @var;
-- user variable generate from SQL
set @var = (select count(*) from t0 limit 1);
select @var;

-- user variable using in hint
with tx as (select @var2 as x)
select /*+ SET_USER_VARIABLE(@var2 = (select count(*) from t0 limit 1)) */ * from tx;
select @var2;


with tx as (select @var2 as x)
select /*+ SET_USER_VARIABLE(@var2 = (select count(*) from (select l.c0 from t0 l join t0 r on l.c0 = r.c0 ) tb)) */ * from tx;