-- name: test_task_run_status
create database db_${uuid0};
-- result:
-- !result
use db_${uuid0};
-- result:
-- !result
CREATE TABLE ss( event_day DATE, pv BIGINT) DUPLICATE KEY(event_day) DISTRIBUTED BY HASH(event_day) BUCKETS 8 PROPERTIES("replication_num" = "1");
-- result:
-- !result
insert into ss values('2020-01-14', 1), ('2020-01-14', 3), ('2020-01-15', 2);
-- result:
-- !result
CREATE MATERIALIZED VIEW mv1 DISTRIBUTED BY hash(event_day) AS SELECT event_day, sum(pv) as sum_pv FROM ss GROUP BY event_day;
-- result:
-- !result
[UC]REFRESH MATERIALIZED VIEW mv1 with sync mode ;
SELECT * FROM mv1 ORDER BY event_day;
-- result:
2020-01-14	4
2020-01-15	2
-- !result
SELECT TABLE_NAME, REFRESH_TYPE, IS_ACTIVE, INACTIVE_REASON FROM information_schema.materialized_views WHERE table_name = 'mv1' and TABLE_SCHEMA='db_${uuid0}';
-- result:
mv1	MANUAL	true	
-- !result
SELECT TABLE_NAME, REFRESH_TYPE, IS_ACTIVE, INACTIVE_REASON FROM information_schema.materialized_views WHERE TABLE_NAME = 'mv1' and TABLE_SCHEMA='db_${uuid0}';
-- result:
mv1	MANUAL	true	
-- !result
SELECT TABLE_NAME, REFRESH_TYPE, IS_ACTIVE, INACTIVE_REASON FROM information_schema.materialized_views WHERE TABLE_SCHEMA = 'db_${uuid0}';
-- result:
mv1	MANUAL	true	
-- !result
SELECT count(1) FROM information_schema.materialized_views WHERE TABLE_SCHEMA = 'db_${uuid0}';
-- result:
1
-- !result
[UC]task_name=SELECT TASK_NAME FROM information_schema.materialized_views WHERE TABLE_SCHEMA = 'db_${uuid0}';
-- result:
mv-10244
-- !result
SELECT count(1) FROM information_schema.task_runs WHERE TASK_NAME = '${task_name}';
-- result:
2
-- !result
SELECT count(1) FROM information_schema.task_runs WHERE task_name = '${task_name}';
-- result:
2
-- !result
[UC]query_id=SELECT `QUERY_ID` FROM information_schema.task_runs WHERE task_name = '${task_name}' limit 1;
-- result:
cb42f73d-0692-11ef-b551-926f0eb983e6
-- !result
SELECT count(1) FROM information_schema.task_runs WHERE QUERY_ID = '${query_id}';
-- result:
1
-- !result
SELECT count(1) FROM information_schema.task_runs WHERE query_id= '${query_id}';
-- result:
1
-- !result
[UC]state=SELECT `STATE` FROM information_schema.task_runs WHERE QUERY_ID = '${query_id}';
-- result:
PENDING
-- !result
SELECT count(1) FROM information_schema.task_runs WHERE `STATE` = '${state}' and task_name = '${task_name}' ;
-- result:
1
-- !result
drop database db_${uuid0};
-- result:
-- !result