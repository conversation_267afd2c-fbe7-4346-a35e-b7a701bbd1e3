#!/usr/bin/env python
# encoding: utf-8

# Copyright 2021-present StarRocks, Inc. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# The format is:
#   <function id> <name>, <exception_safe>, <check_overflow>, <return_type>, [<args>], <backend fn>
# With an optional
#   <prepare fn>, <close fn>
#
# example:
#   [1, "add", "TINYINT", ["TINYINT", "TINYINT"], "Math::add", "Math::add_prepare", "Math::add_close"]
#
# the id rule: {module function group}|0|{function group}|{sub-function/alias-function}
#
# example round functions: 1               013             0       = 10130
#                          ^                ^              ^
#                   {math function} {function group} {sub-function}
vectorized_functions = [
    # 10xxx: math functions
    [10010, "pi", True, False, "DOUBLE", [], "MathFunctions::pi"],
    [10020, "e", True, False, "DOUBLE", [], "MathFunctions::e"],
    [10030, "sign", True, False, "FLOAT", ["DOUBLE"], "MathFunctions::sign"],

    [10040, "abs", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::abs_double"],
    [10041, "abs", True, False, "FLOAT", ["FLOAT"], "MathFunctions::abs_float"],
    [10042, "abs", True, False, "LARGEINT", ["LARGEINT"], "MathFunctions::abs_largeint"],
    [10043, "abs", True, False, "LARGEINT", ["BIGINT"], "MathFunctions::abs_bigint"],
    [10044, "abs", True, False, "BIGINT", ["INT"], "MathFunctions::abs_int"],
    [10045, "abs", True, False, "INT", ["SMALLINT"], "MathFunctions::abs_smallint"],
    [10046, "abs", True, False, "SMALLINT", ["TINYINT"], "MathFunctions::abs_tinyint"],
    [10047, "abs", True, False, "DECIMALV2", ["DECIMALV2"], "MathFunctions::abs_decimalv2val"],
    [100470, "abs", True, False, "DECIMAL32", ["DECIMAL32"], "MathFunctions::abs_decimal32"],
    [100471, "abs", True, False, "DECIMAL64", ["DECIMAL64"], "MathFunctions::abs_decimal64"],
    [100472, "abs", True, False, "DECIMAL128", ["DECIMAL128"], "MathFunctions::abs_decimal128"],

    [10050, "sin", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::sin"],
    [10060, "asin", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::asin"],
    [10061, "sinh", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::sinh"],
    [10070, "cos", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::cos"],
    [10080, "acos", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::acos"],
    [10081, "cosh", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::cosh"],
    [10090, "tan", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::tan"],
    [10100, "atan", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::atan"],
    [10101, "tanh", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::tanh"],
    [10102, "cosine_similarity", True, False, "FLOAT", ["ARRAY_FLOAT", "ARRAY_FLOAT"],
     "MathFunctions::cosine_similarity<TYPE_FLOAT, false>"],
    [10103, "cosine_similarity_norm", True, False, "FLOAT", ["ARRAY_FLOAT", "ARRAY_FLOAT"],
     "MathFunctions::cosine_similarity<TYPE_FLOAT, true>"],

    [10110, "ceil", True, False, "BIGINT", ["DOUBLE"], "MathFunctions::ceil"],
    [10111, "ceiling", True, False, "BIGINT", ["DOUBLE"], "MathFunctions::ceil"],
    [10112, "dceil", True, False, "BIGINT", ["DOUBLE"], "MathFunctions::ceil"],

    [10120, "floor", True, False, "BIGINT", ["DOUBLE"], "MathFunctions::floor"],
    [10121, "dfloor", True, False, "BIGINT", ["DOUBLE"], "MathFunctions::floor"],

    [10125, "dround", True, False, "DECIMAL128", ["DECIMAL128"], "MathFunctions::round_decimal128"],
    [10126, "dround", True, False, "DECIMAL128", ["DECIMAL128", "INT"], "MathFunctions::round_up_to_decimal128"],
    [10127, "round", True, False, "DECIMAL128", ["DECIMAL128"], "MathFunctions::round_decimal128"],
    [10128, "round", True, False, "DECIMAL128", ["DECIMAL128", "INT"], "MathFunctions::round_up_to_decimal128"],
    [10129, "truncate", True, False, "DECIMAL128", ["DECIMAL128", "INT"], "MathFunctions::truncate_decimal128"],
    [10130, "round", True, False, "BIGINT", ["DOUBLE"], "MathFunctions::round"],
    [10131, "dround", True, False, "BIGINT", ["DOUBLE"], "MathFunctions::round"],
    [10132, "round", True, False, "DOUBLE", ["DOUBLE", "INT"], "MathFunctions::round_up_to"],
    [10133, "dround", True, False, "DOUBLE", ["DOUBLE", "INT"], "MathFunctions::round_up_to"],
    [10134, "truncate", True, False, "DOUBLE", ["DOUBLE", "INT"], "MathFunctions::truncate"],

    [10140, "ln", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::ln"],
    [10141, "dlog1", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::ln"],
    [10142, "log", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::ln"],

    [10150, "log", True, False, "DOUBLE", ["DOUBLE", "DOUBLE"], "MathFunctions::log"],
    [10160, "log2", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::log2"],

    [10170, "log10", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::log10"],
    [10171, "dlog10", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::log10"],

    [10180, "exp", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::exp"],
    [10181, "dexp", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::exp"],

    [10190, "radians", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::radians"],
    [10200, "degrees", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::degrees"],

    [10210, "sqrt", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::sqrt"],
    [10211, "dsqrt", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::sqrt"],

    [10220, "pow", True, False, "DOUBLE", ["DOUBLE", "DOUBLE"], "MathFunctions::pow"],
    [10221, "power", True, False, "DOUBLE", ["DOUBLE", "DOUBLE"], "MathFunctions::pow"],
    [10222, "dpow", True, False, "DOUBLE", ["DOUBLE", "DOUBLE"], "MathFunctions::pow"],
    [10223, "fpow", True, False, "DOUBLE", ["DOUBLE", "DOUBLE"], "MathFunctions::pow"],

    [10224, "atan2", True, False, "DOUBLE", ["DOUBLE", "DOUBLE"], "MathFunctions::atan2"],
    [10225, "cot", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::cot"],

    [10230, "pmod", True, False, "BIGINT", ["BIGINT", "BIGINT"], "MathFunctions::pmod<TYPE_BIGINT>"],
    [10231, "pmod", True, False, "DOUBLE", ["DOUBLE", "DOUBLE"], "MathFunctions::pmod<TYPE_DOUBLE>"],

    [10240, "fmod", True, False, "FLOAT", ["FLOAT", "FLOAT"], "MathFunctions::fmod<TYPE_FLOAT>"],
    [10241, "fmod", True, False, "DOUBLE", ["DOUBLE", "DOUBLE"], "MathFunctions::fmod<TYPE_DOUBLE>"],

    [10250, "mod", True, False, "TINYINT", ["TINYINT", "TINYINT"], "MathFunctions::mod<TYPE_TINYINT>"],
    [10251, "mod", True, False, "SMALLINT", ["SMALLINT", "SMALLINT"], "MathFunctions::mod<TYPE_SMALLINT>"],
    [10252, "mod", True, False, "INT", ["INT", "INT"], "MathFunctions::mod<TYPE_INT>"],
    [10253, "mod", True, False, "BIGINT", ["BIGINT", "BIGINT"], "MathFunctions::mod<TYPE_BIGINT>"],
    [10254, "mod", True, False, "LARGEINT", ["LARGEINT", "LARGEINT"], "MathFunctions::mod<TYPE_LARGEINT>"],
    [10255, "mod", True, False, "FLOAT", ["FLOAT", "FLOAT"], "MathFunctions::fmod<TYPE_FLOAT>"],
    [10256, "mod", True, False, "DOUBLE", ["DOUBLE", "DOUBLE"], "MathFunctions::fmod<TYPE_DOUBLE>"],
    [10257, "mod", True, False, "DECIMALV2", ["DECIMALV2", "DECIMALV2"], "MathFunctions::mod<TYPE_DECIMALV2>"],
    [102570, "mod", True, False, "DECIMAL32", ["DECIMAL32", "DECIMAL32"], "MathFunctions::mod<TYPE_DECIMAL32>"],
    [102571, "mod", True, False, "DECIMAL64", ["DECIMAL64", "DECIMAL64"], "MathFunctions::mod<TYPE_DECIMAL64>"],
    [102572, "mod", True, False, "DECIMAL128", ["DECIMAL128", "DECIMAL128"], "MathFunctions::mod<TYPE_DECIMAL128>"],

    [10260, "positive", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::positive<TYPE_DOUBLE>"],
    [10261, "positive", True, False, "BIGINT", ["BIGINT"], "MathFunctions::positive<TYPE_BIGINT>"],
    [10262, "positive", True, False, "DECIMALV2", ["DECIMALV2"], "MathFunctions::positive<TYPE_DECIMALV2>"],
    [102620, "positive", True, False, "DECIMAL32", ["DECIMAL32"], "MathFunctions::positive<TYPE_DECIMAL32>"],
    [102621, "positive", True, False, "DECIMAL64", ["DECIMAL64"], "MathFunctions::positive<TYPE_DECIMAL64>"],
    [102622, "positive", True, False, "DECIMAL128", ["DECIMAL128"], "MathFunctions::positive<TYPE_DECIMAL128>"],

    [10270, "negative", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::negative<TYPE_DOUBLE>"],
    [10271, "negative", True, False, "BIGINT", ["BIGINT"], "MathFunctions::negative<TYPE_BIGINT>"],
    [10272, "negative", True, False, "DECIMALV2", ["DECIMALV2"], "MathFunctions::negative<TYPE_DECIMALV2>"],
    [102720, "negative", True, False, "DECIMAL32", ["DECIMAL32"], "MathFunctions::negative<TYPE_DECIMAL32>"],
    [102721, "negative", True, False, "DECIMAL64", ["DECIMAL64"], "MathFunctions::negative<TYPE_DECIMAL64>"],
    [102722, "negative", True, False, "DECIMAL128", ["DECIMAL128"], "MathFunctions::negative<TYPE_DECIMAL128>"],

    [10280, "least", True, False, "TINYINT", ["TINYINT", "..."], "MathFunctions::least<TYPE_TINYINT>"],
    [10281, "least", True, False, "SMALLINT", ["SMALLINT", "..."], "MathFunctions::least<TYPE_SMALLINT>"],
    [10282, "least", True, False, "INT", ["INT", "..."], "MathFunctions::least<TYPE_INT>"],
    [10283, "least", True, False, "BIGINT", ["BIGINT", "..."], "MathFunctions::least<TYPE_BIGINT>"],
    [10284, "least", True, False, "LARGEINT", ["LARGEINT", "..."], "MathFunctions::least<TYPE_LARGEINT>"],
    [10285, "least", True, False, "FLOAT", ["FLOAT", "..."], "MathFunctions::least<TYPE_FLOAT>"],
    [10286, "least", True, False, "DOUBLE", ["DOUBLE", "..."], "MathFunctions::least<TYPE_DOUBLE>"],
    [10287, "least", True, False, "DECIMALV2", ["DECIMALV2", "..."], "MathFunctions::least<TYPE_DECIMALV2>"],
    [102870, "least", True, False, "DECIMAL32", ["DECIMAL32", "..."], "MathFunctions::least<TYPE_DECIMAL32>"],
    [102871, "least", True, False, "DECIMAL64", ["DECIMAL64", "..."], "MathFunctions::least<TYPE_DECIMAL64>"],
    [102872, "least", True, False, "DECIMAL128", ["DECIMAL128", "..."], "MathFunctions::least<TYPE_DECIMAL128>"],
    [10288, "least", True, False, "DATETIME", ["DATETIME", "..."], "MathFunctions::least<TYPE_DATETIME>"],
    [10289, "least", True, False, "VARCHAR", ["VARCHAR", "..."], "MathFunctions::least<TYPE_VARCHAR>"],

    [10290, "greatest", True, False, "TINYINT", ["TINYINT", "..."], "MathFunctions::greatest<TYPE_TINYINT>"],
    [10291, "greatest", True, False, "SMALLINT", ["SMALLINT", "..."], "MathFunctions::greatest<TYPE_SMALLINT>"],
    [10292, "greatest", True, False, "INT", ["INT", "..."], "MathFunctions::greatest<TYPE_INT>"],
    [10293, "greatest", True, False, "BIGINT", ["BIGINT", "..."], "MathFunctions::greatest<TYPE_BIGINT>"],
    [10294, "greatest", True, False, "LARGEINT", ["LARGEINT", "..."], "MathFunctions::greatest<TYPE_LARGEINT>"],
    [10295, "greatest", True, False, "FLOAT", ["FLOAT", "..."], "MathFunctions::greatest<TYPE_FLOAT>"],
    [10296, "greatest", True, False, "DOUBLE", ["DOUBLE", "..."], "MathFunctions::greatest<TYPE_DOUBLE>"],
    [10297, "greatest", True, False, "DECIMALV2", ["DECIMALV2", "..."], "MathFunctions::greatest<TYPE_DECIMALV2>"],
    [102970, "greatest", True, False, "DECIMAL32", ["DECIMAL32", "..."], "MathFunctions::greatest<TYPE_DECIMAL32>"],
    [102971, "greatest", True, False, "DECIMAL64", ["DECIMAL64", "..."], "MathFunctions::greatest<TYPE_DECIMAL64>"],
    [102972, "greatest", True, False, "DECIMAL128", ["DECIMAL128", "..."], "MathFunctions::greatest<TYPE_DECIMAL128>"],
    [10298, "greatest", True, False, "DATETIME", ["DATETIME", "..."], "MathFunctions::greatest<TYPE_DATETIME>"],
    [10299, "greatest", True, False, "VARCHAR", ["VARCHAR", "..."], "MathFunctions::greatest<TYPE_VARCHAR>"],

    [10300, "rand", True, False, "DOUBLE", [], "MathFunctions::rand", "MathFunctions::rand_prepare",
     "MathFunctions::rand_close"],
    [10301, "random", True, False, "DOUBLE", [], "MathFunctions::rand", "MathFunctions::rand_prepare",
     "MathFunctions::rand_close"],
    [10302, "rand", True, False, "DOUBLE", ["BIGINT"], "MathFunctions::rand_seed", "MathFunctions::rand_prepare",
     "MathFunctions::rand_close"],
    [10303, "random", True, False, "DOUBLE", ["BIGINT"], "MathFunctions::rand_seed", "MathFunctions::rand_prepare",
     "MathFunctions::rand_close"],

    [10311, "bin", True, False, "VARCHAR", ['BIGINT'], "MathFunctions::bin"],

    [10312, "hex", True, False, "VARCHAR", ['BIGINT'], "StringFunctions::hex_int"],
    [10313, "hex", True, False, "VARCHAR", ['VARCHAR'], "StringFunctions::hex_string"],
    [10323, "hex", True, False, "VARCHAR", ['VARBINARY'], "StringFunctions::hex_string"],
    [10314, "unhex", True, False, "VARCHAR", ['VARCHAR'], "StringFunctions::unhex"],
    [10315, "sm3", True, False, "VARCHAR", ['VARCHAR'], "StringFunctions::sm3"],
    [10316, "hex_decode_binary", True, False, "VARBINARY", ['VARCHAR'], "StringFunctions::unhex"],
    [10317, "hex_decode_string", True, False, "VARCHAR", ['VARCHAR'], "StringFunctions::unhex"],

    [10320, "conv", True, False, "VARCHAR", ["BIGINT", "TINYINT", "TINYINT"], "MathFunctions::conv_int"],
    [10321, "conv", True, False, "VARCHAR", ["VARCHAR", "TINYINT", "TINYINT"], "MathFunctions::conv_string"],

    [10322, "square", True, False, "DOUBLE", ["DOUBLE"], "MathFunctions::square"],

    # 20xxx: bit functions
    [20010, 'bitand', True, False, 'TINYINT', ['TINYINT', 'TINYINT'], "BitFunctions::bitAnd<TYPE_TINYINT>"],
    [20011, 'bitand', True, False, 'SMALLINT', ['SMALLINT', 'SMALLINT'], "BitFunctions::bitAnd<TYPE_SMALLINT>"],
    [20012, 'bitand', True, False, 'INT', ['INT', 'INT'], "BitFunctions::bitAnd<TYPE_INT>"],
    [20013, 'bitand', True, False, 'BIGINT', ['BIGINT', 'BIGINT'], "BitFunctions::bitAnd<TYPE_BIGINT>"],
    [20014, 'bitand', True, False, 'LARGEINT', ['LARGEINT', 'LARGEINT'], "BitFunctions::bitAnd<TYPE_LARGEINT>"],

    [20020, 'bitor', True, False, 'TINYINT', ['TINYINT', 'TINYINT'], "BitFunctions::bitOr<TYPE_TINYINT>"],
    [20021, 'bitor', True, False, 'SMALLINT', ['SMALLINT', 'SMALLINT'], "BitFunctions::bitOr<TYPE_SMALLINT>"],
    [20022, 'bitor', True, False, 'INT', ['INT', 'INT'], "BitFunctions::bitOr<TYPE_INT>"],
    [20023, 'bitor', True, False, 'BIGINT', ['BIGINT', 'BIGINT'], "BitFunctions::bitOr<TYPE_BIGINT>"],
    [20024, 'bitor', True, False, 'LARGEINT', ['LARGEINT', 'LARGEINT'], "BitFunctions::bitOr<TYPE_LARGEINT>"],

    [20030, 'bitxor', True, False, 'TINYINT', ['TINYINT', 'TINYINT'], "BitFunctions::bitXor<TYPE_TINYINT>"],
    [20031, 'bitxor', True, False, 'SMALLINT', ['SMALLINT', 'SMALLINT'], "BitFunctions::bitXor<TYPE_SMALLINT>"],
    [20032, 'bitxor', True, False, 'INT', ['INT', 'INT'], "BitFunctions::bitXor<TYPE_INT>"],
    [20033, 'bitxor', True, False, 'BIGINT', ['BIGINT', 'BIGINT'], "BitFunctions::bitXor<TYPE_BIGINT>"],
    [20034, 'bitxor', True, False, 'LARGEINT', ['LARGEINT', 'LARGEINT'], "BitFunctions::bitXor<TYPE_LARGEINT>"],

    [20040, 'bitnot', True, False, 'TINYINT', ['TINYINT'], "BitFunctions::bitNot<TYPE_TINYINT>"],
    [20041, 'bitnot', True, False, 'SMALLINT', ['SMALLINT'], "BitFunctions::bitNot<TYPE_SMALLINT>"],
    [20042, 'bitnot', True, False, 'INT', ['INT'], "BitFunctions::bitNot<TYPE_INT>"],
    [20043, 'bitnot', True, False, 'BIGINT', ['BIGINT'], "BitFunctions::bitNot<TYPE_BIGINT>"],
    [20044, 'bitnot', True, False, 'LARGEINT', ['LARGEINT'], "BitFunctions::bitNot<TYPE_LARGEINT>"],

    [20050, 'bit_shift_left', True, False, 'TINYINT', ['TINYINT', 'BIGINT'],
     "BitFunctions::bitShiftLeft<TYPE_TINYINT>"],
    [20051, 'bit_shift_left', True, False, 'SMALLINT', ['SMALLINT', 'BIGINT'],
     "BitFunctions::bitShiftLeft<TYPE_SMALLINT>"],
    [20052, 'bit_shift_left', True, False, 'INT', ['INT', 'BIGINT'], "BitFunctions::bitShiftLeft<TYPE_INT>"],
    [20053, 'bit_shift_left', True, False, 'BIGINT', ['BIGINT', 'BIGINT'], "BitFunctions::bitShiftLeft<TYPE_BIGINT>"],
    [20054, 'bit_shift_left', True, False, 'LARGEINT', ['LARGEINT', 'BIGINT'],
     "BitFunctions::bitShiftLeft<TYPE_LARGEINT>"],

    [20060, 'bit_shift_right', True, False, 'TINYINT', ['TINYINT', 'BIGINT'],
     "BitFunctions::bitShiftRight<TYPE_TINYINT>"],
    [20061, 'bit_shift_right', True, False, 'SMALLINT', ['SMALLINT', 'BIGINT'],
     "BitFunctions::bitShiftRight<TYPE_SMALLINT>"],
    [20062, 'bit_shift_right', True, False, 'INT', ['INT', 'BIGINT'], "BitFunctions::bitShiftRight<TYPE_INT>"],
    [20063, 'bit_shift_right', True, False, 'BIGINT', ['BIGINT', 'BIGINT'], "BitFunctions::bitShiftRight<TYPE_BIGINT>"],
    [20064, 'bit_shift_right', True, False, 'LARGEINT', ['LARGEINT', 'BIGINT'],
     "BitFunctions::bitShiftRight<TYPE_LARGEINT>"],

    [20070, 'bit_shift_right_logical', True, False, 'TINYINT', ['TINYINT', 'BIGINT'],
     "BitFunctions::bitShiftRightLogical<TYPE_TINYINT>"],
    [20071, 'bit_shift_right_logical', True, False, 'SMALLINT', ['SMALLINT', 'BIGINT'],
     "BitFunctions::bitShiftRightLogical<TYPE_SMALLINT>"],
    [20072, 'bit_shift_right_logical', True, False, 'INT', ['INT', 'BIGINT'],
     "BitFunctions::bitShiftRightLogical<TYPE_INT>"],
    [20073, 'bit_shift_right_logical', True, False, 'BIGINT', ['BIGINT', 'BIGINT'],
     "BitFunctions::bitShiftRightLogical<TYPE_BIGINT>"],
    [20074, 'bit_shift_right_logical', True, False, 'LARGEINT', ['LARGEINT', 'BIGINT'],
     "BitFunctions::bitShiftRightLogical<TYPE_LARGEINT>"],

    # 30xxx: string functions
    [30010, 'substr', True, False, 'VARCHAR', ['VARCHAR', 'INT'], 'StringFunctions::substring',
     'StringFunctions::sub_str_prepare', 'StringFunctions::sub_str_close'],
    [30011, 'substr', True, False, 'VARCHAR', ['VARCHAR', 'INT', 'INT'], 'StringFunctions::substring',
     'StringFunctions::sub_str_prepare', 'StringFunctions::sub_str_close'],
    [30012, 'substring', True, False, 'VARCHAR', ['VARCHAR', 'INT'], 'StringFunctions::substring',
     'StringFunctions::sub_str_prepare', 'StringFunctions::sub_str_close'],
    [30013, 'substring', True, False, 'VARCHAR', ['VARCHAR', 'INT', 'INT'], 'StringFunctions::substring',
     'StringFunctions::sub_str_prepare', 'StringFunctions::sub_str_close'],

    [30020, 'left', True, False, 'VARCHAR', ['VARCHAR', 'INT'], 'StringFunctions::left',
     'StringFunctions::left_or_right_prepare', 'StringFunctions::left_or_right_close'],
    [30021, 'strleft', True, False, 'VARCHAR', ['VARCHAR', 'INT'], 'StringFunctions::left',
     'StringFunctions::left_or_right_prepare', 'StringFunctions::left_or_right_close'],

    [30030, 'right', True, False, 'VARCHAR', ['VARCHAR', 'INT'], 'StringFunctions::right',
     'StringFunctions::left_or_right_prepare', 'StringFunctions::left_or_right_close'],
    [30031, 'strright', True, False, 'VARCHAR', ['VARCHAR', 'INT'], 'StringFunctions::right',
     'StringFunctions::left_or_right_prepare', 'StringFunctions::left_or_right_close'],

    [30040, 'ends_with', True, False, 'BOOLEAN', ['VARCHAR', 'VARCHAR'], 'StringFunctions::ends_with'],
    [30050, 'starts_with', True, False, 'BOOLEAN', ['VARCHAR', 'VARCHAR'], 'StringFunctions::starts_with'],

    [30060, 'null_or_empty', True, False, 'BOOLEAN', ['VARCHAR'], 'StringFunctions::null_or_empty'],

    [30070, 'space', True, True, 'VARCHAR', ['INT'], 'StringFunctions::space'],
    [30080, 'repeat', True, True, 'VARCHAR', ['VARCHAR', 'INT'], 'StringFunctions::repeat'],

    [30090, 'lpad', True, False, 'VARCHAR', ['VARCHAR', 'INT', 'VARCHAR'], 'StringFunctions::lpad',
     'StringFunctions::pad_prepare', 'StringFunctions::pad_close'],
    [30100, 'rpad', True, False, 'VARCHAR', ['VARCHAR', 'INT', 'VARCHAR'], 'StringFunctions::rpad',
     'StringFunctions::pad_prepare', 'StringFunctions::pad_close'],

    [30110, 'append_trailing_char_if_absent', True, True, 'VARCHAR', ['VARCHAR', 'VARCHAR'],
     'StringFunctions::append_trailing_char_if_absent'],

    [30120, 'length', True, False, 'INT', ['VARCHAR'], 'StringFunctions::length'],
    [30130, 'char_length', True, False, 'INT', ['VARCHAR'], 'StringFunctions::utf8_length'],
    [30131, 'character_length', True, False, 'INT', ['VARCHAR'], 'StringFunctions::utf8_length'],

    [30140, 'lower', True, False, 'VARCHAR', ['VARCHAR'], 'StringFunctions::lower'],
    [30141, 'lcase', True, False, 'VARCHAR', ['VARCHAR'], 'StringFunctions::lower'],

    [30150, 'upper', True, False, 'VARCHAR', ['VARCHAR'], 'StringFunctions::upper'],
    [30151, 'ucase', True, False, 'VARCHAR', ['VARCHAR'], 'StringFunctions::upper'],

    [30160, 'reverse', True, False, 'VARCHAR', ['VARCHAR'], 'StringFunctions::reverse'],

    [30170, 'trim', True, False, 'VARCHAR', ['VARCHAR'], 'StringFunctions::trim',
     'StringFunctions::trim_prepare', 'StringFunctions::trim_close'],
    [30171, 'trim', True, False, 'VARCHAR', ['VARCHAR', 'VARCHAR'], 'StringFunctions::trim',
     'StringFunctions::trim_prepare', 'StringFunctions::trim_close'],
    [30180, 'ltrim', True, False, 'VARCHAR', ['VARCHAR'], 'StringFunctions::ltrim',
     'StringFunctions::trim_prepare', 'StringFunctions::trim_close'],
    [30181, 'ltrim', True, False, 'VARCHAR', ['VARCHAR', 'VARCHAR'], 'StringFunctions::ltrim',
     'StringFunctions::trim_prepare', 'StringFunctions::trim_close'],
    [30190, 'rtrim', True, False, 'VARCHAR', ['VARCHAR'], 'StringFunctions::rtrim',
     'StringFunctions::trim_prepare', 'StringFunctions::trim_close'],
    [30191, 'rtrim', True, False, 'VARCHAR', ['VARCHAR', 'VARCHAR'], 'StringFunctions::rtrim',
     'StringFunctions::trim_prepare', 'StringFunctions::trim_close'],

    [30200, 'ascii', True, False, 'INT', ['VARCHAR'], 'StringFunctions::ascii'],
    [30500, 'char', True, False, 'VARCHAR', ['INT'], "StringFunctions::get_char"],
    [30210, 'instr', True, False, 'INT', ['VARCHAR', 'VARCHAR'], 'StringFunctions::instr'],
    [30220, 'locate', True, False, 'INT', ['VARCHAR', 'VARCHAR'], 'StringFunctions::locate'],
    [30221, 'locate', True, False, 'INT', ['VARCHAR', 'VARCHAR', 'INT'], 'StringFunctions::locate_pos'],

    [30250, 'concat', True, True, 'VARCHAR', ['VARCHAR', '...'], 'StringFunctions::concat',
     'StringFunctions::concat_prepare', 'StringFunctions::concat_close'],

    [30260, 'concat_ws', True, True, 'VARCHAR', ['VARCHAR', 'VARCHAR', '...'], 'StringFunctions::concat_ws'],
    [30270, 'find_in_set', True, False, 'INT', ['VARCHAR', 'VARCHAR'], 'StringFunctions::find_in_set'],
    [30310, 'split_part', True, True, 'VARCHAR', ['VARCHAR', 'VARCHAR', 'INT'], 'StringFunctions::split_part'],
    [30311, 'split', True, True, 'ARRAY_VARCHAR', ['VARCHAR', 'VARCHAR'], 'StringFunctions::split', 'StringFunctions::split_prepare', 'StringFunctions::split_close'],
    [30312, 'substring_index', True, False, 'VARCHAR', ['VARCHAR', 'VARCHAR', 'INT'], 'StringFunctions::substring_index'],
    # v1 is deprecated
    [30316, 'str_to_map', True, False, 'MAP_VARCHAR_VARCHAR', ['ARRAY_VARCHAR', 'VARCHAR'],
     'StringFunctions::str_to_map_v1'],
    [30317, 'str_to_map', True, False, 'MAP_VARCHAR_VARCHAR', ['VARCHAR', 'VARCHAR', 'VARCHAR'],
     'StringFunctions::str_to_map', 'StringFunctions::str_to_map_prepare', 'StringFunctions::str_to_map_close'],

    [30320, 'regexp_extract', True, False, 'VARCHAR', ['VARCHAR', 'VARCHAR', 'BIGINT'],
     'StringFunctions::regexp_extract',
     'StringFunctions::regexp_extract_prepare', 'StringFunctions::regexp_close'],
    [30321, 'regexp_extract_all', True, False, 'ARRAY_VARCHAR', ['VARCHAR', 'VARCHAR', 'BIGINT'],
     'StringFunctions::regexp_extract_all', 'StringFunctions::regexp_extract_prepare', 'StringFunctions::regexp_close'],
    [30330, 'regexp_replace', True, True, 'VARCHAR', ['VARCHAR', 'VARCHAR', 'VARCHAR'],
     'StringFunctions::regexp_replace',
     'StringFunctions::regexp_replace_prepare', 'StringFunctions::regexp_close'],
    # @Deprecated: 'replace_old' will be deleted in the future version, keep it just for compatible
    [30331, 'replace_old', True, True, 'VARCHAR', ['VARCHAR', 'VARCHAR', 'VARCHAR'], 'StringFunctions::regexp_replace',
     'StringFunctions::regexp_replace_prepare', 'StringFunctions::regexp_close'],
    [30332, 'replace', True, True, 'VARCHAR', ['VARCHAR', 'VARCHAR', 'VARCHAR'], 'StringFunctions::replace',
     'StringFunctions::replace_prepare', 'StringFunctions::replace_close'],
    [30400, "money_format", True, False, "VARCHAR", ["BIGINT"], "StringFunctions::money_format_bigint"],
    [30401, "money_format", True, False, "VARCHAR", ["LARGEINT"], "StringFunctions::money_format_largeint"],
    [30402, "money_format", True, False, "VARCHAR", ["DECIMALV2"], "StringFunctions::money_format_decimalv2val"],
    [304020, "money_format", True, False, "VARCHAR", ["DECIMAL32"],
     "StringFunctions::money_format_decimal<TYPE_DECIMAL32>"],
    [304021, "money_format", True, False, "VARCHAR", ["DECIMAL64"],
     "StringFunctions::money_format_decimal<TYPE_DECIMAL64>"],
    [304022, "money_format", True, False, "VARCHAR", ["DECIMAL128"],
     "StringFunctions::money_format_decimal<TYPE_DECIMAL128>"],
    [30403, "money_format", True, False, "VARCHAR", ["DOUBLE"], "StringFunctions::money_format_double"],

    [30410, 'parse_url', True, False, 'VARCHAR', ['VARCHAR', 'VARCHAR'], 'StringFunctions::parse_url',
     'StringFunctions::parse_url_prepare', 'StringFunctions::parse_url_close'],
    [30411, 'url_extract_parameter', True, False, 'VARCHAR', ['VARCHAR', 'VARCHAR'],
     'StringFunctions::url_extract_parameter',
     'StringFunctions::url_extract_parameter_prepare', 'StringFunctions::url_extract_parameter_close'],
    [30420, 'strcmp', True, False, 'INT', ['VARCHAR', 'VARCHAR'], 'StringFunctions::strcmp'],
    [30421, 'url_encode', True, False, 'VARCHAR', ['VARCHAR'], 'StringFunctions::url_encode'],
    [30422, 'url_decode', True, False, 'VARCHAR', ['VARCHAR'], 'StringFunctions::url_decode'],

    [30430, 'translate', True, False, 'VARCHAR', ['VARCHAR', 'VARCHAR', 'VARCHAR'], 'StringFunctions::translate',
     'StringFunctions::translate_prepare', 'StringFunctions::translate_close'],

    # Binary Functions
    # to_binary
    [30600, 'to_binary', True, True, 'VARBINARY', ['VARCHAR', 'VARCHAR'], 'BinaryFunctions::to_binary',
     'BinaryFunctions::to_binary_prepare', 'BinaryFunctions::to_binary_close'],
    [30601, 'to_binary', True, True, 'VARBINARY', ['VARCHAR'], 'BinaryFunctions::to_binary',
     'BinaryFunctions::to_binary_prepare', 'BinaryFunctions::to_binary_close'],
    # from_binary
    [30602, 'from_binary', True, True, 'VARCHAR', ['VARBINARY', 'VARCHAR'], 'BinaryFunctions::from_binary',
     'BinaryFunctions::from_binary_prepare', 'BinaryFunctions::from_binary_close'],
    [30603, 'from_binary', True, True, 'VARCHAR', ['VARBINARY'], 'BinaryFunctions::from_binary',
     'BinaryFunctions::from_binary_prepare', 'BinaryFunctions::from_binary_close'],

    # 50xxx: timestamp functions
    [50008, 'year', True, False, 'SMALLINT', ['DATE'], 'TimeFunctions::yearV3'],
    [50009, 'year', True, False, 'SMALLINT', ['DATETIME'], 'TimeFunctions::yearV2'],
    [50010, 'year', True, False, 'INT', ['DATETIME'], 'TimeFunctions::year'],
    [50018, 'month', True, False, 'TINYINT', ['DATE'], 'TimeFunctions::monthV3'],
    [50019, 'month', True, False, 'TINYINT', ['DATETIME'], 'TimeFunctions::monthV2'],
    [50020, 'month', True, False, 'INT', ['DATETIME'], 'TimeFunctions::month'],
    [50030, 'quarter', True, False, 'INT', ['DATETIME'], 'TimeFunctions::quarter'],
    [50040, 'dayofweek', True, False, 'INT', ['DATETIME'], 'TimeFunctions::day_of_week'],
    [50041, 'dayofweek_iso', True, False, 'INT', ['DATETIME'], 'TimeFunctions::day_of_week_iso'],
    [50050, 'to_date', True, False, 'DATE', ['DATETIME'], 'TimeFunctions::to_date'],
    [50051, 'date', True, False, 'DATE', ['DATETIME'], 'TimeFunctions::to_date'],
    [50052, 'to_tera_date', True, False, 'DATE', ['VARCHAR', 'VARCHAR'], 'TimeFunctions::to_tera_date', "TimeFunctions::to_tera_date_prepare", "TimeFunctions::to_tera_date_close"],
    [50053, 'to_tera_timestamp', True, False, 'DATETIME', ['VARCHAR', 'VARCHAR'], 'TimeFunctions::to_tera_timestamp', "TimeFunctions::to_tera_timestamp_prepare", "TimeFunctions::to_tera_timestamp_close"],

    [50057, 'day', True, False, 'TINYINT', ['DATE'], 'TimeFunctions::dayV3'],
    [50058, 'day', True, False, 'TINYINT', ['DATETIME'], 'TimeFunctions::dayV2'],
    [50059, 'dayofmonth', True, False, 'TINYINT', ['DATETIME'], 'TimeFunctions::dayV2'],
    [50060, 'dayofmonth', True, False, 'INT', ['DATETIME'], 'TimeFunctions::day'],
    [50061, 'day', True, False, 'INT', ['DATETIME'], 'TimeFunctions::day'],
    [50062, 'dayofyear', True, False, 'INT', ['DATETIME'], 'TimeFunctions::day_of_year'],
    [50063, 'weekofyear', True, False, 'INT', ['DATETIME'], 'TimeFunctions::week_of_year'],
    [50064, 'week', True, False, 'INT', ['DATETIME'], 'TimeFunctions::week_of_year_with_default_mode'],
    [50067, 'week', True, False, 'INT', ['DATETIME', 'INT'], 'TimeFunctions::week_of_year_with_mode'],
    [50068, 'week_iso', True, False, 'INT', ['DATETIME'], 'TimeFunctions::week_of_year_iso'],

    [50069, 'hour', True, False, 'TINYINT', ['DATETIME'], 'TimeFunctions::hourV2'],
    [50070, 'hour', True, False, 'INT', ['DATETIME'], 'TimeFunctions::hour'],
    [50079, 'minute', True, False, 'TINYINT', ['DATETIME'], 'TimeFunctions::minuteV2'],
    [50080, 'minute', True, False, 'INT', ['DATETIME'], 'TimeFunctions::minute'],
    [50089, 'second', True, False, 'TINYINT', ['DATETIME'], 'TimeFunctions::secondV2'],
    [50090, 'second', True, False, 'INT', ['DATETIME'], 'TimeFunctions::second'],

    [50110, 'years_add', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::years_add'],
    [50111, 'years_sub', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::years_sub'],
    [50115, 'quarters_add', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::quarters_add'],
    [50116, 'quarters_sub', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::quarters_sub'],
    [50120, 'months_add', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::months_add'],
    [50121, 'months_sub', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::months_sub'],
    [50122, 'add_months', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::months_add'],
    [50130, 'weeks_add', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::weeks_add'],
    [50131, 'weeks_sub', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::weeks_sub'],
    [50140, 'days_add', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::days_add'],
    [50141, 'days_sub', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::days_sub'],

    [50142, 'date_add', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::days_add'],
    [50143, 'date_sub', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::days_sub'],

    [50144, 'adddate', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::days_add'],
    [50145, 'subdate', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::days_sub'],

    [50150, 'hours_add', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::hours_add'],
    [50151, 'hours_sub', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::hours_sub'],
    [50160, 'minutes_add', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::minutes_add'],
    [50161, 'minutes_sub', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::minutes_sub'],
    [50170, 'seconds_add', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::seconds_add'],
    [50171, 'seconds_sub', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::seconds_sub'],
    [50175, 'milliseconds_add', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::millis_add'],
    [50176, 'milliseconds_sub', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::millis_sub'],
    [50180, 'microseconds_add', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::micros_add'],
    [50181, 'microseconds_sub', True, False, 'DATETIME', ['DATETIME', 'INT'], 'TimeFunctions::micros_sub'],
    [50190, 'years_diff', True, False, 'BIGINT', ['DATETIME', 'DATETIME'], 'TimeFunctions::years_diff'],
    [50191, 'months_diff', True, False, 'BIGINT', ['DATETIME', 'DATETIME'], 'TimeFunctions::months_diff'],
    [50192, 'weeks_diff', True, False, 'BIGINT', ['DATETIME', 'DATETIME'], 'TimeFunctions::weeks_diff'],
    [50193, 'days_diff', True, False, 'BIGINT', ['DATETIME', 'DATETIME'], 'TimeFunctions::days_diff'],
    [50194, 'hours_diff', True, False, 'BIGINT', ['DATETIME', 'DATETIME'], 'TimeFunctions::hours_diff'],
    [50195, 'minutes_diff', True, False, 'BIGINT', ['DATETIME', 'DATETIME'], 'TimeFunctions::minutes_diff'],
    [50196, 'seconds_diff', True, False, 'BIGINT', ['DATETIME', 'DATETIME'], 'TimeFunctions::seconds_diff'],
    [50197, 'datediff', True, False, 'INT', ['DATETIME', 'DATETIME'], 'TimeFunctions::date_diff'],
    [50198, 'timediff', True, False, 'TIME', ['DATETIME', 'DATETIME'], 'TimeFunctions::time_diff'],
    [50199, 'date_diff', True, False, 'BIGINT', ['VARCHAR', 'DATETIME', 'DATETIME'], 'TimeFunctions::datediff',
     "TimeFunctions::datediff_prepare", "TimeFunctions::datediff_close"],
    [50200, 'now', True, False, 'DATETIME', [], 'TimeFunctions::now'],
    [50201, 'current_timestamp', True, False, 'DATETIME', [], 'TimeFunctions::now'],
    [50202, 'localtime', True, False, 'DATETIME', [], 'TimeFunctions::now'],
    [50203, 'localtimestamp', True, False, 'DATETIME', [], 'TimeFunctions::now'],
    [50204, 'now', True, False, 'DATETIME', ['INT'], 'TimeFunctions::now'],
    [50210, 'curtime', True, False, 'TIME', [], 'TimeFunctions::curtime'],
    [50211, 'current_time', True, False, 'TIME', [], 'TimeFunctions::curtime'],
    [50220, 'curdate', True, False, 'DATE', [], 'TimeFunctions::curdate'],
    [50221, 'current_date', True, False, 'DATE', [], 'TimeFunctions::curdate'],
    [50230, 'from_days', True, False, 'DATE', ['INT'], 'TimeFunctions::from_days'],
    [50231, 'to_days', True, False, 'INT', ['DATE'], 'TimeFunctions::to_days'],
    [50241, 'date_format', True, False, 'VARCHAR', ['DATETIME', 'VARCHAR'], 'TimeFunctions::datetime_format',
     'TimeFunctions::format_prepare', 'TimeFunctions::format_close'],
    [50242, 'date_format', True, False, 'VARCHAR', ['DATE', 'VARCHAR'], 'TimeFunctions::date_format',
     'TimeFunctions::format_prepare', 'TimeFunctions::format_close'],
    [50245, 'milliseconds_diff', True, False, 'BIGINT', ['DATETIME', 'DATETIME'], 'TimeFunctions::milliseconds_diff'],

    # From string to DATE/DATETIME
    # the function will call by FE getStrToDateFunction, and is invisible to user
    [50240, 'str_to_date', True, False, 'DATETIME', ['VARCHAR', 'VARCHAR'], 'TimeFunctions::str_to_date',
     'TimeFunctions::str_to_date_prepare', 'TimeFunctions::str_to_date_close'],
    [50243, 'str2date', True, False, 'DATE', ['VARCHAR', 'VARCHAR'], 'TimeFunctions::str2date',
     'TimeFunctions::str_to_date_prepare', 'TimeFunctions::str_to_date_close'],

    # Joda Time parse & format
    [50244, 'str_to_jodatime', True, False, 'DATETIME', ['VARCHAR', 'VARCHAR'],
     'TimeFunctions::parse_jodatime', 'TimeFunctions::parse_joda_prepare', 'TimeFunctions::parse_joda_close'],

    [50260, 'jodatime_format', True, False, 'VARCHAR', ['DATETIME', 'VARCHAR'], 'TimeFunctions::jodadatetime_format',
     'TimeFunctions::jodatime_format_prepare', 'TimeFunctions::jodatime_format_close'],
    [50261, 'jodatime_format', True, False, 'VARCHAR', ['DATE', 'VARCHAR'], 'TimeFunctions::jodadate_format',
     'TimeFunctions::jodatime_format_prepare', 'TimeFunctions::jodatime_format_close'],

    [50262, 'to_iso8601', True, False, 'VARCHAR', ['DATETIME'], 'TimeFunctions::datetime_to_iso8601'],
    [50263, 'to_iso8601', True, False, 'VARCHAR', ['DATE'], 'TimeFunctions::date_to_iso8601'],
    [50250, 'time_to_sec', True, False, 'BIGINT', ['TIME'], 'TimeFunctions::time_to_sec'],

    # unix timestamp extended version to int64
    # be sure to put before int32 version, so fe will find signature in order.
    [50284, 'unix_timestamp', True, False, 'BIGINT', [], 'TimeFunctions::to_unix_for_now_64'],
    [50285, 'unix_timestamp', True, False, 'BIGINT', ['DATETIME'], 'TimeFunctions::to_unix_from_datetime_64'],
    [50286, 'unix_timestamp', True, False, 'BIGINT', ['DATE'], 'TimeFunctions::to_unix_from_date_64'],
    [50287, 'unix_timestamp', True, False, 'BIGINT', ['VARCHAR', 'VARCHAR'],
     'TimeFunctions::to_unix_from_datetime_with_format_64'],
    [50288, 'from_unixtime', True, False, 'VARCHAR', ['BIGINT'], 'TimeFunctions::from_unix_to_datetime_64'],
    [50289, 'from_unixtime', True, False, 'VARCHAR', ['BIGINT', 'VARCHAR'],
     'TimeFunctions::from_unix_to_datetime_with_format_64',
     'TimeFunctions::from_unix_prepare', 'TimeFunctions::from_unix_close'],
    [50290, 'from_unixtime_ms', True, False, 'VARCHAR', ['BIGINT'], 'TimeFunctions::from_unix_to_datetime_ms_64'],

    [50300, 'unix_timestamp', True, False, 'INT', [], 'TimeFunctions::to_unix_for_now_32'],
    [50301, 'unix_timestamp', True, False, 'INT', ['DATETIME'], 'TimeFunctions::to_unix_from_datetime_32'],
    [50302, 'unix_timestamp', True, False, 'INT', ['DATE'], 'TimeFunctions::to_unix_from_date_32'],
    [50303, 'unix_timestamp', True, False, 'INT', ['VARCHAR', 'VARCHAR'],
     'TimeFunctions::to_unix_from_datetime_with_format_32'],
    [50304, 'from_unixtime', True, False, 'VARCHAR', ['INT'], 'TimeFunctions::from_unix_to_datetime_32'],
    [50305, 'from_unixtime', True, False, 'VARCHAR', ['INT', 'VARCHAR'],
     'TimeFunctions::from_unix_to_datetime_with_format_32',
     'TimeFunctions::from_unix_prepare', 'TimeFunctions::from_unix_close'],

    [50310, 'dayname', True, False, 'VARCHAR', ['DATETIME'], 'TimeFunctions::day_name'],
    [50311, 'monthname', True, False, 'VARCHAR', ['DATETIME'], 'TimeFunctions::month_name'],
    [50320, 'convert_tz', True, False, 'DATETIME', ['DATETIME', 'VARCHAR', 'VARCHAR'], 'TimeFunctions::convert_tz',
     'TimeFunctions::convert_tz_prepare', 'TimeFunctions::convert_tz_close'],
    [50330, 'utc_timestamp', True, False, 'DATETIME', [], 'TimeFunctions::utc_timestamp'],
    [50331, 'utc_time', True, False, 'TIME', [], 'TimeFunctions::utc_time'],
    [50340, 'date_trunc', True, False, 'DATETIME', ['VARCHAR', 'DATETIME'], 'TimeFunctions::datetime_trunc',
     'TimeFunctions::datetime_trunc_prepare', 'TimeFunctions::datetime_trunc_close'],
    [50350, 'date_trunc', True, False, 'DATE', ['VARCHAR', 'DATE'], 'TimeFunctions::date_trunc',
     'TimeFunctions::date_trunc_prepare', 'TimeFunctions::date_trunc_close'],
    [50360, 'timestamp', True, False, 'DATETIME', ['DATETIME'], 'TimeFunctions::timestamp'],
    [50370, 'date_slice', True, False, 'DATE', ['DATE', 'INT', 'VARCHAR'], 'TimeFunctions::time_slice',
     'TimeFunctions::time_slice_prepare', 'TimeFunctions::time_slice_close'],
    [50371, 'date_slice', True, False, 'DATE', ['DATE', 'INT', 'VARCHAR', 'VARCHAR'], 'TimeFunctions::time_slice',
     'TimeFunctions::time_slice_prepare', 'TimeFunctions::time_slice_close'],
    [50372, 'time_slice', True, False, 'DATETIME', ['DATETIME', 'INT', 'VARCHAR'], 'TimeFunctions::time_slice',
     'TimeFunctions::time_slice_prepare', 'TimeFunctions::time_slice_close'],
    [50373, 'time_slice', True, False, 'DATETIME', ['DATETIME', 'INT', 'VARCHAR', 'VARCHAR'],
     'TimeFunctions::time_slice', 'TimeFunctions::time_slice_prepare', 'TimeFunctions::time_slice_close'],
    [50400, 'next_day', True, False, 'DATE', ['DATETIME', 'VARCHAR'], 'TimeFunctions::next_day',
     'TimeFunctions::next_day_prepare', 'TimeFunctions::next_day_close'],
    [50401, 'previous_day', True, False, 'DATE', ['DATETIME', 'VARCHAR'], 'TimeFunctions::previous_day',
     'TimeFunctions::previous_day_prepare', 'TimeFunctions::previous_day_close'],
    [50402, 'last_day', True, False, 'DATE', ['DATETIME'], 'TimeFunctions::last_day'],
    [50403, 'last_day', True, False, 'DATE', ['DATETIME', 'VARCHAR'], 'TimeFunctions::last_day_with_format',
     'TimeFunctions::last_day_prepare', 'TimeFunctions::last_day_close'],
    [50501, 'makedate', True, False, 'DATE', ['INT', 'INT'], 'TimeFunctions::make_date'],

    # 60xxx: like predicate
    # important ref: LikePredicate.java, must keep name equals LikePredicate.Operator
    [60010, 'LIKE', True, False, 'BOOLEAN', ['VARCHAR', 'VARCHAR'], 'LikePredicate::like',
     'LikePredicate::like_prepare', 'LikePredicate::like_close'],
    [60020, 'REGEXP', True, False, 'BOOLEAN', ['VARCHAR', 'VARCHAR'], 'LikePredicate::regex',
     'LikePredicate::regex_prepare', 'LikePredicate::regex_close'],

    # 70xxx: condition functions
    # In fact, condition function will use condition express, not function. There just support
    # a function name for FE.
    # Why use express and not functions? I think one of the reasons is performance, we need to
    # execute all the children expressions ahead of time in function_call_expr, but condition
    # expressions may not need execute all children expressions if the condition be true ahead
    # of time
    [70100, 'if', True, False, 'BOOLEAN', ['BOOLEAN', 'BOOLEAN', 'BOOLEAN'], 'nullptr'],
    [70101, 'if', True, False, 'TINYINT', ['BOOLEAN', 'TINYINT', 'TINYINT'], 'nullptr'],
    [70102, 'if', True, False, 'SMALLINT', ['BOOLEAN', 'SMALLINT', 'SMALLINT'], 'nullptr'],
    [70103, 'if', True, False, 'INT', ['BOOLEAN', 'INT', 'INT'], 'nullptr'],
    [70104, 'if', True, False, 'BIGINT', ['BOOLEAN', 'BIGINT', 'BIGINT'], 'nullptr'],
    [70105, 'if', True, False, 'LARGEINT', ['BOOLEAN', 'LARGEINT', 'LARGEINT'], 'nullptr'],
    [70106, 'if', True, False, 'FLOAT', ['BOOLEAN', 'FLOAT', 'FLOAT'], 'nullptr'],
    [70107, 'if', True, False, 'DOUBLE', ['BOOLEAN', 'DOUBLE', 'DOUBLE'], 'nullptr'],
    [70108, 'if', True, False, 'DATETIME', ['BOOLEAN', 'DATETIME', 'DATETIME'], 'nullptr'],
    [70109, 'if', True, False, 'DATE', ['BOOLEAN', 'DATE', 'DATE'], 'nullptr'],
    [70110, 'if', True, False, 'DECIMALV2', ['BOOLEAN', 'DECIMALV2', 'DECIMALV2'], 'nullptr'],
    [701100, 'if', True, False, 'DECIMAL32', ['BOOLEAN', 'DECIMAL32', 'DECIMAL32'], 'nullptr'],
    [701101, 'if', True, False, 'DECIMAL64', ['BOOLEAN', 'DECIMAL64', 'DECIMAL64'], 'nullptr'],
    [701102, 'if', True, False, 'DECIMAL128', ['BOOLEAN', 'DECIMAL128', 'DECIMAL128'], 'nullptr'],
    [70111, 'if', True, False, 'VARCHAR', ['BOOLEAN', 'VARCHAR', 'VARCHAR'], 'nullptr'],
    [70112, 'if', True, False, 'BITMAP', ['BOOLEAN', 'BITMAP', 'BITMAP'], 'nullptr'],
    [70113, 'if', True, False, 'PERCENTILE', ['BOOLEAN', 'PERCENTILE', 'PERCENTILE'], 'nullptr'],
    [70114, 'if', True, False, 'HLL', ['BOOLEAN', 'HLL', 'HLL'], 'nullptr'],
    [70115, 'if', True, False, 'TIME', ['BOOLEAN', 'TIME', 'TIME'], 'nullptr'],
    [70116, 'if', True, False, 'ANY_ARRAY', ['BOOLEAN', 'ANY_ARRAY', 'ANY_ARRAY'], 'nullptr'],
    [70117, 'if', True, False, 'ANY_MAP', ['BOOLEAN', 'ANY_MAP', 'ANY_MAP'], 'nullptr'],
    [70118, 'if', True, False, 'ANY_STRUCT', ['BOOLEAN', 'ANY_STRUCT', 'ANY_STRUCT'], 'nullptr'],
    [70119, 'if', True, False, 'JSON', ['BOOLEAN', 'JSON', 'JSON'], 'nullptr'],

    [70200, 'ifnull', True, False, 'BOOLEAN', ['BOOLEAN', 'BOOLEAN'], 'nullptr'],
    [70201, 'ifnull', True, False, 'TINYINT', ['TINYINT', 'TINYINT'], 'nullptr'],
    [70202, 'ifnull', True, False, 'SMALLINT', ['SMALLINT', 'SMALLINT'], 'nullptr'],
    [70203, 'ifnull', True, False, 'INT', ['INT', 'INT'], 'nullptr'],
    [70204, 'ifnull', True, False, 'BIGINT', ['BIGINT', 'BIGINT'], 'nullptr'],
    [70205, 'ifnull', True, False, 'LARGEINT', ['LARGEINT', 'LARGEINT'], 'nullptr'],
    [70206, 'ifnull', True, False, 'FLOAT', ['FLOAT', 'FLOAT'], 'nullptr'],
    [70207, 'ifnull', True, False, 'DOUBLE', ['DOUBLE', 'DOUBLE'], 'nullptr'],
    [70208, 'ifnull', True, False, 'DATE', ['DATE', 'DATE'], 'nullptr'],
    [70209, 'ifnull', True, False, 'DATETIME', ['DATETIME', 'DATETIME'], 'nullptr'],
    [70210, 'ifnull', True, False, 'DECIMALV2', ['DECIMALV2', 'DECIMALV2'], 'nullptr'],
    [702100, 'ifnull', True, False, 'DECIMAL32', ['DECIMAL32', 'DECIMAL32'], 'nullptr'],
    [702101, 'ifnull', True, False, 'DECIMAL64', ['DECIMAL64', 'DECIMAL64'], 'nullptr'],
    [702102, 'ifnull', True, False, 'DECIMAL128', ['DECIMAL128', 'DECIMAL128'], 'nullptr'],
    [70211, 'ifnull', True, False, 'VARCHAR', ['VARCHAR', 'VARCHAR'], 'nullptr'],
    [70212, 'ifnull', True, False, 'BITMAP', ['BITMAP', 'BITMAP'], 'nullptr'],
    [70213, 'ifnull', True, False, 'PERCENTILE', ['PERCENTILE', 'PERCENTILE'], 'nullptr'],
    [70214, 'ifnull', True, False, 'HLL', ['HLL', 'HLL'], 'nullptr'],
    [70215, 'ifnull', True, False, 'TIME', ['TIME', 'TIME'], 'nullptr'],
    [70216, 'ifnull', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ANY_ARRAY'], 'nullptr'],
    [70217, 'ifnull', True, False, 'ANY_MAP', ['ANY_MAP', 'ANY_MAP'], 'nullptr'],
    [70218, 'ifnull', True, False, 'ANY_STRUCT', ['ANY_STRUCT', 'ANY_STRUCT'], 'nullptr'],
    [70219, 'ifnull', True, False, 'JSON', ['JSON', 'JSON'], 'nullptr'],

    [70300, 'nullif', True, False, 'BOOLEAN', ['BOOLEAN', 'BOOLEAN'], 'nullptr'],
    [70301, 'nullif', True, False, 'TINYINT', ['TINYINT', 'TINYINT'], 'nullptr'],
    [70302, 'nullif', True, False, 'SMALLINT', ['SMALLINT', 'SMALLINT'], 'nullptr'],
    [70303, 'nullif', True, False, 'INT', ['INT', 'INT'], 'nullptr'],
    [70304, 'nullif', True, False, 'BIGINT', ['BIGINT', 'BIGINT'], 'nullptr'],
    [70305, 'nullif', True, False, 'LARGEINT', ['LARGEINT', 'LARGEINT'], 'nullptr'],
    [70306, 'nullif', True, False, 'FLOAT', ['FLOAT', 'FLOAT'], 'nullptr'],
    [70307, 'nullif', True, False, 'DOUBLE', ['DOUBLE', 'DOUBLE'], 'nullptr'],
    [70308, 'nullif', True, False, 'DATE', ['DATE', 'DATE'], 'nullptr'],
    [70309, 'nullif', True, False, 'DATETIME', ['DATETIME', 'DATETIME'], 'nullptr'],
    [70310, 'nullif', True, False, 'DECIMALV2', ['DECIMALV2', 'DECIMALV2'], 'nullptr'],
    [703100, 'nullif', True, False, 'DECIMAL32', ['DECIMAL32', 'DECIMAL32'], 'nullptr'],
    [703101, 'nullif', True, False, 'DECIMAL64', ['DECIMAL64', 'DECIMAL64'], 'nullptr'],
    [703102, 'nullif', True, False, 'DECIMAL128', ['DECIMAL128', 'DECIMAL128'], 'nullptr'],
    [70311, 'nullif', True, False, 'VARCHAR', ['VARCHAR', 'VARCHAR'], 'nullptr'],
    [70312, 'nullif', True, False, 'BITMAP', ['BITMAP', 'BITMAP'], 'nullptr'],
    [70313, 'nullif', True, False, 'PERCENTILE', ['PERCENTILE', 'PERCENTILE'], 'nullptr'],
    [70314, 'nullif', True, False, 'HLL', ['HLL', 'HLL'], 'nullptr'],
    [70315, 'nullif', True, False, 'TIME', ['TIME', 'TIME'], 'nullptr'],
    [70316, 'nullif', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ANY_ARRAY'], 'nullptr'],
    [70317, 'nullif', True, False, 'ANY_MAP', ['ANY_MAP', 'ANY_MAP'], 'nullptr'],
    [70318, 'nullif', True, False, 'ANY_STRUCT', ['ANY_STRUCT', 'ANY_STRUCT'], 'nullptr'],
    [70319, 'nullif', True, False, 'JSON', ['JSON', 'JSON'], 'nullptr'],

    [70400, 'coalesce', True, False, 'BOOLEAN', ['BOOLEAN', '...'], 'nullptr'],
    [70401, 'coalesce', True, False, 'TINYINT', ['TINYINT', '...'], 'nullptr'],
    [70402, 'coalesce', True, False, 'SMALLINT', ['SMALLINT', '...'], 'nullptr'],
    [70403, 'coalesce', True, False, 'INT', ['INT', '...'], 'nullptr'],
    [70404, 'coalesce', True, False, 'BIGINT', ['BIGINT', '...'], 'nullptr'],
    [70405, 'coalesce', True, False, 'LARGEINT', ['LARGEINT', '...'], 'nullptr'],
    [70406, 'coalesce', True, False, 'FLOAT', ['FLOAT', '...'], 'nullptr'],
    [70407, 'coalesce', True, False, 'DOUBLE', ['DOUBLE', '...'], 'nullptr'],
    [70408, 'coalesce', True, False, 'DATETIME', ['DATETIME', '...'], 'nullptr'],
    [70409, 'coalesce', True, False, 'DATE', ['DATE', '...'], 'nullptr'],
    [70410, 'coalesce', True, False, 'DECIMALV2', ['DECIMALV2', '...'], 'nullptr'],
    [704100, 'coalesce', True, False, 'DECIMAL32', ['DECIMAL32', '...'], 'nullptr'],
    [704101, 'coalesce', True, False, 'DECIMAL64', ['DECIMAL64', '...'], 'nullptr'],
    [704102, 'coalesce', True, False, 'DECIMAL128', ['DECIMAL128', '...'], 'nullptr'],
    [70411, 'coalesce', True, False, 'VARCHAR', ['VARCHAR', '...'], 'nullptr'],
    [70412, 'coalesce', True, False, 'BITMAP', ['BITMAP', '...'], 'nullptr'],
    [70413, 'coalesce', True, False, 'PERCENTILE', ['PERCENTILE', '...'], 'nullptr'],
    [70414, 'coalesce', True, False, 'HLL', ['HLL', '...'], 'nullptr'],
    [70416, 'coalesce', True, False, 'TIME', ['TIME', '...'], 'nullptr'],
    [70417, 'coalesce', True, False, 'ANY_ARRAY', ['ANY_ARRAY', '...'], 'nullptr'],
    [70418, 'coalesce', True, False, 'ANY_MAP', ['ANY_MAP', '...'], 'nullptr'],
    [70419, 'coalesce', True, False, 'ANY_STRUCT', ['ANY_STRUCT', '...'], 'nullptr'],
    [70420, 'coalesce', True, False, 'JSON', ['JSON', '...'], 'nullptr'],

    [70415, 'esquery', True, False, 'BOOLEAN', ['VARCHAR', 'VARCHAR'], 'ESFunctions::match'],

    # hyperloglog function
    [80010, 'hll_cardinality', True, False, 'BIGINT', ['HLL'], 'HyperloglogFunctions::hll_cardinality'],
    [80011, 'hll_cardinality', True, False, 'BIGINT', ['VARCHAR'], 'HyperloglogFunctions::hll_cardinality_from_string'],
    [80020, 'hll_hash', True, False, 'HLL', ['VARCHAR'], 'HyperloglogFunctions::hll_hash'],
    [80030, 'hll_empty', True, False, 'HLL', [], 'HyperloglogFunctions::hll_empty'],

    [80040, 'hll_serialize', True, False, 'VARCHAR', ['HLL'], 'HyperloglogFunctions::hll_serialize'],
    [80041, 'hll_deserialize', True, False, 'HLL', ['VARCHAR'], 'HyperloglogFunctions::hll_deserialize'],

    # bitmap function
    [90010, 'to_bitmap', False, False, 'BITMAP', ['VARCHAR'], 'BitmapFunctions::to_bitmap<TYPE_VARCHAR>'],
    [90011, 'to_bitmap', False, False, 'BITMAP', ['BOOLEAN'], 'BitmapFunctions::to_bitmap<TYPE_BOOLEAN>'],
    [90012, 'to_bitmap', False, False, 'BITMAP', ['TINYINT'], 'BitmapFunctions::to_bitmap<TYPE_TINYINT>'],
    [90013, 'to_bitmap', False, False, 'BITMAP', ['SMALLINT'], 'BitmapFunctions::to_bitmap<TYPE_SMALLINT>'],
    [90014, 'to_bitmap', False, False, 'BITMAP', ['INT'], 'BitmapFunctions::to_bitmap<TYPE_INT>'],
    [90015, 'to_bitmap', False, False, 'BITMAP', ['BIGINT'], 'BitmapFunctions::to_bitmap<TYPE_BIGINT>'],
    [90016, 'to_bitmap', False, False, 'BITMAP', ['LARGEINT'], 'BitmapFunctions::to_bitmap<TYPE_LARGEINT>'],
    [90020, 'bitmap_hash', False, False, 'BITMAP', ['VARCHAR'], 'BitmapFunctions::bitmap_hash'],
    [90030, 'bitmap_count', True, False, 'BIGINT', ['BITMAP'], 'BitmapFunctions::bitmap_count'],
    [90040, 'bitmap_empty', False, False, 'BITMAP', [], 'BitmapFunctions::bitmap_empty'],
    [90050, 'bitmap_or', False, False, 'BITMAP', ['BITMAP', 'BITMAP'], 'BitmapFunctions::bitmap_or'],
    [90060, 'bitmap_and', False, False, 'BITMAP', ['BITMAP', 'BITMAP'], 'BitmapFunctions::bitmap_and'],
    [90070, 'bitmap_to_string', False, True, 'VARCHAR', ['BITMAP'], 'BitmapFunctions::bitmap_to_string'],
    [90080, 'bitmap_from_string', False, False, 'BITMAP', ['VARCHAR'], 'BitmapFunctions::bitmap_from_string'],
    [90090, 'bitmap_contains', False, False, 'BOOLEAN', ['BITMAP', 'BIGINT'], 'BitmapFunctions::bitmap_contains'],
    [90100, 'bitmap_has_any', False, False, 'BOOLEAN', ['BITMAP', 'BITMAP'], 'BitmapFunctions::bitmap_has_any'],
    [90200, 'bitmap_andnot', False, False, 'BITMAP', ['BITMAP', 'BITMAP'], 'BitmapFunctions::bitmap_andnot'],
    [90300, 'bitmap_xor', False, False, 'BITMAP', ['BITMAP', 'BITMAP'], 'BitmapFunctions::bitmap_xor'],
    [90400, 'bitmap_remove', False, False, 'BITMAP', ['BITMAP', 'BIGINT'], 'BitmapFunctions::bitmap_remove'],
    [90500, 'bitmap_to_array', False, False, 'ARRAY_BIGINT', ['BITMAP'], 'BitmapFunctions::bitmap_to_array'],
    [90600, 'bitmap_max', False, False, 'LARGEINT', ['BITMAP'], 'BitmapFunctions::bitmap_max'],
    [90700, 'bitmap_min', False, False, 'LARGEINT', ['BITMAP'], 'BitmapFunctions::bitmap_min'],
    [90800, 'base64_to_bitmap', False, False, 'BITMAP', ['VARCHAR'], 'BitmapFunctions::base64_to_bitmap'],
    [90801, 'bitmap_to_base64', False, True, 'VARCHAR', ['BITMAP'], 'BitmapFunctions::bitmap_to_base64'],
    [90900, 'array_to_bitmap', False, False, 'BITMAP', ['ARRAY_BIGINT'], 'BitmapFunctions::array_to_bitmap'],
    [91000, 'sub_bitmap', False, False, 'BITMAP', ['BITMAP', 'BIGINT', 'BIGINT'], 'BitmapFunctions::sub_bitmap'],
    [91001, 'bitmap_subset_limit', False, False, 'BITMAP', ['BITMAP', 'BIGINT', 'BIGINT'],
     'BitmapFunctions::bitmap_subset_limit'],
    [91002, 'bitmap_subset_in_range', False, False, 'BITMAP', ['BITMAP', 'BIGINT', 'BIGINT'],
     'BitmapFunctions::bitmap_subset_in_range'],
    [91003, 'bitmap_to_binary', False, True, 'VARBINARY', ['BITMAP'], 'BitmapFunctions::bitmap_to_binary'],
    [91004, 'bitmap_from_binary', False, False, 'BITMAP', ['VARBINARY'], 'BitmapFunctions::bitmap_from_binary'],

    # hash function
    [100010, 'murmur_hash3_32', True, False, 'INT', ['VARCHAR', '...'], 'HashFunctions::murmur_hash3_32'],
    [100021, 'xx_hash3_64', True, False, 'BIGINT', ['VARCHAR', '...'], 'HashFunctions::xx_hash3_64'],
    [100022, 'xx_hash3_128', True, False, 'LARGEINT', ['VARCHAR', '...'], 'HashFunctions::xx_hash3_128'],


    # Utility functions
    [100011, 'sleep', True, False, 'BOOLEAN', ['INT'], "UtilityFunctions::sleep"],
    [100012, 'version', True, False, 'VARCHAR', [], "UtilityFunctions::version"],
    [100013, 'current_version', True, False, 'VARCHAR', [], "UtilityFunctions::current_version"],
    [100014, 'last_query_id', True, False, 'VARCHAR', [], "UtilityFunctions::last_query_id"],
    [100015, 'uuid', True, False, 'VARCHAR', [], "UtilityFunctions::uuid"],
    [100016, 'uuid_numeric', True, False, 'LARGEINT', [], "UtilityFunctions::uuid_numeric"],
    [100017, 'assert_true', True, False, 'BOOLEAN', ['BOOLEAN'], 'UtilityFunctions::assert_true'],
    [100019, 'assert_true', True, False, 'BOOLEAN', ['BOOLEAN', "VARCHAR"], 'UtilityFunctions::assert_true'],
    [100018, 'host_name', True, False, 'VARCHAR', [], "UtilityFunctions::host_name"],
    [100020, 'get_query_profile', True, False, 'VARCHAR', ['VARCHAR'], "UtilityFunctions::get_query_profile"],

    # json string function
    [110000, "get_json_int", False, False, "INT", ["VARCHAR", "VARCHAR"], "JsonFunctions::get_json_int",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],
    [110001, "get_json_double", False, False, "DOUBLE", ["VARCHAR", "VARCHAR"], "JsonFunctions::get_json_double",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],
    [110002, "get_json_string", False, True, "VARCHAR", ["VARCHAR", "VARCHAR"], "JsonFunctions::get_json_string",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],
    [110012, "get_json_int", False, False, "INT", ["JSON", "VARCHAR"], "JsonFunctions::get_native_json_int",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],
    [110013, "get_json_double", False, False, "DOUBLE", ["JSON", "VARCHAR"], "JsonFunctions::get_native_json_double",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],
    [110014, "get_json_string", False, True, "VARCHAR", ["JSON", "VARCHAR"], "JsonFunctions::get_native_json_string",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],
    [110015, "get_json_object", False, True, "VARCHAR", ["JSON", "VARCHAR"], "JsonFunctions::get_native_json_string",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],
    [110020, "get_json_object", False, True, "VARCHAR", ["VARCHAR", "VARCHAR"], "JsonFunctions::get_json_string",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],

    # json type function
    [110003, "parse_json", False, False, "JSON", ["VARCHAR"], "JsonFunctions::parse_json"],
    [110004, "json_string", False, True, "VARCHAR", ["JSON"], "JsonFunctions::json_string"],
    [110005, "json_query", False, False, "JSON", ["JSON", "VARCHAR"], "JsonFunctions::json_query",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],
    # [110006, "json_value", "JSON", ["JSON", "VARCHAR"], "JsonFunctions::json_query"],
    [110007, "json_exists", False, False, "BOOLEAN", ["JSON", "VARCHAR"], "JsonFunctions::json_exists",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],
    [110008, "json_object", False, False, "JSON", ["JSON", "..."], "JsonFunctions::json_object"],
    [110009, "json_array", False, False, "JSON", ["JSON", "..."], "JsonFunctions::json_array"],
    [110010, "json_object", False, False, "JSON", [], "JsonFunctions::json_object_empty"],
    [110011, "json_array", False, False, "JSON", [], "JsonFunctions::json_array_empty"],
    [110016, "json_length", False, False, "INT", ["JSON"], "JsonFunctions::json_length"],
    [110017, "json_length", False, False, "INT", ["JSON", "VARCHAR"], "JsonFunctions::json_length",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],
    [110018, "json_keys", False, False, "JSON", ["JSON"], "JsonFunctions::json_keys"],
    [110019, "json_keys", False, False, "JSON", ["JSON", "VARCHAR"], "JsonFunctions::json_keys",
     "JsonFunctions::native_json_path_prepare", "JsonFunctions::native_json_path_close"],
    [110100, "to_json", False, False, "JSON", ["ANY_MAP"], "JsonFunctions::to_json"],
    [110101, "to_json", False, False, "JSON", ["ANY_STRUCT"], "JsonFunctions::to_json"],

    # aes and base64 function
    [120100, "aes_encrypt", False, False, "VARCHAR", ["VARCHAR", "VARCHAR"], "EncryptionFunctions::aes_encrypt"],
    [120110, "aes_decrypt", False, False, "VARCHAR", ["VARCHAR", "VARCHAR"], "EncryptionFunctions::aes_decrypt"],
    [120120, "from_base64", False, False, "VARCHAR", ["VARCHAR"], "EncryptionFunctions::from_base64"],
    [120121, "base64_decode_binary", False, False, "VARBINARY", ["VARCHAR"], "EncryptionFunctions::from_base64"],
    [120122, "base64_decode_string", False, False, "VARCHAR", ["VARCHAR"], "EncryptionFunctions::from_base64"],
    [120130, "to_base64", False, True, "VARCHAR", ["VARCHAR"], "EncryptionFunctions::to_base64"],
    [120140, "md5", False, False, "VARCHAR", ["VARCHAR"], "EncryptionFunctions::md5"],
    [120150, "md5sum", False, False, "VARCHAR", ["VARCHAR", "..."], "EncryptionFunctions::md5sum"],
    [120151, "md5sum_numeric", False, False, "LARGEINT", ["VARCHAR", "..."], "EncryptionFunctions::md5sum_numeric"],
    [120160, "sha2", False, False, "VARCHAR", ["VARCHAR", "INT"], "EncryptionFunctions::sha2",
     "EncryptionFunctions::sha2_prepare", "EncryptionFunctions::sha2_close"],
    [120161, "to_base64", False, True, "VARCHAR", ["VARBINARY"], "EncryptionFunctions::to_base64"],

    # geo function
    [120000, "ST_Point", False, False, "VARCHAR", ["DOUBLE", "DOUBLE"], "GeoFunctions::st_point"],
    [120001, "ST_X", False, False, "DOUBLE", ["VARCHAR"], "GeoFunctions::st_x"],
    [120002, "ST_Y", False, False, "DOUBLE", ["VARCHAR"], "GeoFunctions::st_y"],
    [120003, "ST_Distance_Sphere", False, False, "DOUBLE", ["DOUBLE", "DOUBLE", "DOUBLE", "DOUBLE"],
     "GeoFunctions::st_distance_sphere"],
    [120004, "ST_AsText", False, False, "VARCHAR", ["VARCHAR"], "GeoFunctions::st_as_wkt"],
    [120005, "ST_AsWKT", False, False, "VARCHAR", ["VARCHAR"], "GeoFunctions::st_as_wkt"],
    [120006, "ST_GeometryFromText", False, False, "VARCHAR", ["VARCHAR"], "GeoFunctions::st_from_wkt",
     "GeoFunctions::st_from_wkt_prepare", "GeoFunctions::st_from_wkt_close"],
    [120007, "ST_GeomFromText", False, False, "VARCHAR", ["VARCHAR"], "GeoFunctions::st_from_wkt",
     "GeoFunctions::st_from_wkt_prepare", "GeoFunctions::st_from_wkt_close"],
    [120008, "ST_LineFromText", False, False, "VARCHAR", ["VARCHAR"], "GeoFunctions::st_line",
     "GeoFunctions::st_line_prepare", "GeoFunctions::st_from_wkt_close"],
    [120009, "ST_LineStringFromText", False, False, "VARCHAR", ["VARCHAR"], "GeoFunctions::st_line",
     "GeoFunctions::st_line_prepare", "GeoFunctions::st_from_wkt_close"],
    [120010, "ST_Polygon", False, False, "VARCHAR", ["VARCHAR"], "GeoFunctions::st_polygon",
     "GeoFunctions::st_polygon_prepare", "GeoFunctions::st_from_wkt_close"],
    [120011, "ST_PolyFromText", False, False, "VARCHAR", ["VARCHAR"], "GeoFunctions::st_polygon",
     "GeoFunctions::st_polygon_prepare", "GeoFunctions::st_from_wkt_close"],
    [120012, "ST_PolygonFromText", False, False, "VARCHAR", ["VARCHAR"], "GeoFunctions::st_polygon",
     "GeoFunctions::st_polygon_prepare", "GeoFunctions::st_from_wkt_close"],
    [120013, "ST_Circle", False, False, "VARCHAR", ["DOUBLE", "DOUBLE", "DOUBLE"], "GeoFunctions::st_circle",
     "GeoFunctions::st_circle_prepare", "GeoFunctions::st_from_wkt_close"],
    [120014, "ST_Contains", False, False, "BOOLEAN", ["VARCHAR", "VARCHAR"], "GeoFunctions::st_contains",
     "GeoFunctions::st_contains_prepare", "GeoFunctions::st_contains_close"],

    # percentile function
    [130000, 'percentile_hash', True, False, 'PERCENTILE', ['DOUBLE'], 'PercentileFunctions::percentile_hash'],
    [130001, 'percentile_empty', True, False, 'PERCENTILE', [], 'PercentileFunctions::percentile_empty'],
    [130002, 'percentile_approx_raw', True, False, 'DOUBLE', ['PERCENTILE', 'DOUBLE'],
     'PercentileFunctions::percentile_approx_raw'],

    [140000, 'grouping_id', True, False, 'BIGINT', ['BIGINT'], 'GroupingSetsFunctions::grouping_id'],
    [140001, 'grouping', True, False, 'BIGINT', ['BIGINT'], 'GroupingSetsFunctions::grouping'],

    [150000, 'array_length', True, False, 'INT', ['ANY_ARRAY'], 'ArrayFunctions::array_length'],
    [150001, 'array_append', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ANY_ELEMENT'], 'ArrayFunctions::array_append'],
    [150002, 'array_contains', True, False, 'BOOLEAN', ['ANY_ARRAY', 'ANY_ELEMENT'], 'ArrayFunctions::array_contains'],

    # sum
    [150003, 'array_sum', True, False, 'BIGINT', ['ARRAY_BOOLEAN'], 'ArrayFunctions::array_sum<TYPE_BOOLEAN>'],
    [150004, 'array_sum', True, False, 'BIGINT', ['ARRAY_TINYINT'], 'ArrayFunctions::array_sum<TYPE_TINYINT>'],
    [150005, 'array_sum', True, False, 'BIGINT', ['ARRAY_SMALLINT'], 'ArrayFunctions::array_sum<TYPE_SMALLINT>'],
    [150006, 'array_sum', True, False, 'BIGINT', ['ARRAY_INT'], 'ArrayFunctions::array_sum<TYPE_INT>'],
    [150007, 'array_sum', True, False, 'BIGINT', ['ARRAY_BIGINT'], 'ArrayFunctions::array_sum<TYPE_BIGINT>'],
    [150008, 'array_sum', True, False, 'LARGEINT', ['ARRAY_LARGEINT'], 'ArrayFunctions::array_sum<TYPE_LARGEINT>'],
    [150011, 'array_sum', True, False, 'DECIMALV2', ['ARRAY_DECIMALV2'], 'ArrayFunctions::array_sum<TYPE_DECIMALV2>'],
    [150012, 'array_sum', True, False, 'DECIMAL128', ['ARRAY_DECIMAL32'], 'ArrayFunctions::array_sum<TYPE_DECIMAL32>'],
    [150013, 'array_sum', True, False, 'DECIMAL128', ['ARRAY_DECIMAL64'], 'ArrayFunctions::array_sum<TYPE_DECIMAL64>'],
    [150014, 'array_sum', True, False, 'DECIMAL128', ['ARRAY_DECIMAL128'],
     'ArrayFunctions::array_sum<TYPE_DECIMAL128>'],
    [150009, 'array_sum', True, False, 'DOUBLE', ['ARRAY_FLOAT'], 'ArrayFunctions::array_sum<TYPE_FLOAT>'],
    [150010, 'array_sum', True, False, 'DOUBLE', ['ARRAY_DOUBLE'], 'ArrayFunctions::array_sum<TYPE_DOUBLE>'],

    # avg
    [150023, 'array_avg', True, False, 'DOUBLE', ['ARRAY_BOOLEAN'], 'ArrayFunctions::array_avg<TYPE_BOOLEAN>'],
    [150024, 'array_avg', True, False, 'DOUBLE', ['ARRAY_TINYINT'], 'ArrayFunctions::array_avg<TYPE_TINYINT>'],
    [150025, 'array_avg', True, False, 'DOUBLE', ['ARRAY_SMALLINT'], 'ArrayFunctions::array_avg<TYPE_SMALLINT>'],
    [150026, 'array_avg', True, False, 'DOUBLE', ['ARRAY_INT'], 'ArrayFunctions::array_avg<TYPE_INT>'],
    [150027, 'array_avg', True, False, 'DOUBLE', ['ARRAY_BIGINT'], 'ArrayFunctions::array_avg<TYPE_BIGINT>'],
    [150028, 'array_avg', True, False, 'DOUBLE', ['ARRAY_LARGEINT'], 'ArrayFunctions::array_avg<TYPE_LARGEINT>'],
    [150031, 'array_avg', True, False, 'DECIMALV2', ['ARRAY_DECIMALV2'], 'ArrayFunctions::array_avg<TYPE_DECIMALV2>'],
    # [150032, 'array_avg', 'DATE',       ['ARRAY_DATE'],         'ArrayFunctions::array_avg_date'],
    # [150033, 'array_avg', 'DATETIME',   ['ARRAY_DATETIME'],     'ArrayFunctions::array_avg_datetime'],
    [150034, 'array_avg', True, False, 'DECIMAL128', ['ARRAY_DECIMAL32'], 'ArrayFunctions::array_avg<TYPE_DECIMAL32>'],
    [150035, 'array_avg', True, False, 'DECIMAL128', ['ARRAY_DECIMAL64'], 'ArrayFunctions::array_avg<TYPE_DECIMAL64>'],
    [150036, 'array_avg', True, False, 'DECIMAL128', ['ARRAY_DECIMAL128'],
     'ArrayFunctions::array_avg<TYPE_DECIMAL128>'],
    [150029, 'array_avg', True, False, 'DOUBLE', ['ARRAY_FLOAT'], 'ArrayFunctions::array_avg<TYPE_FLOAT>'],
    [150030, 'array_avg', True, False, 'DOUBLE', ['ARRAY_DOUBLE'], 'ArrayFunctions::array_avg<TYPE_DOUBLE>'],

    # min
    [150043, 'array_min', True, False, 'BOOLEAN', ['ARRAY_BOOLEAN'], 'ArrayFunctions::array_min<TYPE_BOOLEAN>'],
    [150044, 'array_min', True, False, 'TINYINT', ['ARRAY_TINYINT'], 'ArrayFunctions::array_min<TYPE_TINYINT>'],
    [150045, 'array_min', True, False, 'SMALLINT', ['ARRAY_SMALLINT'], 'ArrayFunctions::array_min<TYPE_SMALLINT>'],
    [150046, 'array_min', True, False, 'INT', ['ARRAY_INT'], 'ArrayFunctions::array_min<TYPE_INT>'],
    [150047, 'array_min', True, False, 'BIGINT', ['ARRAY_BIGINT'], 'ArrayFunctions::array_min<TYPE_BIGINT>'],
    [150048, 'array_min', True, False, 'LARGEINT', ['ARRAY_LARGEINT'], 'ArrayFunctions::array_min<TYPE_LARGEINT>'],
    [150049, 'array_min', True, False, 'FLOAT', ['ARRAY_FLOAT'], 'ArrayFunctions::array_min<TYPE_FLOAT>'],
    [150055, 'array_min', True, False, 'DECIMAL32', ['ARRAY_DECIMAL32'], 'ArrayFunctions::array_min<TYPE_DECIMAL32>'],
    [150056, 'array_min', True, False, 'DECIMAL64', ['ARRAY_DECIMAL64'], 'ArrayFunctions::array_min<TYPE_DECIMAL64>'],
    [150057, 'array_min', True, False, 'DECIMAL128', ['ARRAY_DECIMAL128'],
     'ArrayFunctions::array_min<TYPE_DECIMAL128>'],
    [150050, 'array_min', True, False, 'DOUBLE', ['ARRAY_DOUBLE'], 'ArrayFunctions::array_min<TYPE_DOUBLE>'],
    [150051, 'array_min', True, False, 'DECIMALV2', ['ARRAY_DECIMALV2'], 'ArrayFunctions::array_min<TYPE_DECIMALV2>'],
    [150052, 'array_min', True, False, 'DATE', ['ARRAY_DATE'], 'ArrayFunctions::array_min<TYPE_DATE>'],
    [150053, 'array_min', True, False, 'DATETIME', ['ARRAY_DATETIME'], 'ArrayFunctions::array_min<TYPE_DATETIME>'],
    [150054, 'array_min', True, False, 'VARCHAR', ['ARRAY_VARCHAR'], 'ArrayFunctions::array_min<TYPE_VARCHAR>'],

    # max
    [150063, 'array_max', True, False, 'BOOLEAN', ['ARRAY_BOOLEAN'], 'ArrayFunctions::array_max<TYPE_BOOLEAN>'],
    [150064, 'array_max', True, False, 'TINYINT', ['ARRAY_TINYINT'], 'ArrayFunctions::array_max<TYPE_TINYINT>'],
    [150065, 'array_max', True, False, 'SMALLINT', ['ARRAY_SMALLINT'], 'ArrayFunctions::array_max<TYPE_SMALLINT>'],
    [150066, 'array_max', True, False, 'INT', ['ARRAY_INT'], 'ArrayFunctions::array_max<TYPE_INT>'],
    [150067, 'array_max', True, False, 'BIGINT', ['ARRAY_BIGINT'], 'ArrayFunctions::array_max<TYPE_BIGINT>'],
    [150068, 'array_max', True, False, 'LARGEINT', ['ARRAY_LARGEINT'], 'ArrayFunctions::array_max<TYPE_LARGEINT>'],
    [150069, 'array_max', True, False, 'FLOAT', ['ARRAY_FLOAT'], 'ArrayFunctions::array_max<TYPE_FLOAT>'],
    [150075, 'array_max', True, False, 'DECIMAL32', ['ARRAY_DECIMAL32'], 'ArrayFunctions::array_max<TYPE_DECIMAL32>'],
    [150076, 'array_max', True, False, 'DECIMAL64', ['ARRAY_DECIMAL64'], 'ArrayFunctions::array_max<TYPE_DECIMAL64>'],
    [150077, 'array_max', True, False, 'DECIMAL128', ['ARRAY_DECIMAL128'],
     'ArrayFunctions::array_max<TYPE_DECIMAL128>'],
    [150070, 'array_max', True, False, 'DOUBLE', ['ARRAY_DOUBLE'], 'ArrayFunctions::array_max<TYPE_DOUBLE>'],
    [150071, 'array_max', True, False, 'DECIMALV2', ['ARRAY_DECIMALV2'], 'ArrayFunctions::array_max<TYPE_DECIMALV2>'],
    [150072, 'array_max', True, False, 'DATE', ['ARRAY_DATE'], 'ArrayFunctions::array_max<TYPE_DATE>'],
    [150073, 'array_max', True, False, 'DATETIME', ['ARRAY_DATETIME'], 'ArrayFunctions::array_max<TYPE_DATETIME>'],
    [150074, 'array_max', True, False, 'VARCHAR', ['ARRAY_VARCHAR'], 'ArrayFunctions::array_max<TYPE_VARCHAR>'],

    [150083, 'array_remove', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ANY_ELEMENT'], 'ArrayFunctions::array_remove'],
    [150084, 'array_position', True, False, 'INT', ['ANY_ARRAY', 'ANY_ELEMENT'], 'ArrayFunctions::array_position'],

    [150090, 'array_distinct', True, False, 'ARRAY_BOOLEAN', ['ARRAY_BOOLEAN'],
     'ArrayFunctions::array_distinct<TYPE_BOOLEAN>'],
    [150091, 'array_distinct', True, False, 'ARRAY_TINYINT', ['ARRAY_TINYINT'],
     'ArrayFunctions::array_distinct<TYPE_TINYINT>'],
    [150092, 'array_distinct', True, False, 'ARRAY_SMALLINT', ['ARRAY_SMALLINT'],
     'ArrayFunctions::array_distinct<TYPE_SMALLINT>'],
    [150093, 'array_distinct', True, False, 'ARRAY_INT', ['ARRAY_INT'], 'ArrayFunctions::array_distinct<TYPE_INT>'],
    [150094, 'array_distinct', True, False, 'ARRAY_BIGINT', ['ARRAY_BIGINT'],
     'ArrayFunctions::array_distinct<TYPE_BIGINT>'],
    [150095, 'array_distinct', True, False, 'ARRAY_LARGEINT', ['ARRAY_LARGEINT'],
     'ArrayFunctions::array_distinct<TYPE_LARGEINT>'],
    [150096, 'array_distinct', True, False, 'ARRAY_FLOAT', ['ARRAY_FLOAT'],
     'ArrayFunctions::array_distinct<TYPE_FLOAT>'],
    [150103, 'array_distinct', True, False, 'ARRAY_DECIMAL32', ['ARRAY_DECIMAL32'],
     'ArrayFunctions::array_distinct<TYPE_DECIMAL32>'],
    [150104, 'array_distinct', True, False, 'ARRAY_DECIMAL64', ['ARRAY_DECIMAL64'],
     'ArrayFunctions::array_distinct<TYPE_DECIMAL64>'],
    [150105, 'array_distinct', True, False, 'ARRAY_DECIMAL128', ['ARRAY_DECIMAL128'],
     'ArrayFunctions::array_distinct<TYPE_DECIMAL128>'],
    [150097, 'array_distinct', True, False, 'ARRAY_DOUBLE', ['ARRAY_DOUBLE'],
     'ArrayFunctions::array_distinct<TYPE_DOUBLE>'],
    [150098, 'array_distinct', True, False, 'ARRAY_VARCHAR', ['ARRAY_VARCHAR'],
     'ArrayFunctions::array_distinct<TYPE_VARCHAR>'],
    [150099, 'array_distinct', True, False, 'ARRAY_DECIMALV2', ['ARRAY_DECIMALV2'],
     'ArrayFunctions::array_distinct<TYPE_DECIMALV2>'],
    [150100, 'array_distinct', True, False, 'ARRAY_DATETIME', ['ARRAY_DATETIME'],
     'ArrayFunctions::array_distinct<TYPE_DATETIME>'],
    [150101, 'array_distinct', True, False, 'ARRAY_DATE', ['ARRAY_DATE'], 'ArrayFunctions::array_distinct<TYPE_DATE>'],

    [150107, 'array_distinct', True, False, 'ANY_ARRAY', ['ANY_ARRAY'], 'ArrayFunctions::array_distinct_any_type'],

    [150110, 'array_sort', True, False, 'ARRAY_BOOLEAN', ['ARRAY_BOOLEAN'], 'ArrayFunctions::array_sort<TYPE_BOOLEAN>'],
    [150111, 'array_sort', True, False, 'ARRAY_TINYINT', ['ARRAY_TINYINT'], 'ArrayFunctions::array_sort<TYPE_TINYINT>'],
    [150112, 'array_sort', True, False, 'ARRAY_SMALLINT', ['ARRAY_SMALLINT'],
     'ArrayFunctions::array_sort<TYPE_SMALLINT>'],
    [150113, 'array_sort', True, False, 'ARRAY_INT', ['ARRAY_INT'], 'ArrayFunctions::array_sort<TYPE_INT>'],
    [150114, 'array_sort', True, False, 'ARRAY_BIGINT', ['ARRAY_BIGINT'], 'ArrayFunctions::array_sort<TYPE_BIGINT>'],
    [150115, 'array_sort', True, False, 'ARRAY_LARGEINT', ['ARRAY_LARGEINT'],
     'ArrayFunctions::array_sort<TYPE_LARGEINT>'],
    [150116, 'array_sort', True, False, 'ARRAY_FLOAT', ['ARRAY_FLOAT'], 'ArrayFunctions::array_sort<TYPE_FLOAT>'],
    [150123, 'array_sort', True, False, 'ARRAY_DECIMAL32', ['ARRAY_DECIMAL32'],
     'ArrayFunctions::array_sort<TYPE_DECIMAL32>'],
    [150124, 'array_sort', True, False, 'ARRAY_DECIMAL64', ['ARRAY_DECIMAL64'],
     'ArrayFunctions::array_sort<TYPE_DECIMAL64>'],
    [150125, 'array_sort', True, False, 'ARRAY_DECIMAL128', ['ARRAY_DECIMAL128'],
     'ArrayFunctions::array_sort<TYPE_DECIMAL128>'],
    [150117, 'array_sort', True, False, 'ARRAY_DOUBLE', ['ARRAY_DOUBLE'], 'ArrayFunctions::array_sort<TYPE_DOUBLE>'],
    [150118, 'array_sort', True, False, 'ARRAY_VARCHAR', ['ARRAY_VARCHAR'], 'ArrayFunctions::array_sort<TYPE_VARCHAR>'],
    [150119, 'array_sort', True, False, 'ARRAY_DECIMALV2', ['ARRAY_DECIMALV2'],
     'ArrayFunctions::array_sort<TYPE_DECIMALV2>'],
    [150120, 'array_sort', True, False, 'ARRAY_DATETIME', ['ARRAY_DATETIME'],
     'ArrayFunctions::array_sort<TYPE_DATETIME>'],
    [150121, 'array_sort', True, False, 'ARRAY_DATE', ['ARRAY_DATE'], 'ArrayFunctions::array_sort<TYPE_DATE>'],
    [150122, 'array_sort', True, False, 'ARRAY_JSON', ['ARRAY_JSON'], 'ArrayFunctions::array_sort<TYPE_JSON>'],

    [150130, 'reverse', True, False, 'ARRAY_BOOLEAN', ['ARRAY_BOOLEAN'], 'ArrayFunctions::array_reverse<TYPE_BOOLEAN>'],
    [150131, 'reverse', True, False, 'ARRAY_TINYINT', ['ARRAY_TINYINT'], 'ArrayFunctions::array_reverse<TYPE_TINYINT>'],
    [150132, 'reverse', True, False, 'ARRAY_SMALLINT', ['ARRAY_SMALLINT'],
     'ArrayFunctions::array_reverse<TYPE_SMALLINT>'],
    [150133, 'reverse', True, False, 'ARRAY_INT', ['ARRAY_INT'], 'ArrayFunctions::array_reverse<TYPE_INT>'],
    [150134, 'reverse', True, False, 'ARRAY_BIGINT', ['ARRAY_BIGINT'], 'ArrayFunctions::array_reverse<TYPE_BIGINT>'],
    [150135, 'reverse', True, False, 'ARRAY_LARGEINT', ['ARRAY_LARGEINT'],
     'ArrayFunctions::array_reverse<TYPE_LARGEINT>'],
    [150136, 'reverse', True, False, 'ARRAY_FLOAT', ['ARRAY_FLOAT'], 'ArrayFunctions::array_reverse<TYPE_FLOAT>'],
    [150143, 'reverse', True, False, 'ARRAY_DECIMAL32', ['ARRAY_DECIMAL32'],
     'ArrayFunctions::array_reverse<TYPE_DECIMAL32>'],
    [150144, 'reverse', True, False, 'ARRAY_DECIMAL64', ['ARRAY_DECIMAL64'],
     'ArrayFunctions::array_reverse<TYPE_DECIMAL64>'],
    [150145, 'reverse', True, False, 'ARRAY_DECIMAL128', ['ARRAY_DECIMAL128'],
     'ArrayFunctions::array_reverse<TYPE_DECIMAL128>'],
    [150137, 'reverse', True, False, 'ARRAY_DOUBLE', ['ARRAY_DOUBLE'], 'ArrayFunctions::array_reverse<TYPE_DOUBLE>'],
    [150138, 'reverse', True, False, 'ARRAY_VARCHAR', ['ARRAY_VARCHAR'], 'ArrayFunctions::array_reverse<TYPE_VARCHAR>'],
    [150139, 'reverse', True, False, 'ARRAY_DECIMALV2', ['ARRAY_DECIMALV2'],
     'ArrayFunctions::array_reverse<TYPE_DECIMALV2>'],
    [150140, 'reverse', True, False, 'ARRAY_DATETIME', ['ARRAY_DATETIME'],
     'ArrayFunctions::array_reverse<TYPE_DATETIME>'],
    [150141, 'reverse', True, False, 'ARRAY_DATE', ['ARRAY_DATE'], 'ArrayFunctions::array_reverse<TYPE_DATE>'],
    [150142, 'reverse', True, False, 'ARRAY_JSON', ['ARRAY_JSON'], 'ArrayFunctions::array_reverse<TYPE_JSON>'],

    [150146, 'reverse', True, False, 'ANY_ARRAY', ['ANY_ARRAY'], 'ArrayFunctions::array_reverse_any_types'],

    [150150, 'array_join', True, True, 'VARCHAR', ['ARRAY_VARCHAR', 'VARCHAR'], 'ArrayFunctions::array_join'],
    [150151, 'array_join', True, True, 'VARCHAR', ['ARRAY_VARCHAR', 'VARCHAR', 'VARCHAR'],
     'ArrayFunctions::array_join'],

    [150160, 'array_difference', True, False, 'ARRAY_BIGINT', ['ARRAY_BOOLEAN'],
     'ArrayFunctions::array_difference<TYPE_BOOLEAN>'],
    [150161, 'array_difference', True, False, 'ARRAY_BIGINT', ['ARRAY_TINYINT'],
     'ArrayFunctions::array_difference<TYPE_TINYINT>'],
    [150162, 'array_difference', True, False, 'ARRAY_BIGINT', ['ARRAY_SMALLINT'],
     'ArrayFunctions::array_difference<TYPE_SMALLINT>'],
    [150163, 'array_difference', True, False, 'ARRAY_BIGINT', ['ARRAY_INT'],
     'ArrayFunctions::array_difference<TYPE_INT>'],
    [150164, 'array_difference', True, False, 'ARRAY_BIGINT', ['ARRAY_BIGINT'],
     'ArrayFunctions::array_difference<TYPE_BIGINT>'],
    [150165, 'array_difference', True, False, 'ARRAY_LARGEINT', ['ARRAY_LARGEINT'],
     'ArrayFunctions::array_difference<TYPE_LARGEINT>'],
    [150166, 'array_difference', True, False, 'ARRAY_DOUBLE', ['ARRAY_FLOAT'],
     'ArrayFunctions::array_difference<TYPE_FLOAT>'],
    [150168, 'array_difference', True, False, 'ARRAY_DECIMALV2', ['ARRAY_DECIMALV2'],
     'ArrayFunctions::array_difference<TYPE_DECIMALV2>'],
    [1501681, 'array_difference', True, False, 'ARRAY_DECIMAL32', ['ARRAY_DECIMAL32'],
     'ArrayFunctions::array_difference<TYPE_DECIMAL32>'],
    [1501682, 'array_difference', True, False, 'ARRAY_DECIMAL64', ['ARRAY_DECIMAL64'],
     'ArrayFunctions::array_difference<TYPE_DECIMAL64>'],
    [1501683, 'array_difference', True, False, 'ARRAY_DECIMAL128', ['ARRAY_DECIMAL128'],
     'ArrayFunctions::array_difference<TYPE_DECIMAL128>'],
    [150167, 'array_difference', True, False, 'ARRAY_DOUBLE', ['ARRAY_DOUBLE'],
     'ArrayFunctions::array_difference<TYPE_DOUBLE>'],

    # @Deprecated: these will be deleted in the future version, keep these just for compatible
    [150170, 'array_slice', True, False, 'ARRAY_DATE', ['ARRAY_DATE', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150171, 'array_slice', True, False, 'ARRAY_DATETIME', ['ARRAY_DATETIME', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150172, 'array_slice', True, False, 'ARRAY_BOOLEAN', ['ARRAY_BOOLEAN', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150173, 'array_slice', True, False, 'ARRAY_TINYINT', ['ARRAY_TINYINT', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150174, 'array_slice', True, False, 'ARRAY_SMALLINT', ['ARRAY_SMALLINT', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150175, 'array_slice', True, False, 'ARRAY_INT', ['ARRAY_INT', 'BIGINT', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150176, 'array_slice', True, False, 'ARRAY_BIGINT', ['ARRAY_BIGINT', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150177, 'array_slice', True, False, 'ARRAY_LARGEINT', ['ARRAY_LARGEINT', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150178, 'array_slice', True, False, 'ARRAY_FLOAT', ['ARRAY_FLOAT', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150184, 'array_slice', True, False, 'ARRAY_DECIMAL32', ['ARRAY_DECIMAL32', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150185, 'array_slice', True, False, 'ARRAY_DECIMAL64', ['ARRAY_DECIMAL64', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150186, 'array_slice', True, False, 'ARRAY_DECIMAL128', ['ARRAY_DECIMAL128', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150179, 'array_slice', True, False, 'ARRAY_DOUBLE', ['ARRAY_DOUBLE', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150180, 'array_slice', True, False, 'ARRAY_DECIMALV2', ['ARRAY_DECIMALV2', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150181, 'array_slice', True, False, 'ARRAY_VARCHAR', ['ARRAY_VARCHAR', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150182, 'array_slice', True, False, 'ARRAY_JSON', ['ARRAY_JSON', 'BIGINT', 'BIGINT'],
     'ArrayFunctions::array_slice'],

    [150183, 'array_slice', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'BIGINT', 'BIGINT'], 'ArrayFunctions::array_slice'],

    # @Deprecated: these will be deleted in the future version, keep these just for compatible
    [150190, 'array_concat', True, False, 'ARRAY_DATE', ['ARRAY_DATE', "..."], 'ArrayFunctions::concat'],
    [150191, 'array_concat', True, False, 'ARRAY_DATETIME', ['ARRAY_DATETIME', "..."], 'ArrayFunctions::concat'],
    [150192, 'array_concat', True, False, 'ARRAY_BOOLEAN', ['ARRAY_BOOLEAN', "..."], 'ArrayFunctions::concat'],
    [150193, 'array_concat', True, False, 'ARRAY_TINYINT', ['ARRAY_TINYINT', "..."], 'ArrayFunctions::concat'],
    [150194, 'array_concat', True, False, 'ARRAY_SMALLINT', ['ARRAY_SMALLINT', "..."], 'ArrayFunctions::concat'],
    [150195, 'array_concat', True, False, 'ARRAY_INT', ['ARRAY_INT', "..."], 'ArrayFunctions::concat'],
    [150196, 'array_concat', True, False, 'ARRAY_BIGINT', ['ARRAY_BIGINT', "..."], 'ArrayFunctions::concat'],
    [150197, 'array_concat', True, False, 'ARRAY_LARGEINT', ['ARRAY_LARGEINT', "..."], 'ArrayFunctions::concat'],
    [150198, 'array_concat', True, False, 'ARRAY_FLOAT', ['ARRAY_FLOAT', "..."], 'ArrayFunctions::concat'],
    [150204, 'array_concat', True, False, 'ARRAY_DECIMAL32', ['ARRAY_DECIMAL32', "..."], 'ArrayFunctions::concat'],
    [150205, 'array_concat', True, False, 'ARRAY_DECIMAL64', ['ARRAY_DECIMAL64', "..."], 'ArrayFunctions::concat'],
    [150206, 'array_concat', True, False, 'ARRAY_DECIMAL128', ['ARRAY_DECIMAL128', "..."], 'ArrayFunctions::concat'],
    [150199, 'array_concat', True, False, 'ARRAY_DOUBLE', ['ARRAY_DOUBLE', "..."], 'ArrayFunctions::concat'],
    [150200, 'array_concat', True, False, 'ARRAY_DECIMALV2', ['ARRAY_DECIMALV2', "..."], 'ArrayFunctions::concat'],
    [150201, 'array_concat', True, True, 'ARRAY_VARCHAR', ['ARRAY_VARCHAR', "..."], 'ArrayFunctions::concat'],
    [150202, 'array_concat', True, False, 'ARRAY_JSON', ['ARRAY_JSON', "..."], 'ArrayFunctions::concat'],

    [150203, 'array_concat', True, True, 'ANY_ARRAY', ['ANY_ARRAY', "..."], 'ArrayFunctions::concat'],

    [150210, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_DATE', 'ARRAY_DATE'],
     'ArrayFunctions::array_overlap<TYPE_DATE>'],
    [150211, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_DATETIME', 'ARRAY_DATETIME'],
     'ArrayFunctions::array_overlap<TYPE_DATETIME>'],
    [150212, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_BOOLEAN', 'ARRAY_BOOLEAN'],
     'ArrayFunctions::array_overlap<TYPE_BOOLEAN>'],
    [150213, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_TINYINT', 'ARRAY_TINYINT'],
     'ArrayFunctions::array_overlap<TYPE_TINYINT>'],
    [150214, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_SMALLINT', 'ARRAY_SMALLINT'],
     'ArrayFunctions::array_overlap<TYPE_SMALLINT>'],
    [150215, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_INT', 'ARRAY_INT'],
     'ArrayFunctions::array_overlap<TYPE_INT>'],
    [150216, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_BIGINT', 'ARRAY_BIGINT'],
     'ArrayFunctions::array_overlap<TYPE_BIGINT>'],
    [150217, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_LARGEINT', 'ARRAY_LARGEINT'],
     'ArrayFunctions::array_overlap<TYPE_LARGEINT>'],
    [150218, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_FLOAT', 'ARRAY_FLOAT'],
     'ArrayFunctions::array_overlap<TYPE_FLOAT>'],
    [150222, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_DECIMAL32', 'ARRAY_DECIMAL32'],
     'ArrayFunctions::array_overlap<TYPE_DECIMAL32>'],
    [150223, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_DECIMAL64', 'ARRAY_DECIMAL64'],
     'ArrayFunctions::array_overlap<TYPE_DECIMAL64>'],
    [150224, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_DECIMAL128', 'ARRAY_DECIMAL128'],
     'ArrayFunctions::array_overlap<TYPE_DECIMAL128>'],
    [150219, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_DOUBLE', 'ARRAY_DOUBLE'],
     'ArrayFunctions::array_overlap<TYPE_DOUBLE>'],
    [150220, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_DECIMALV2', 'ARRAY_DECIMALV2'],
     'ArrayFunctions::array_overlap<TYPE_DECIMALV2>'],
    [150221, 'arrays_overlap', True, False, 'BOOLEAN', ['ARRAY_VARCHAR', 'ARRAY_VARCHAR'],
     'ArrayFunctions::array_overlap<TYPE_VARCHAR>'],

    [150226, 'arrays_overlap', True, False, 'BOOLEAN', ['ANY_ARRAY', 'ANY_ARRAY'],
     'ArrayFunctions::array_contains_any'],

    [150230, 'array_intersect', True, False, 'ARRAY_DATE', ['ARRAY_DATE', "..."],
     'ArrayFunctions::array_intersect<TYPE_DATE>'],
    [150231, 'array_intersect', True, False, 'ARRAY_DATETIME', ['ARRAY_DATETIME', "..."],
     'ArrayFunctions::array_intersect<TYPE_DATETIME>'],
    [150232, 'array_intersect', True, False, 'ARRAY_BOOLEAN', ['ARRAY_BOOLEAN', "..."],
     'ArrayFunctions::array_intersect<TYPE_BOOLEAN>'],
    [150233, 'array_intersect', True, False, 'ARRAY_TINYINT', ['ARRAY_TINYINT', "..."],
     'ArrayFunctions::array_intersect<TYPE_TINYINT>'],
    [150234, 'array_intersect', True, False, 'ARRAY_SMALLINT', ['ARRAY_SMALLINT', "..."],
     'ArrayFunctions::array_intersect<TYPE_SMALLINT>'],
    [150235, 'array_intersect', True, False, 'ARRAY_INT', ['ARRAY_INT', "..."],
     'ArrayFunctions::array_intersect<TYPE_INT>'],
    [150236, 'array_intersect', True, False, 'ARRAY_BIGINT', ['ARRAY_BIGINT', "..."],
     'ArrayFunctions::array_intersect<TYPE_BIGINT>'],
    [150237, 'array_intersect', True, False, 'ARRAY_LARGEINT', ['ARRAY_LARGEINT', "..."],
     'ArrayFunctions::array_intersect<TYPE_LARGEINT>'],
    [150238, 'array_intersect', True, False, 'ARRAY_FLOAT', ['ARRAY_FLOAT', "..."],
     'ArrayFunctions::array_intersect<TYPE_FLOAT>'],
    [150242, 'array_intersect', True, False, 'ARRAY_DECIMAL32', ['ARRAY_DECIMAL32', "..."],
     'ArrayFunctions::array_intersect<TYPE_DECIMAL32>'],
    [150243, 'array_intersect', True, False, 'ARRAY_DECIMAL64', ['ARRAY_DECIMAL64', "..."],
     'ArrayFunctions::array_intersect<TYPE_DECIMAL64>'],
    [150244, 'array_intersect', True, False, 'ARRAY_DECIMAL128', ['ARRAY_DECIMAL128', "..."],
     'ArrayFunctions::array_intersect<TYPE_DECIMAL128>'],
    [150239, 'array_intersect', True, False, 'ARRAY_DOUBLE', ['ARRAY_DOUBLE', "..."],
     'ArrayFunctions::array_intersect<TYPE_DOUBLE>'],
    [150240, 'array_intersect', True, False, 'ARRAY_DECIMALV2', ['ARRAY_DECIMALV2', "..."],
     'ArrayFunctions::array_intersect<TYPE_DECIMALV2>'],
    [150241, 'array_intersect', True, False, 'ARRAY_VARCHAR', ['ARRAY_VARCHAR', "..."],
     'ArrayFunctions::array_intersect<TYPE_VARCHAR>'],

    [150246, 'array_intersect', True, False, 'ANY_ARRAY', ['ANY_ARRAY', "..."],
     'ArrayFunctions::array_intersect_any_type'],

    # @Deprecated: these will be deleted in the future version, keep these just for compatible
    [150250, 'array_slice', True, False, 'ARRAY_DATE', ['ARRAY_DATE', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150251, 'array_slice', True, False, 'ARRAY_DATETIME', ['ARRAY_DATETIME', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150252, 'array_slice', True, False, 'ARRAY_BOOLEAN', ['ARRAY_BOOLEAN', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150253, 'array_slice', True, False, 'ARRAY_TINYINT', ['ARRAY_TINYINT', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150254, 'array_slice', True, False, 'ARRAY_SMALLINT', ['ARRAY_SMALLINT', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150255, 'array_slice', True, False, 'ARRAY_INT', ['ARRAY_INT', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150256, 'array_slice', True, False, 'ARRAY_BIGINT', ['ARRAY_BIGINT', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150257, 'array_slice', True, False, 'ARRAY_LARGEINT', ['ARRAY_LARGEINT', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150258, 'array_slice', True, False, 'ARRAY_FLOAT', ['ARRAY_FLOAT', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150264, 'array_slice', True, False, 'ARRAY_DECIMAL32', ['ARRAY_DECIMAL32', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150265, 'array_slice', True, False, 'ARRAY_DECIMAL64', ['ARRAY_DECIMAL64', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150266, 'array_slice', True, False, 'ARRAY_DECIMAL128', ['ARRAY_DECIMAL128', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150259, 'array_slice', True, False, 'ARRAY_DOUBLE', ['ARRAY_DOUBLE', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150260, 'array_slice', True, False, 'ARRAY_DECIMALV2', ['ARRAY_DECIMALV2', 'BIGINT'],
     'ArrayFunctions::array_slice'],
    [150261, 'array_slice', True, False, 'ARRAY_VARCHAR', ['ARRAY_VARCHAR', 'BIGINT'], 'ArrayFunctions::array_slice'],
    [150262, 'array_slice', True, False, 'ARRAY_JSON', ['ARRAY_JSON', 'BIGINT'], 'ArrayFunctions::array_slice'],

    [150263, 'array_slice', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'BIGINT'], 'ArrayFunctions::array_slice'],

    [150270, 'array_cum_sum', True, False, 'ARRAY_BIGINT', ['ARRAY_BIGINT'], 'ArrayFunctions::array_cum_sum_bigint'],
    [150271, 'array_cum_sum', True, False, 'ARRAY_DOUBLE', ['ARRAY_DOUBLE'], 'ArrayFunctions::array_cum_sum_double'],

    # reserve 150281
    [150282, 'array_contains_all', True, False, 'BOOLEAN', ['ANY_ARRAY', 'ANY_ARRAY'], 'ArrayFunctions::array_contains_all'],

    [150300, 'array_filter', True, False, 'ANY_ARRAY',   ['ANY_ARRAY', 'ARRAY_BOOLEAN'],   'ArrayFunctions::array_filter'],
    [150301, 'all_match', True, False, 'BOOLEAN',   ['ARRAY_BOOLEAN'],   'ArrayFunctions::all_match'],
    [150302, 'any_match', True, False, 'BOOLEAN',   ['ARRAY_BOOLEAN'],   'ArrayFunctions::any_match'],

    [150311, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_BOOLEAN'],
     'ArrayFunctions::array_sortby<TYPE_BOOLEAN>'],
    [150312, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_TINYINT'],
     'ArrayFunctions::array_sortby<TYPE_TINYINT>'],
    [150313, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_SMALLINT'],
     'ArrayFunctions::array_sortby<TYPE_SMALLINT>'],
    [150314, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_INT'],
     'ArrayFunctions::array_sortby<TYPE_INT>'],
    [150315, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_BIGINT'],
     'ArrayFunctions::array_sortby<TYPE_BIGINT>'],
    [150316, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_LARGEINT'],
     'ArrayFunctions::array_sortby<TYPE_LARGEINT>'],
    [150317, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_FLOAT'],
     'ArrayFunctions::array_sortby<TYPE_FLOAT>'],
    [150324, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_DECIMAL32'],
     'ArrayFunctions::array_sortby<TYPE_DECIMAL32>'],
    [150325, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_DECIMAL64'],
     'ArrayFunctions::array_sortby<TYPE_DECIMAL64>'],
    [150326, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_DECIMAL128'],
     'ArrayFunctions::array_sortby<TYPE_DECIMAL128>'],
    [150318, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_DOUBLE'],
     'ArrayFunctions::array_sortby<TYPE_DOUBLE>'],
    [150319, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_VARCHAR'],
     'ArrayFunctions::array_sortby<TYPE_VARCHAR>'],
    [150320, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_DECIMALV2'],
     'ArrayFunctions::array_sortby<TYPE_DECIMALV2>'],
    [150321, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_DATETIME'],
     'ArrayFunctions::array_sortby<TYPE_DATETIME>'],
    [150322, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_DATE'],
     'ArrayFunctions::array_sortby<TYPE_DATE>'],
    [150323, 'array_sortby', True, False, 'ANY_ARRAY', ['ANY_ARRAY', 'ARRAY_JSON'],
     'ArrayFunctions::array_sortby<TYPE_JSON>'],

    [150330, 'array_generate', True, False, 'ARRAY_TINYINT', ['TINYINT', 'TINYINT', 'TINYINT'],
     "ArrayFunctions::array_generate<TYPE_TINYINT>"],
    [150331, 'array_generate', True, False, 'ARRAY_SMALLINT', ['SMALLINT', 'SMALLINT', 'SMALLINT'],
     "ArrayFunctions::array_generate<TYPE_SMALLINT>"],
    [150332, 'array_generate', True, False, 'ARRAY_INT', ['INT', 'INT', 'INT'],
     "ArrayFunctions::array_generate<TYPE_INT>"],
    [150333, 'array_generate', True, False, 'ARRAY_BIGINT', ['BIGINT', 'BIGINT', 'BIGINT'],
     "ArrayFunctions::array_generate<TYPE_BIGINT>"],
    [150334, 'array_generate', True, False, 'ARRAY_LARGEINT', ['LARGEINT', 'LARGEINT', 'LARGEINT'],
     "ArrayFunctions::array_generate<TYPE_LARGEINT>"],

    # high-order functions related to lambda functions.
    [160100, 'array_map', True, False, 'ANY_ARRAY', ['FUNCTION', 'ANY_ARRAY', "..."], 'ArrayFunctions::array_map'],

    # map functions
    [170000, 'map_size', True, False, 'INT', ['ANY_MAP'], 'MapFunctions::map_size'],
    [170001, 'map_keys', True, False, 'ANY_ARRAY', ['ANY_MAP'], 'MapFunctions::map_keys'],
    [170002, 'map_values', True, False, 'ANY_ARRAY', ['ANY_MAP'], 'MapFunctions::map_values'],
    [170003, 'map_from_arrays', True, False, 'ANY_MAP', ['ANY_ARRAY', 'ANY_ARRAY'], 'MapFunctions::map_from_arrays'],
    [170004, 'map_apply', True, False, 'ANY_MAP', ['FUNCTION', 'ANY_MAP'], 'nullptr'],
    [170005, 'map_filter', True, False, 'ANY_MAP', ['ANY_MAP', 'ARRAY_BOOLEAN'], 'MapFunctions::map_filter'],
    [170006, 'distinct_map_keys', True, False, 'ANY_MAP', ['ANY_MAP'], 'MapFunctions::distinct_map_keys'],
    [170007, 'map_concat', True, False, 'ANY_MAP', ['ANY_MAP', "..."], 'MapFunctions::map_concat'],

    # map, array common functions
    [170100, 'cardinality', True, False, 'INT', ['ANY_MAP'], 'MapFunctions::map_size'],
    [170101, 'cardinality', True, False, 'INT', ['ANY_ARRAY'], 'ArrayFunctions::array_length'],

    # struct functions
    [170500, 'row', True, False, 'ANY_STRUCT', ['ANY_ELEMENT', "..."], 'StructFunctions::new_struct'],
    [170501, 'named_struct', True, False, 'ANY_STRUCT', ['ANY_ELEMENT', "..."], 'StructFunctions::named_struct'],

    # user function
    [180000, 'is_role_in_session', True, False, 'BOOLEAN', ['VARCHAR'], 'nullptr']
]
