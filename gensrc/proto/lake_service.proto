// Copyright 2021-present StarRocks, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
syntax="proto2";

package starrocks;
option java_package = "com.starrocks.proto";

import "types.proto";
import "lake_types.proto";
import "status.proto";

option cc_generic_services = true;

message PublishVersionRequest {
    repeated int64 tablet_ids = 1;
    // This field is deprecated now, use |txn_infos| instead.
    repeated int64 txn_ids = 2; // deprecated
    // |base_version| would be |new_version - txn_ids_size()| now, define
    // it explicitly for more clarity and better forward compatibility.
    optional int64 base_version = 3;
    optional int64 new_version = 4;
    // Commit time of the transaction to be published.
    // If the size of txn_ids is greater than 1, commit_time should be the commit time of the last transaction.
    // Meansured as the number of seconds since the Epoch, 1970-01-01 00:00:00 +0000 (UTC)
    optional int64 commit_time = 5; // deprecated
    optional int64 timeout_ms = 6;
    repeated TxnInfoPB txn_infos = 7;
}

message PublishVersionResponse {
    repeated int64 failed_tablets = 1;
    // Mapping from tablet id to compaction score.
    map<int64, double> compaction_scores = 2;
    optional StatusPB status = 3;
}

message AbortTxnRequest {
    repeated int64 tablet_ids = 1;
    repeated int64 txn_ids = 2;
    // Whether need to clean up the txn logs
    optional bool skip_cleanup = 3;
    // Should have the save size with txn_ids or 0
    repeated TxnTypePB txn_types = 4;
}

message AbortTxnResponse {
    repeated int64 failed_tablets = 1;
}

message DeleteTabletRequest {
    // All tablets must be in the same partition.
    repeated int64 tablet_ids = 1;
}

message DeleteTabletResponse {
    repeated int64 failed_tablets = 1;
    optional StatusPB status = 2;
}

message DeleteTxnLogRequest {
    repeated int64 tablet_ids = 1;
    repeated int64 txn_ids = 2;
}

message DeleteTxnLogResponse {
    optional StatusPB status = 1;
}

message CompactRequest {
    repeated int64 tablet_ids = 1;
    optional int64 txn_id = 2;
    optional int64 version = 3;
    optional int64 timeout_ms = 4;
    optional bool allow_partial_success = 5;
}

message CompactResponse {
    repeated int64 failed_tablets = 1;
    // optional int64 execution_time = 2; // ms
    // optional int64 num_input_bytes = 3;
    // optional int64 num_input_rows = 4;
    // optional int64 num_output_bytes = 5;
    // optional int64 num_output_rows = 6;
    optional StatusPB status = 7;
}

message DropTableRequest {
    // tablet_id can be the id of any of the tablet belongs to the table to be dropped.
    optional int64 tablet_id = 1;
    optional string path = 2;
}

message DropTableResponse {
    // unused, just for preventing jprotobuf error "no field use annotation @com.baidu.bjf.remoting.protobuf.annotation.Protobuf ..."
    optional int32 pad = 1;
    optional StatusPB status = 2;
}

message DeleteDataRequest {
    repeated int64 tablet_ids = 1;
    optional int64 txn_id = 2;
    optional DeletePredicatePB delete_predicate = 3;
}

message DeleteDataResponse {
    repeated int64 failed_tablets = 1;
}

message TabletStatRequest {
    message TabletInfo {
        optional int64 tablet_id = 1;
        optional int64 version = 2;
    }

    repeated TabletInfo tablet_infos = 1;
    optional int64 timeout_ms = 2;
}

message TabletStatResponse {
    message TabletStat {
        optional int64 tablet_id = 1;
        optional int64 num_rows = 2;
        optional int64 data_size = 3;
    }

    repeated TabletStat tablet_stats = 1;
}

// Rename file txn_{tablet_id}_{txn_id} to vtxn_{tablet_id}_{version}
message PublishLogVersionRequest {
    repeated int64 tablet_ids = 1;
    optional int64 txn_id = 2;
    optional int64 version = 3;
}

message PublishLogVersionBatchRequest {
    repeated int64 tablet_ids = 1;
    repeated int64 txn_ids = 2;
    repeated int64 versions = 3;
}

message PublishLogVersionResponse {
    repeated int64 failed_tablets = 1;
}

message LockTabletMetadataRequest {
    optional int64 tablet_id = 1;
    optional int64 version = 2;
    optional int64 expire_time = 3;
}

message LockTabletMetadataResponse {
    // unused, just for preventing jprotobuf error "no field use annotation @com.baidu.bjf.remoting.protobuf.annotation.Protobuf ..."
    optional int32 pad = 1;
}

message UnlockTabletMetadataRequest {
    optional int64 tablet_id = 1;
    optional int64 version = 2;
    optional int64 expire_time = 3;
}

message UnlockTabletMetadataResponse {
    // unused, just for preventing jprotobuf error "no field use annotation @com.baidu.bjf.remoting.protobuf.annotation.Protobuf ..."
    optional int32 pad = 1;
}

message UploadSnapshotsRequest {
    map<int64, Snapshot> snapshots = 1;
    optional string broker = 2;
    map<string, string> broker_properties = 3;
}

message UploadSnapshotsResponse {
    // unused, just for preventing jprotobuf error "no field use annotation @com.baidu.bjf.remoting.protobuf.annotation.Protobuf ..."
    optional int32 pad = 1;
}

message Snapshot {
    optional int64 version = 1;
    optional string dest_path = 2;
}

message RestoreInfo {
    optional int64 tablet_id = 1;
    optional string snapshot_path = 2;
}

message RestoreSnapshotsRequest {
    repeated RestoreInfo restore_infos = 1;
    optional string broker = 2;
    map<string, string> broker_properties = 3;
}

message RestoreSnapshotsResponse {
    // unused, just for preventing jprotobuf error "no field use annotation @com.baidu.bjf.remoting.protobuf.annotation.Protobuf ..."
    optional int32 pad = 1;
}

message AbortCompactionRequest {
    optional int64 txn_id = 1;
}

message AbortCompactionResponse {
    optional StatusPB status = 1;
}

message VacuumRequest {
    // All tablets must be in the same partition.
    repeated int64 tablet_ids = 1;
    // Tablet metadata files with version numbers greater than or equals to min_retain_version
    // will NOT be vacuumed. For tablet metadata files with version numbers less than
    // min_retain_version, decide whether they should be deleted by comparing the create time
    // against the grace_timestamp.
    optional int64 min_retain_version = 2;
    // the timestamp after which created tablet metadata files will not be vacuumed.
    // In addition to retaining all versions after grace_timestamp, retain the last version before
    // grace_timestamp.
    // Meansured as the number of seconds since the Epoch, 1970-01-01 00:00:00 +0000 (UTC)
    optional int64 grace_timestamp = 3;
    // Delete txn log files with txn IDs less than min_active_txn_id
    optional int64 min_active_txn_id = 4;
    // Whether need to delete committed txn logs or not.
    optional bool delete_txn_log = 5;
    // ID of the partition the tablet belongs to.
    optional int64 partition_id = 6;
}

message VacuumResponse {
    optional StatusPB status = 1;
    // The number of files vacuumed, value undefined if status is not ok.
    optional int64 vacuumed_files = 2;
    // The total size of files vacuumed, value undefined if status is not ok.
    optional int64 vacuumed_file_size = 3;
}

message VacuumFullRequest {
    repeated int64 tablet_ids = 1;
    optional int64 min_check_version = 2;
    optional int64 max_check_version = 3;
    optional int64 min_active_txn_id = 4;
}

message VacuumFullResponse {
    optional StatusPB status = 1;
    optional int64 vacuumed_files = 2;
    optional int64 vacuumed_file_size = 3;
}

service LakeService {
    rpc publish_version(PublishVersionRequest) returns (PublishVersionResponse);
    rpc publish_log_version(PublishLogVersionRequest) returns (PublishLogVersionResponse);
    rpc publish_log_version_batch(PublishLogVersionBatchRequest) returns (PublishLogVersionResponse);
    rpc delete_txn_log(DeleteTxnLogRequest) returns (DeleteTxnLogResponse);
    rpc abort_txn(AbortTxnRequest) returns (AbortTxnResponse);
    rpc compact(CompactRequest) returns (CompactResponse);
    rpc delete_tablet(DeleteTabletRequest) returns (DeleteTabletResponse);
    rpc drop_table(DropTableRequest) returns (DropTableResponse);
    rpc delete_data(DeleteDataRequest) returns (DeleteDataResponse);
    rpc get_tablet_stats(TabletStatRequest) returns (TabletStatResponse);
    rpc lock_tablet_metadata(LockTabletMetadataRequest) returns (LockTabletMetadataResponse);
    rpc unlock_tablet_metadata(UnlockTabletMetadataRequest) returns (UnlockTabletMetadataResponse);
    rpc upload_snapshots(UploadSnapshotsRequest) returns (UploadSnapshotsResponse);
    rpc restore_snapshots(RestoreSnapshotsRequest) returns (RestoreSnapshotsResponse);
    rpc abort_compaction(AbortCompactionRequest) returns (AbortCompactionResponse);
    rpc vacuum(VacuumRequest) returns (VacuumResponse);
    rpc vacuum_full(VacuumFullRequest) returns (VacuumFullResponse);
}

