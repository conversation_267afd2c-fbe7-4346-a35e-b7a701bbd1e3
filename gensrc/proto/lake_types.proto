// Copyright 2021-present StarRocks, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
syntax = "proto2";

package starrocks;
option java_package = "com.starrocks.proto";

import "types.proto";
import "tablet_schema.proto";
import "olap_file.proto";

message DelvecDataPB {
    optional int64 version = 1;
    // serialized data
    optional bytes data = 2;
}

message DelvecPagePB {
    optional int64 version = 1;
    // position in file
    optional uint64 offset = 2;
    optional uint64 size = 3;
}

message DelvecCacheKeyPB {
    optional int64 id = 1;
    optional DelvecPagePB delvec_page = 2;
}

message FileMetaPB {
    optional string name = 1;
    optional int64 size = 2;
}

message DelvecMetadataPB {
    // map from version to delete vector file meta
    // so that we no need to keep file name (consume more memory) in each DelvecPagePB
    // instead we can lookup delete vector file name by version saved in DelvecPagePB
    map<int64, FileMetaPB> version_to_file = 1;
    // from segment id to delvec page
    map<uint32, DelvecPagePB> delvecs = 2;
}

message RowsetMetadataPB {
    optional uint32 id = 1;
    optional bool overlapped = 2;
    repeated string segments = 3;
    optional int64 num_rows = 4;
    optional int64 data_size = 5;
    optional DeletePredicatePB delete_predicate = 6;
    // approximate, BE upgrade from old version can't get exact number of dels
    optional int64 num_dels = 7;
    repeated uint64 segment_size = 8 [packed = true];
    // May not be empty if this rowset was generated by a compaction task in PK table.
    // We use max_compact_input_rowset_id to decide the rowset order when doing pk recover.
    optional uint32 max_compact_input_rowset_id = 9;
    // WARNING! 10-12 is already taken in newer version
    // set in partial compaction, indicate which segment index next compaction should start from
    // only be set > 0 if `overlapped` is true
    optional uint32 next_compaction_offset = 13;
}

// At present, the lake persistent index reuses the logic of the persistent index,
// and writes the index to the local disk, which may be upgraded and written to the remote end in the future,
// the type is is to make it upgradable for compatibility.
enum PersistentIndexTypePB {
    LOCAL = 0;
    CLOUD_NATIVE = 1;
}

message TabletMetadataPB {
    optional int64 id = 1;
    optional int64 version = 2;
    optional TabletSchemaPB schema = 3;
    repeated RowsetMetadataPB rowsets = 4;
    optional uint32 next_rowset_id = 5;
    // cumulative point rowset index
    optional uint32 cumulative_point = 6;
    optional DelvecMetadataPB delvec_meta = 7;
    // May not be empty if this metadata was generated by a compaction task
    // The data files in the compaction input rowsets would be deleted before
    // the metadata was deleted by the GC module.
    repeated RowsetMetadataPB compaction_inputs = 8;
    // The previous metadata version containing garbage file records, i.e,  the
    // pervious metadata version whose "compaction_inputs" or "orphan_files" is
    // not empty.
    optional int64 prev_garbage_version = 9;
    repeated FileMetaPB orphan_files = 10;
    optional bool enable_persistent_index = 11;
    optional PersistentIndexTypePB persistent_index_type = 12;
    // The commit time on the FE for the transaction that created this tablet metadata.
    // Meansured as the number of seconds since the Epoch, 1970-01-01 00:00:00 +0000 (UTC).
    optional int64 commit_time = 13;
    // If the tablet is replicated from another cluster, the source_schema saved the schema in the cluster
    optional TabletSchemaPB source_schema = 14;
}

message MetadataUpdateInfoPB {
    // enable persistent index in primary key table
    optional bool enable_persistent_index = 1;
}

message TxnLogPB {
    message OpWrite {
        optional RowsetMetadataPB rowset = 1;
        // some txn semantic information bind to this rowset
        optional RowsetTxnMetaPB txn_meta = 2;
        repeated string dels = 3;
        // for partial update, record dest rewrite segment names to avoid gc
        repeated string rewrite_segments = 4;
    }

    message OpCompaction {
        repeated uint32 input_rowsets = 1;
        optional RowsetMetadataPB output_rowset = 2;
    }

    message OpSchemaChange {
        repeated RowsetMetadataPB rowsets = 1;
        // Whether segment files are linked to new tablet's segment files.
        // If it's false, the segment files can be deleted on aborting,
        // otherwise cannot delete them.
        optional bool linked_segment = 2;
        // The data <= |alter_version| is generated by schema change,
        // and the rowsets are saved in txn_log.
        // The data between |alter_version + 1| and |new_version - 1| in publish version request is generated by load,
        // and the rowsets are saved in vtxn_log.
        optional int64 alter_version = 3;
        // delete vector
        optional DelvecMetadataPB delvec_meta = 4;
    }

    message OpAlterMetadata {
        // alter tablet meta op
        repeated MetadataUpdateInfoPB metadata_update_infos = 1;
    }

    message OpReplication {
        optional ReplicationTxnMetaPB txn_meta = 1;
        repeated OpWrite op_writes = 2;
        map<uint32, DelvecDataPB> delvecs = 3;
        optional TabletSchemaPB source_schema = 4;
    }

    optional int64 tablet_id = 1;
    optional int64 txn_id = 2;
    optional OpWrite op_write = 3;
    optional OpCompaction op_compaction = 4;
    optional OpSchemaChange op_schema_change = 5;
    optional OpAlterMetadata op_alter_metadata = 6;
    optional OpReplication op_replication = 7;
}

message TabletMetadataLockPB {}

message TxnInfoPB {
    optional int64 txn_id = 1;
    optional int64 commit_time = 2;
    // Does the transaction generated a combined txn log
    optional bool combined_txn_log = 3;
    optional TxnTypePB txn_type = 4;
    optional bool force_publish = 5; // only used for compaction
};
