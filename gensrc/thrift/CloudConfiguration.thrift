// Copyright 2021-present StarRocks, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

namespace cpp starrocks
namespace java com.starrocks.thrift

enum TCloudType {
    DEFAULT,
    AWS,
    AZURE,
    GCP,
    ALIYUN,
    HDFS
}

// Deprecated
struct TCloudProperty {
    1: required string key;
    2: required string value;
}

struct TCloudConfiguration {
    1: optional TCloudType cloud_type;
    2: optional list<TCloudProperty> deprecated_cloud_properties; // Deprecated
    3: optional map<string, string> cloud_properties;
}
