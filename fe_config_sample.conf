# StarRocks FE 配置文件样例
# 基于 StarRocks 源码中的配置参数整理

#####################################################################
## JVM 相关配置 (大写参数由 start_fe.sh 读取)
#####################################################################

# 日志输出目录
LOG_DIR = ${STARROCKS_HOME}/log

# 日期格式
DATE = "$(date +%Y%m%d-%H%M%S)"

# JDK 8 的 JVM 参数
JAVA_OPTS="-Dlog4j2.formatMsgNoLookups=true -Xmx8192m -XX:+UseG1GC -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:${LOG_DIR}/fe.gc.log.$DATE -XX:+PrintConcurrentLocks -Djava.security.policy=${STARROCKS_HOME}/conf/udf_security.policy"

# JDK 11+ 的 JVM 参数
JAVA_OPTS_FOR_JDK_11="-Dlog4j2.formatMsgNoLookups=true -Xmx8192m -XX:+UseG1GC -Xlog:gc*:${LOG_DIR}/fe.gc.log.$DATE:time -Djava.security.policy=${STARROCKS_HOME}/conf/udf_security.policy"

#####################################################################
## 基础配置 (小写参数由主程序读取)
#####################################################################

# 日志级别: DEBUG, INFO, WARN, ERROR, FATAL
sys_log_level = INFO

# 元数据存储目录
meta_dir = ${STARROCKS_HOME}/meta

# 网络端口配置
http_port = 8030          # HTTP 服务端口
rpc_port = 9020           # Thrift RPC 端口
query_port = 9030         # MySQL 协议端口
edit_log_port = 9010      # 编辑日志端口

# 启用 MySQL 服务的 NIO 模式
mysql_service_nio_enabled = true

# 指定 FE 的 IP 地址 (CIDR 格式)
# priority_networks = **********/24;***********/16

# 指定具体的 FE IP 地址
# frontend_address = 0.0.0.0

#####################################################################
## 日志配置
#####################################################################

# 系统日志配置
# sys_log_dir = ${STARROCKS_HOME}/log
# log_roll_size_mb = 1024
# sys_log_roll_num = 10
# sys_log_verbose_modules = 
# sys_log_roll_interval = DAY
# sys_log_delete_age = 7d
# sys_log_to_console = false
# sys_log_format = plaintext

# 审计日志配置
# audit_log_dir = ${STARROCKS_HOME}/log
# audit_log_modules = slow_query, query
# audit_log_roll_num = 90
# audit_log_roll_interval = DAY
# audit_log_delete_age = 30d
# audit_log_json_format = false

# 慢查询日志配置
# qe_slow_log_ms = 5000
# enable_qe_slow_log = true

# Profile 日志配置
# enable_profile_log = true
# profile_log_dir = ${STARROCKS_HOME}/log
# profile_log_roll_num = 5
# profile_log_roll_interval = DAY
# profile_log_delete_age = 7d

#####################################################################
## 网络和连接配置
#####################################################################

# HTTP 服务配置
# http_backlog_num = 1024
# http_max_initial_line_length = 4096
# http_max_header_size = 32768
# http_max_chunk_size = 8192
# http_slow_request_threshold_ms = 5000

# Thrift RPC 配置
# thrift_client_timeout_ms = 5000
# thrift_backlog_num = 1024
# thrift_rpc_timeout_ms = 10000
# thrift_rpc_retry_times = 3
# thrift_rpc_strict_mode = true
# thrift_rpc_max_body_size = -1

# BRPC 配置 (用于解决过载问题)
brpc_connection_pool_size = 64
brpc_idle_wait_max_time = 30000

#####################################################################
## 查询引擎配置
#####################################################################

# 连接数限制
qe_max_connection = 4096
max_connection_scheduler_threads_num = 4096

# 查询超时配置
# qe_query_timeout_second = 300
# max_conn_per_user = 100

# 查询队列配置 (通过 SQL 设置)
# enable_query_queue_select = true
# query_queue_concurrency_limit = 100
# query_queue_mem_used_pct_limit = 80
# query_queue_max_queued_queries = 1000
# query_queue_pending_timeout_second = 300

#####################################################################
## 存储和数据管理配置
#####################################################################

# 临时目录
# tmp_dir = ${STARROCKS_HOME}/temp_dir

# 编辑日志配置
# edit_log_type = BDB
# edit_log_roll_num = 50000
# edit_log_write_slow_log_threshold_ms = 2000

# 元数据延迟容忍度
# meta_delay_toleration_second = 10

# 存储使用限制
# storage_usage_hard_limit_percent = 95
# storage_usage_hard_limit_reserve_bytes = 107374182400

#####################################################################
## 性能调优配置
#####################################################################

# 统计信息收集
# statistic_collect_interval_sec = 300
# statistic_collect_concurrency = 3
# statistic_collect_query_timeout = 3600

# 物化视图配置
# enable_materialized_view = true
# enable_materialized_view_text_based_rewrite = true

# 并发控制
# max_allowed_in_element_num_of_delete = 10000

#####################################################################
## 监控和追踪配置
#####################################################################

# Jaeger 追踪配置
# jaeger_grpc_endpoint = http://localhost:14250

# 端口连通性检查
# port_connectivity_check_interval_sec = 60
# port_connectivity_check_retry_times = 3
# port_connectivity_check_timeout_ms = 10000

#####################################################################
## 高级配置
#####################################################################

# UDF 支持
# enable_udf = false

# Decimal V3 支持
# enable_decimal_v3 = true

# 实验性行存储
# enable_experimental_rowstore = false

# JSON 文件大小限制
# json_file_size_limit = 4294967296

# VARCHAR 最大长度
# max_varchar_length = 1048576

#####################################################################
## 云原生配置 (如果使用共享数据模式)
#####################################################################

# 运行模式: shared_nothing, shared_data, hybrid
# run_mode = shared_nothing

# 云原生元数据服务端口
# cloud_native_meta_port = 6090

# 存储类型: S3, HDFS, AZBLOB
# cloud_native_storage_type = S3

# S3 配置
# aws_s3_region = us-west-2
# aws_s3_endpoint = 
# aws_s3_access_key = 
# aws_s3_secret_key = 
# aws_s3_path = 

#####################################################################
## 外部系统集成配置
#####################################################################

# Hive Metastore 配置
# hive_meta_load_concurrency = 4
# hive_meta_cache_refresh_interval_s = 7200
# hive_meta_cache_ttl_s = 86400
# max_hive_partitions_per_rpc = 5000

# JDBC 配置
# jdbc_connection_pool_size = 8
# jdbc_minimum_idle_connections = 1
# jdbc_connection_idle_timeout_ms = 600000

#####################################################################
## 备份和恢复配置
#####################################################################

# 备份作业默认超时时间
# backup_job_default_timeout_ms = 86400000

# 系统区域设置
# locale = zh_CN.UTF-8
