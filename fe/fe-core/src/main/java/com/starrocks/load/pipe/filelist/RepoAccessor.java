// Copyright 2021-present StarRocks, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package com.starrocks.load.pipe.filelist;

import com.google.common.base.Preconditions;
import com.starrocks.load.pipe.PipeFileRecord;
import com.starrocks.thrift.TResultBatch;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Query the repo
 */
public class RepoAccessor {

    private static final Logger LOG = LogManager.getLogger(RepoAccessor.class);
    private static final RepoAccessor INSTANCE = new RepoAccessor();
    private static long DML_EXCEPTION_SLEEP_MS = 10000;

    public static RepoAccessor getInstance() {
        return INSTANCE;
    }

    public List<PipeFileRecord> listAllFiles() {
        try {
            List<TResultBatch> batch = RepoExecutor.getInstance().executeDQL(
                    FileListTableRepo.SELECT_FILES);
            return PipeFileRecord.fromResultBatch(batch);
        } catch (Exception e) {
            LOG.error("listAllFiles failed", e);
            throw e;
        }
    }

    public List<PipeFileRecord> listFilesByState(long pipeId, FileListRepo.PipeFileState state, long limit) {
        List<PipeFileRecord> res = null;
        try {
            String sql = buildListFileByState(pipeId, state, limit);
            List<TResultBatch> batch = RepoExecutor.getInstance().executeDQL(sql);
            res = PipeFileRecord.fromResultBatch(batch);
        } catch (Exception e) {
            LOG.error("listUnloadedFiles failed", e);
            throw e;
        }
        return res;
    }

    public PipeFileRecord listFilesByPath(long pipeId, String path) {
        PipeFileRecord res = null;
        try {
            String sql = buildListFileByPath(pipeId, path);
            List<TResultBatch> batch = RepoExecutor.getInstance().executeDQL(sql);
            if (CollectionUtils.isEmpty(batch)) {
                throw new IllegalArgumentException("file not found: " + path);
            } else if (batch.size() > 1) {
                throw new IllegalArgumentException("too many files found but expect 1: " + path);
            }
            res = PipeFileRecord.fromResultBatch(batch).get(0);
        } catch (Exception e) {
            LOG.error("listFilesByPath failed: pipeId={} path={}", pipeId, path, e);
            throw e;
        }
        return res;
    }

    public List<PipeFileRecord> selectStagedFiles(List<PipeFileRecord> records) {
        try {
            String sql = buildSelectStagedFiles(records);
            List<TResultBatch> batch = RepoExecutor.getInstance().executeDQL(sql);
            return PipeFileRecord.fromResultBatch(batch);
        } catch (Exception e) {
            LOG.error("selectStagedFiles failed", e);
            throw e;
        }
    }

    public void addFiles(List<PipeFileRecord> records) {
        try {
            String sql = buildSqlAddFiles(records);
            RepoExecutor.getInstance().executeDML(sql);
            LOG.info("addFiles into repo: {}", records);
            return;
        } catch (Exception e) {
            LOG.error("addFiles {} failed", records, e);
            throw e;
        }
    }

    /**
     * pipe_id, file_name, file_version are required to locate unique file
     */
    public void updateFilesState(List<PipeFileRecord> records, FileListRepo.PipeFileState state, String insertLabel) {
        try {
            String sql = null;
            switch (state) {
                case UNLOADED:
                case ERROR:
                case SKIPPED:
                    sql = buildSqlUpdateState(records, state);
                    break;
                case LOADING:
                    sql = buildSqlStartLoad(records, state, insertLabel);
                    break;
                case FINISHED:
                    sql = buildSqlFinishLoad(records, state);
                    break;
                default:
                    Preconditions.checkState(false, "not supported");
                    break;
            }
            RepoExecutor.getInstance().executeDML(sql);
            LOG.info("update files state to {}: {}", state, records);
        } catch (Exception e) {
            LOG.error("update files state failed: {}", records, e);
            throw e;
        }
    }

    public void deleteByPipe(long pipeId) {
        try {
            String sql = String.format(FileListTableRepo.DELETE_BY_PIPE, pipeId);
            RepoExecutor.getInstance().executeDML(sql);
            LOG.info("delete pipe files {}", pipeId);
        } catch (Exception e) {
            LOG.error("delete file of pipe {} failed", pipeId, e);
            throw e;
        }
    }

    protected String buildSelectStagedFiles(List<PipeFileRecord> files) {
        String where = files.stream().map(PipeFileRecord::toUniqueLocator).collect(Collectors.joining(" OR "));
        return FileListTableRepo.SELECTED_STAGED_FILES + where;
    }

    protected String buildListFileByState(long pipeId, FileListRepo.PipeFileState state, long limit) {
        return limit <= 0 ?
                String.format(FileListTableRepo.SELECT_FILES_BY_STATE,
                        pipeId, Strings.quote(state.toString())) :
                String.format(FileListTableRepo.SELECT_FILES_BY_STATE_WITH_LIMIT,
                        pipeId, Strings.quote(state.toString()), limit);
    }

    protected String buildListFileByPath(long pipeId, String path) {
        return String.format(FileListTableRepo.SELECT_FILES_BY_PATH, pipeId, Strings.quote(path));
    }

    protected String buildDeleteByPipe(long pipeId) {
        return String.format(FileListTableRepo.DELETE_BY_PIPE, pipeId);
    }

    protected String buildSqlAddFiles(List<PipeFileRecord> records) {
        StringBuilder sb = new StringBuilder();
        sb.append(FileListTableRepo.INSERT_FILES);
        sb.append(records.stream().map(PipeFileRecord::toValueList).collect(Collectors.joining(",")));
        return sb.toString();
    }

    protected String buildSqlUpdateState(List<PipeFileRecord> records, FileListRepo.PipeFileState state) {
        // FIXME: update error message for each file, use partial update capability
        String errorMessage = records.stream()
                .filter(x -> StringUtils.isNotEmpty(x.getErrorMessage()))
                .findFirst()
                .map(PipeFileRecord::toErrorInfo).orElse("");
        StringBuilder sb = new StringBuilder();
        sb.append(String.format(FileListTableRepo.UPDATE_FILE_STATE,
                Strings.quote(state.toString()), Strings.quote(errorMessage)));
        sb.append(records.stream().map(PipeFileRecord::toUniqueLocator).collect(Collectors.joining(" OR ")));
        return sb.toString();
    }

    public String buildSqlStartLoad(List<PipeFileRecord> records, FileListRepo.PipeFileState state, String label) {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format(FileListTableRepo.UPDATE_FILE_STATE_START_LOAD,
                Strings.quote(state.toString()), Strings.quote(label)));
        sb.append(records.stream().map(PipeFileRecord::toUniqueLocator).collect(Collectors.joining(" OR ")));
        return sb.toString();
    }

    protected String buildSqlFinishLoad(List<PipeFileRecord> records, FileListRepo.PipeFileState state) {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format(FileListTableRepo.UPDATE_FILE_STATE_FINISH_LOAD, Strings.quote(state.toString())));
        sb.append(records.stream().map(PipeFileRecord::toUniqueLocator).collect(Collectors.joining(" OR ")));
        return sb.toString();
    }
}
