// Copyright 2021-present StarRocks, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.


package com.starrocks.connector.iceberg;

public enum IcebergCatalogType {
    HIVE_CATALOG,
    CUSTOM_CATALOG,
    GLUE_CATALOG,
    REST_CATALOG,
    HADOOP_CATALOG,
    UNKNOWN;
    // TODO: add more iceberg catalog type

    public static IcebergCatalogType fromString(String catalogType) {
        for (IcebergCatalogType type : IcebergCatalogType.values()) {
            if (type.name().equalsIgnoreCase(String.format("%s_CATALOG", catalogType))) {
                return type;
            }
        }
        return UNKNOWN;
    }
}
