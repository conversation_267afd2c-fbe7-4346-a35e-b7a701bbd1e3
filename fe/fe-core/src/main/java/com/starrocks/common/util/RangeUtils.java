// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

package com.starrocks.common.util;

import com.google.common.collect.BoundType;
import com.google.common.collect.Range;
import com.google.common.collect.RangeMap;
import com.google.common.collect.TreeRangeMap;
import com.starrocks.catalog.PartitionKey;
import com.starrocks.common.DdlException;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

public class RangeUtils {

    public static final Comparator<Map.Entry<Long, Range<PartitionKey>>> RANGE_MAP_ENTRY_COMPARATOR =
            Comparator.comparing(o -> o.getValue().lowerEndpoint());

    public static final Comparator<Range<PartitionKey>> RANGE_COMPARATOR =
            Comparator.comparing(Range::lowerEndpoint);

    public static void checkRangeIntersect(Range<PartitionKey> range1, Range<PartitionKey> range2) throws DdlException {
        if (range2.isConnected(range1)) {
            if (!range2.intersection(range1).isEmpty()) {
                throw new DdlException("Range " + range1 + " is intersected with range: " + range2);
            }
        }
    }

    public static <T extends Comparable> boolean isRangeEqual(Range<T> l, Range<T> r) {
        if (l == null || r == null) {
            return false;
        }
        return l.lowerEndpoint().compareTo(r.lowerEndpoint()) == 0 &&
                l.upperEndpoint().compareTo(r.upperEndpoint()) == 0;
    }

    /*
     * Pass only if the 2 range lists are exactly same
     * What is "exactly same"?
     *      1. {[0, 10), [10, 20)} exactly same as {[0, 20)}
     *      2. {[0, 10), [15, 20)} exactly same as {[0, 10), [15, 18), [18, 20)}
     *      3. {[0, 10), [15, 20)} exactly same as {[0, 10), [15, 20)}
     *      4. {[0, 10), [15, 20)} NOT exactly same as {[0, 20)}
     *
     *  Here I will use an example to explain the algorithm:
     *      list1: {[0, 10), [15, 20)}
     *      list2: {[0, 10), [15, 18), [18, 20)}
     *
     *  1. sort 2 lists first (the above 2 lists are already sorted)
     *  2. Begin to compare ranges from index 0: [0, 10) and [0, 10)
     *      2.1 lower bounds (0 and 0) are equal.
     *      2.2 upper bounds (10 and 10) are equal.
     *  3. Begin to compare next 2 ranges [15, 20) and [15, 18)
     *      3.1 lower bounds (15 and 15) are equal.
     *      3.2 upper bounds (20 and 18) are not equal. and 20 > 18
     *      3.3 Split range [15, 20) to [15, 18) and [18, 20)
     *  4. Begin to compare next 2 ranges [18, 20) and [18, 20), the first [18, 20) is the splitted range
     *      4.1 lower bounds (18 and 18) are equal.
     *      4.2 upper bounds (20 and 20) are equal.
     *  5. Not more next ranges, so 2 lists are equal.
     */
    public static void checkRangeListsMatch(List<Range<PartitionKey>> list1, List<Range<PartitionKey>> list2)
            throws DdlException {
        Collections.sort(list1, RangeUtils.RANGE_COMPARATOR);
        Collections.sort(list2, RangeUtils.RANGE_COMPARATOR);

        int idx1 = 0;
        int idx2 = 0;
        Range<PartitionKey> range1 = list1.get(idx1);
        Range<PartitionKey> range2 = list2.get(idx2);
        while (true) {
            if (range1.lowerEndpoint().compareTo(range2.lowerEndpoint()) != 0) {
                throw new DdlException("2 range lists are not strictly matched. "
                        + range1.lowerEndpoint() + " vs. " + range2.lowerEndpoint());
            }

            int res = range1.upperEndpoint().compareTo(range2.upperEndpoint());
            if (res == 0) {
                ++idx1;
                ++idx2;
                if (idx1 == list1.size() || idx2 == list2.size()) {
                    break;
                }
                range1 = list1.get(idx1);
                range2 = list2.get(idx2);
                continue;
            } else if (res > 0) {
                if (++idx2 == list2.size()) {
                    break;
                }
                range1 = Range.closedOpen(range2.upperEndpoint(), range1.upperEndpoint());
                range2 = list2.get(idx2);
            } else {
                if (++idx1 == list1.size()) {
                    break;
                }
                range2 = Range.closedOpen(range1.upperEndpoint(), range2.upperEndpoint());
                range1 = list1.get(idx1);
            }
        }

        if (idx1 < list1.size() || idx2 < list2.size()) {
            throw new DdlException("2 range lists are not strictly matched. "
                    + list1 + " vs. " + list2);
        }
    }

    public static void writeRange(DataOutput out, Range<PartitionKey> range) throws IOException {
        boolean hasLowerBound = false;
        boolean hasUpperBound = false;

        if (range == null) {
            out.writeBoolean(hasLowerBound);
            out.writeBoolean(hasUpperBound);
            return;
        }

        // write lower bound if lower bound exists
        hasLowerBound = range.hasLowerBound();
        out.writeBoolean(hasLowerBound);
        if (hasLowerBound) {
            PartitionKey lowerBound = range.lowerEndpoint();
            out.writeBoolean(range.lowerBoundType() == BoundType.CLOSED);
            lowerBound.write(out);
        }

        // write upper bound if upper bound exists
        hasUpperBound = range.hasUpperBound();
        out.writeBoolean(hasUpperBound);
        if (hasUpperBound) {
            PartitionKey upperBound = range.upperEndpoint();
            out.writeBoolean(range.upperBoundType() == BoundType.CLOSED);
            upperBound.write(out);
        }
    }

    public static Range<PartitionKey> readRange(DataInput in) throws IOException {
        boolean hasLowerBound = false;
        boolean hasUpperBound = false;
        boolean lowerBoundClosed = false;
        boolean upperBoundClosed = false;
        PartitionKey lowerBound = null;
        PartitionKey upperBound = null;

        hasLowerBound = in.readBoolean();
        if (hasLowerBound) {
            lowerBoundClosed = in.readBoolean();
            lowerBound = PartitionKey.read(in);
        }

        hasUpperBound = in.readBoolean();
        if (hasUpperBound) {
            upperBoundClosed = in.readBoolean();
            upperBound = PartitionKey.read(in);
        }

        // Totally 9 cases. Both lower bound and upper bound could be open, closed or not exist
        if (hasLowerBound && lowerBoundClosed && hasUpperBound && upperBoundClosed) {
            return Range.closed(lowerBound, upperBound);
        }
        if (hasLowerBound && lowerBoundClosed && hasUpperBound && !upperBoundClosed) {
            return Range.closedOpen(lowerBound, upperBound);
        }
        if (hasLowerBound && !lowerBoundClosed && hasUpperBound && upperBoundClosed) {
            return Range.openClosed(lowerBound, upperBound);
        }
        if (hasLowerBound && !lowerBoundClosed && hasUpperBound && !upperBoundClosed) {
            return Range.open(lowerBound, upperBound);
        }
        if (hasLowerBound && lowerBoundClosed && !hasUpperBound) {
            return Range.atLeast(lowerBound);
        }
        if (hasLowerBound && !lowerBoundClosed && !hasUpperBound) {
            return Range.greaterThan(lowerBound);
        }
        if (!hasLowerBound && hasUpperBound && upperBoundClosed) {
            return Range.atMost(upperBound);
        }
        if (!hasLowerBound && hasUpperBound && !upperBoundClosed) {
            return Range.lessThan(upperBound);
        }
        // Neither lower bound nor upper bound exists, return null. This means just one partition
        return null;
    }

    // check if any ranges in "rangesToBeChecked" conflict with ranges in "baseRanges".
    public static void checkRangeConflict(List<Range<PartitionKey>> baseRanges,
                                          List<Range<PartitionKey>> rangesToBeChecked) throws DdlException {

        RangeMap<PartitionKey, Long> baseRangeMap = TreeRangeMap.create();
        long idx = 0;
        for (Range<PartitionKey> baseRange : baseRanges) {
            baseRangeMap.put(baseRange, idx++);
        }

        for (Range<PartitionKey> range : rangesToBeChecked) {
            if (!baseRangeMap.subRangeMap(range).asMapOfRanges().isEmpty()) {
                throw new DdlException("Range: " + range + " conflicts with existing range");
            }
        }
    }
}
