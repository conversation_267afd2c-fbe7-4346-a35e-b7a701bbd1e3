// Copyright 2021-present StarRocks, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package com.starrocks.common.profile;

import com.starrocks.common.util.RuntimeProfile;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

public abstract class Tracer {
    private static final Timer EMPTY_TIMER = new Timer();

    public Timer watchScope(String name) {
        return EMPTY_TIMER;
    }

    public void log(String event) {
    }

    public void log(String event, Object... args) {
    }

    public void log(Function<Object[], String> func, Object... args) {
    }

    public void reason(String event, Object... args) {
    }

    public void record(String name, String value) {
    }

    public void count(String name, int count) {
    }

    public List<Var<?>> getAllVars() {
        return Collections.emptyList();
    }

    public String printScopeTimer() {
        return "";
    }

    public String printTiming() {
        return "";
    }

    public String printVars() {
        return "";
    }

    public String printLogs() {
        return "";
    }

    public String printReasons() {
        return "";
    }

    public void toRuntimeProfile(RuntimeProfile parent) {
    }
}
