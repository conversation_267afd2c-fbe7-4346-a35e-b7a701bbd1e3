// Copyright 2021-present StarRocks, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.


package com.starrocks.scheduler.persist;

import com.google.gson.annotations.SerializedName;
import com.starrocks.common.io.Text;
import com.starrocks.common.io.Writable;
import com.starrocks.persist.gson.GsonUtils;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import java.util.Map;


public class TaskRunPeriodStatusChange implements Writable {
    // taskId -> progress
    @SerializedName("taskRunProgressMap")
    private Map<Long, Integer> taskRunProgressMap;

    public TaskRunPeriodStatusChange(Map<Long, Integer> taskRunProgressMap) {
        this.taskRunProgressMap = taskRunProgressMap;
    }

    public Map<Long, Integer> getTaskRunProgressMap() {
        return taskRunProgressMap;
    }

    public void setTaskRunProgressMap(Map<Long, Integer> taskRunPrgressMap) {
        this.taskRunProgressMap = taskRunPrgressMap;
    }

    public static TaskRunPeriodStatusChange read(DataInput in) throws IOException {
        String json = Text.readString(in);
        return GsonUtils.GSON.fromJson(json, TaskRunPeriodStatusChange.class);
    }

    @Override
    public void write(DataOutput out) throws IOException {
        String json = GsonUtils.GSON.toJson(this);
        Text.writeString(out, json);
    }
}
