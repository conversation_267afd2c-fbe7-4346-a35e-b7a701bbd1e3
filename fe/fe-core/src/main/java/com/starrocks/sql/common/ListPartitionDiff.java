// Copyright 2021-present StarRocks, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.


package com.starrocks.sql.common;

import java.util.List;
import java.util.Map;


public class ListPartitionDiff {

    Map<String, List<List<String>>> adds;

    Map<String, List<List<String>>> deletes;


    public ListPartitionDiff(Map<String, List<List<String>>> adds, Map<String, List<List<String>>> deletes) {
        this.adds = adds;
        this.deletes = deletes;
    }

    public Map<String, List<List<String>>> getAdds() {
        return adds;
    }

    public void setAdds(Map<String, List<List<String>>> adds) {
        this.adds = adds;
    }

    public Map<String, List<List<String>>> getDeletes() {
        return deletes;
    }

    public void setDeletes(Map<String, List<List<String>>> deletes) {
        this.deletes = deletes;
    }
}
