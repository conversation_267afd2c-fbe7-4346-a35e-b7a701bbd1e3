// Copyright 2021-present StarRocks, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.


package com.starrocks.sql.ast;

import com.starrocks.analysis.TaskName;
import com.starrocks.sql.parser.NodePosition;

import java.util.Map;

public class SubmitTaskStmt extends DdlStmt {

    private String dbName;

    private String taskName;

    private Map<String, String> properties;

    private int sqlBeginIndex;

    private String sqlText;

    private CreateTableAsSelectStmt createTableAsSelectStmt;
    private InsertStmt insertStmt;

    public SubmitTaskStmt(TaskName taskName, Map<String, String> properties, int sqlBeginIndex,
                          CreateTableAsSelectStmt createTableAsSelectStmt, NodePosition pos) {
        super(pos);
        this.dbName = taskName.getDbName();
        this.taskName = taskName.getName();
        this.properties = properties;
        this.sqlBeginIndex = sqlBeginIndex;
        this.createTableAsSelectStmt = createTableAsSelectStmt;
    }

    public SubmitTaskStmt(TaskName taskName, Map<String, String> properties, int sqlBeginIndex,
                          InsertStmt insertStmt, NodePosition pos) {
        super(pos);
        this.dbName = taskName.getDbName();
        this.taskName = taskName.getName();
        this.properties = properties;
        this.sqlBeginIndex = sqlBeginIndex;
        this.insertStmt = insertStmt;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public int getSqlBeginIndex() {
        return sqlBeginIndex;
    }

    public void setSqlBeginIndex(int sqlBeginIndex) {
        this.sqlBeginIndex = sqlBeginIndex;
    }

    public Map<String, String> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, String> properties) {
        this.properties = properties;
    }

    public String getSqlText() {
        return sqlText;
    }

    public void setSqlText(String sqlText) {
        this.sqlText = sqlText;
    }

    public CreateTableAsSelectStmt getCreateTableAsSelectStmt() {
        return createTableAsSelectStmt;
    }

    public void setCreateTableAsSelectStmt(CreateTableAsSelectStmt createTableAsSelectStmt) {
        this.createTableAsSelectStmt = createTableAsSelectStmt;
    }

    public InsertStmt getInsertStmt() {
        return insertStmt;
    }

    public void setInsertStmt(InsertStmt insertStmt) {
        this.insertStmt = insertStmt;
    }

    @Override
    public <R, C> R accept(AstVisitor<R, C> visitor, C context) {
        return visitor.visitSubmitTaskStatement(this, context);
    }
}
