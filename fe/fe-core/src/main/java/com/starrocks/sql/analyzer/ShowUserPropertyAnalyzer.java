// Copyright 2021-present StarRocks, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package com.starrocks.sql.analyzer;

import com.google.common.base.Strings;
import com.starrocks.qe.ConnectContext;
import com.starrocks.sql.ast.ShowUserPropertyStmt;

public class ShowUserPropertyAnalyzer {

    public static void analyze(ShowUserPropertyStmt statment, ConnectContext context) {
        String user = statment.getUser();
        if (Strings.isNullOrEmpty(user)) {
            statment.setUser(context.getQualifiedUser());
        } else {
            statment.setUser(user);
        }
        statment.setPattern(Strings.emptyToNull(statment.getPatter()));
    }
}
