// Copyright 2021-present StarRocks, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.


package com.starrocks.sql.optimizer.rule.implementation;

import com.google.common.collect.Lists;
import com.starrocks.sql.optimizer.OptExpression;
import com.starrocks.sql.optimizer.OptimizerContext;
import com.starrocks.sql.optimizer.base.OrderSpec;
import com.starrocks.sql.optimizer.operator.OperatorType;
import com.starrocks.sql.optimizer.operator.logical.LogicalTopNOperator;
import com.starrocks.sql.optimizer.operator.pattern.Pattern;
import com.starrocks.sql.optimizer.operator.physical.PhysicalTopNOperator;
import com.starrocks.sql.optimizer.rule.RuleType;

import java.util.List;

public class TopNImplementationRule extends ImplementationRule {
    public TopNImplementationRule() {
        super(RuleType.IMP_TOPN,
                Pattern.create(OperatorType.LOGICAL_TOPN, OperatorType.PATTERN_LEAF));
    }

    @Override
    public List<OptExpression> transform(OptExpression input, OptimizerContext context) {
        LogicalTopNOperator logicalTopN = (LogicalTopNOperator) input.getOp();

        PhysicalTopNOperator physicalTopN =
                new PhysicalTopNOperator(new OrderSpec(logicalTopN.getOrderByElements()),
                        logicalTopN.getLimit(),
                        logicalTopN.getOffset(),
                        logicalTopN.getPartitionByColumns(),
                        logicalTopN.getPartitionLimit(),
                        logicalTopN.getSortPhase(),
                        logicalTopN.getTopNType(),
                        logicalTopN.isSplit(),
                        false,
                        logicalTopN.getPredicate(),
                        logicalTopN.getProjection());
        return Lists.newArrayList(OptExpression.create(physicalTopN, input.getInputs()));
    }
}
