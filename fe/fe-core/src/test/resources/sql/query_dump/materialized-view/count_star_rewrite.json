{"statement": "SELECT count(*) AS mock_068\nFROM db_mock_000.tbl_mock_001\nWHERE tbl_mock_001.mock_019 = '2024-06-21'", "table_meta": {"db_mock_000.tbl_mock_001": "CREATE TABLE db_mock_000.tbl_mock_001 (\nmock_009 int(11) NOT NULL ,\nmock_012 int(11) ,\nmock_030 tinyint(4) ,\nmock_031 tinyint(4) ,\nmock_019 date NOT NULL ,\nmock_041 varchar(2000) ,\nmock_059 varchar(500) ,\nmock_017 varchar(500) ,\nmock_050 varchar(65533) ,\nmock_045 json ,\nmock_051 json ,\nmock_020 int(11) ,\nmock_047 json ,\nmock_049 int(11) ,\nmock_046 varchar(65533) ,\nmock_040 varchar(65533) ,\nmock_023 varchar(65533) ,\nmock_054 varchar(65533) ,\nmock_003 varchar(2000) ,\nmock_043 varchar(2000) ,\nmock_042 varchar(200) ,\nmock_053 varchar(2000) ,\nmock_011 varchar(500) ,\nmock_021 varchar(200) ,\nmock_006 varchar(200) ,\nmock_010 varchar(300) ,\nmock_016 varchar(300) ,\nmock_037 varchar(65533) ,\nmock_018 varchar(65533) ,\nmock_005 varchar(200) ,\nmock_052 varchar(200) ,\nmock_038 varchar(500) ,\nmock_013 varchar(200) ,\nmock_004 varchar(300) ,\nmock_022 varchar(200) ,\nmock_036 varchar(200) ,\nmock_007 varchar(65533) ,\nmock_033 varchar(65533) ,\nmock_034 varchar(65533) ,\nmock_035 varchar(65533) ,\nmock_048 json ,\nmock_058 varchar(300) ,\nmock_008 varchar(300) ,\nmock_002 varchar(512) ,\nmock_014 datetime NOT NULL ,\nmock_032 bigint(20) NOT NULL ,\nmock_044 varchar(65533) ,\nmock_055 varchar(65533) ,\nmock_056 varchar(65533) ,\nmock_057 json ,\nmock_026 json ,\nmock_024 bigint(20) ,\nmock_025 varchar(65533) ,\nmock_015 int(11) ,\nmock_039 varchar(20) ,\nmock_029 tinyint(4) ,\nmock_028 bigint(20) ,\nmock_027 datetime DEFAULT CURRENT_TIMESTAMP ,\n  INDEX app_id_index (mock_004) USING BITMAP\n) ENGINE= OLAP \nDUPLICATE KEY(mock_009, mock_012, mock_030, mock_031)\nPARTITION BY RANGE(mock_019)\n(PARTITION p20240405 VALUES [(\"2024-04-05\"), (\"2024-04-06\")),\nPARTITION p20240406 VALUES [(\"2024-04-06\"), (\"2024-04-07\")),\nPARTITION p20240407 VALUES [(\"2024-04-07\"), (\"2024-04-08\")),\nPARTITION p20240408 VALUES [(\"2024-04-08\"), (\"2024-04-09\")),\nPARTITION p20240409 VALUES [(\"2024-04-09\"), (\"2024-04-10\")),\nPARTITION p20240410 VALUES [(\"2024-04-10\"), (\"2024-04-11\")),\nPARTITION p20240411 VALUES [(\"2024-04-11\"), (\"2024-04-12\")),\nPARTITION p20240412 VALUES [(\"2024-04-12\"), (\"2024-04-13\")),\nPARTITION p20240413 VALUES [(\"2024-04-13\"), (\"2024-04-14\")),\nPARTITION p20240414 VALUES [(\"2024-04-14\"), (\"2024-04-15\")),\nPARTITION p20240415 VALUES [(\"2024-04-15\"), (\"2024-04-16\")),\nPARTITION p20240416 VALUES [(\"2024-04-16\"), (\"2024-04-17\")),\nPARTITION p20240417 VALUES [(\"2024-04-17\"), (\"2024-04-18\")),\nPARTITION p20240418 VALUES [(\"2024-04-18\"), (\"2024-04-19\")),\nPARTITION p20240419 VALUES [(\"2024-04-19\"), (\"2024-04-20\")),\nPARTITION p20240420 VALUES [(\"2024-04-20\"), (\"2024-04-21\")),\nPARTITION p20240421 VALUES [(\"2024-04-21\"), (\"2024-04-22\")),\nPARTITION p20240422 VALUES [(\"2024-04-22\"), (\"2024-04-23\")),\nPARTITION p20240423 VALUES [(\"2024-04-23\"), (\"2024-04-24\")),\nPARTITION p20240424 VALUES [(\"2024-04-24\"), (\"2024-04-25\")),\nPARTITION p20240425 VALUES [(\"2024-04-25\"), (\"2024-04-26\")),\nPARTITION p20240426 VALUES [(\"2024-04-26\"), (\"2024-04-27\")),\nPARTITION p20240427 VALUES [(\"2024-04-27\"), (\"2024-04-28\")),\nPARTITION p20240428 VALUES [(\"2024-04-28\"), (\"2024-04-29\")),\nPARTITION p20240429 VALUES [(\"2024-04-29\"), (\"2024-04-30\")),\nPARTITION p20240430 VALUES [(\"2024-04-30\"), (\"2024-05-01\")),\nPARTITION p20240501 VALUES [(\"2024-05-01\"), (\"2024-05-02\")),\nPARTITION p20240502 VALUES [(\"2024-05-02\"), (\"2024-05-03\")),\nPARTITION p20240503 VALUES [(\"2024-05-03\"), (\"2024-05-04\")),\nPARTITION p20240504 VALUES [(\"2024-05-04\"), (\"2024-05-05\")),\nPARTITION p20240505 VALUES [(\"2024-05-05\"), (\"2024-05-06\")),\nPARTITION p20240506 VALUES [(\"2024-05-06\"), (\"2024-05-07\")),\nPARTITION p20240507 VALUES [(\"2024-05-07\"), (\"2024-05-08\")),\nPARTITION p20240508 VALUES [(\"2024-05-08\"), (\"2024-05-09\")),\nPARTITION p20240509 VALUES [(\"2024-05-09\"), (\"2024-05-10\")),\nPARTITION p20240510 VALUES [(\"2024-05-10\"), (\"2024-05-11\")),\nPARTITION p20240511 VALUES [(\"2024-05-11\"), (\"2024-05-12\")),\nPARTITION p20240512 VALUES [(\"2024-05-12\"), (\"2024-05-13\")),\nPARTITION p20240513 VALUES [(\"2024-05-13\"), (\"2024-05-14\")),\nPARTITION p20240514 VALUES [(\"2024-05-14\"), (\"2024-05-15\")),\nPARTITION p20240515 VALUES [(\"2024-05-15\"), (\"2024-05-16\")),\nPARTITION p20240516 VALUES [(\"2024-05-16\"), (\"2024-05-17\")),\nPARTITION p20240517 VALUES [(\"2024-05-17\"), (\"2024-05-18\")),\nPARTITION p20240518 VALUES [(\"2024-05-18\"), (\"2024-05-19\")),\nPARTITION p20240519 VALUES [(\"2024-05-19\"), (\"2024-05-20\")),\nPARTITION p20240520 VALUES [(\"2024-05-20\"), (\"2024-05-21\")),\nPARTITION p20240521 VALUES [(\"2024-05-21\"), (\"2024-05-22\")),\nPARTITION p20240522 VALUES [(\"2024-05-22\"), (\"2024-05-23\")),\nPARTITION p20240523 VALUES [(\"2024-05-23\"), (\"2024-05-24\")),\nPARTITION p20240524 VALUES [(\"2024-05-24\"), (\"2024-05-25\")),\nPARTITION p20240525 VALUES [(\"2024-05-25\"), (\"2024-05-26\")),\nPARTITION p20240526 VALUES [(\"2024-05-26\"), (\"2024-05-27\")),\nPARTITION p20240527 VALUES [(\"2024-05-27\"), (\"2024-05-28\")),\nPARTITION p20240528 VALUES [(\"2024-05-28\"), (\"2024-05-29\")),\nPARTITION p20240529 VALUES [(\"2024-05-29\"), (\"2024-05-30\")),\nPARTITION p20240530 VALUES [(\"2024-05-30\"), (\"2024-05-31\")),\nPARTITION p20240531 VALUES [(\"2024-05-31\"), (\"2024-06-01\")),\nPARTITION p20240601 VALUES [(\"2024-06-01\"), (\"2024-06-02\")),\nPARTITION p20240602 VALUES [(\"2024-06-02\"), (\"2024-06-03\")),\nPARTITION p20240603 VALUES [(\"2024-06-03\"), (\"2024-06-04\")),\nPARTITION p20240604 VALUES [(\"2024-06-04\"), (\"2024-06-05\")),\nPARTITION p20240605 VALUES [(\"2024-06-05\"), (\"2024-06-06\")),\nPARTITION p20240606 VALUES [(\"2024-06-06\"), (\"2024-06-07\")),\nPARTITION p20240607 VALUES [(\"2024-06-07\"), (\"2024-06-08\")),\nPARTITION p20240608 VALUES [(\"2024-06-08\"), (\"2024-06-09\")),\nPARTITION p20240609 VALUES [(\"2024-06-09\"), (\"2024-06-10\")),\nPARTITION p20240610 VALUES [(\"2024-06-10\"), (\"2024-06-11\")),\nPARTITION p20240611 VALUES [(\"2024-06-11\"), (\"2024-06-12\")),\nPARTITION p20240612 VALUES [(\"2024-06-12\"), (\"2024-06-13\")),\nPARTITION p20240613 VALUES [(\"2024-06-13\"), (\"2024-06-14\")),\nPARTITION p20240614 VALUES [(\"2024-06-14\"), (\"2024-06-15\")),\nPARTITION p20240615 VALUES [(\"2024-06-15\"), (\"2024-06-16\")),\nPARTITION p20240616 VALUES [(\"2024-06-16\"), (\"2024-06-17\")),\nPARTITION p20240617 VALUES [(\"2024-06-17\"), (\"2024-06-18\")),\nPARTITION p20240618 VALUES [(\"2024-06-18\"), (\"2024-06-19\")),\nPARTITION p20240619 VALUES [(\"2024-06-19\"), (\"2024-06-20\")),\nPARTITION p20240620 VALUES [(\"2024-06-20\"), (\"2024-06-21\")),\nPARTITION p20240621 VALUES [(\"2024-06-21\"), (\"2024-06-22\")),\nPARTITION p20240622 VALUES [(\"2024-06-22\"), (\"2024-06-23\")),\nPARTITION p20240623 VALUES [(\"2024-06-23\"), (\"2024-06-24\")),\nPARTITION p20240624 VALUES [(\"2024-06-24\"), (\"2024-06-25\")),\nPARTITION p20240625 VALUES [(\"2024-06-25\"), (\"2024-06-26\")),\nPARTITION p20240626 VALUES [(\"2024-06-26\"), (\"2024-06-27\")),\nPARTITION p20240627 VALUES [(\"2024-06-27\"), (\"2024-06-28\")),\nPARTITION p20240628 VALUES [(\"2024-06-28\"), (\"2024-06-29\")),\nPARTITION p20240629 VALUES [(\"2024-06-29\"), (\"2024-06-30\")),\nPARTITION p20240630 VALUES [(\"2024-06-30\"), (\"2024-07-01\")),\nPARTITION p20240701 VALUES [(\"2024-07-01\"), (\"2024-07-02\")),\nPARTITION p20240702 VALUES [(\"2024-07-02\"), (\"2024-07-03\")),\nPARTITION p20240703 VALUES [(\"2024-07-03\"), (\"2024-07-04\")),\nPARTITION p20240704 VALUES [(\"2024-07-04\"), (\"2024-07-05\")),\nPARTITION p20240705 VALUES [(\"2024-07-05\"), (\"2024-07-06\")),\nPARTITION p20240706 VALUES [(\"2024-07-06\"), (\"2024-07-07\")),\nPARTITION p20240707 VALUES [(\"2024-07-07\"), (\"2024-07-08\")),\nPARTITION p20240708 VALUES [(\"2024-07-08\"), (\"2024-07-09\")),\nPARTITION p20240709 VALUES [(\"2024-07-09\"), (\"2024-07-10\")),\nPARTITION p20240710 VALUES [(\"2024-07-10\"), (\"2024-07-11\")),\nPARTITION p20240711 VALUES [(\"2024-07-11\"), (\"2024-07-12\")),\nPARTITION p20240712 VALUES [(\"2024-07-12\"), (\"2024-07-13\")),\nPARTITION p20240713 VALUES [(\"2024-07-13\"), (\"2024-07-14\")),\nPARTITION p20240714 VALUES [(\"2024-07-14\"), (\"2024-07-15\")),\nPARTITION p20240715 VALUES [(\"2024-07-15\"), (\"2024-07-16\")),\nPARTITION p20240716 VALUES [(\"2024-07-16\"), (\"2024-07-17\")),\nPARTITION p20240717 VALUES [(\"2024-07-17\"), (\"2024-07-18\")),\nPARTITION p20240718 VALUES [(\"2024-07-18\"), (\"2024-07-19\")),\nPARTITION p20240719 VALUES [(\"2024-07-19\"), (\"2024-07-20\")),\nPARTITION p20240720 VALUES [(\"2024-07-20\"), (\"2024-07-21\")),\nPARTITION p20240721 VALUES [(\"2024-07-21\"), (\"2024-07-22\")),\nPARTITION p20240722 VALUES [(\"2024-07-22\"), (\"2024-07-23\")),\nPARTITION p20240723 VALUES [(\"2024-07-23\"), (\"2024-07-24\")),\nPARTITION p20240724 VALUES [(\"2024-07-24\"), (\"2024-07-25\")),\nPARTITION p20240725 VALUES [(\"2024-07-25\"), (\"2024-07-26\")),\nPARTITION p20240726 VALUES [(\"2024-07-26\"), (\"2024-07-27\")),\nPARTITION p20240727 VALUES [(\"2024-07-27\"), (\"2024-07-28\")),\nPARTITION p20240728 VALUES [(\"2024-07-28\"), (\"2024-07-29\")),\nPARTITION p20240729 VALUES [(\"2024-07-29\"), (\"2024-07-30\")),\nPARTITION p20240730 VALUES [(\"2024-07-30\"), (\"2024-07-31\")),\nPARTITION p20240731 VALUES [(\"2024-07-31\"), (\"2024-08-01\")),\nPARTITION p20240801 VALUES [(\"2024-08-01\"), (\"2024-08-02\")),\nPARTITION p20240802 VALUES [(\"2024-08-02\"), (\"2024-08-03\")))\nDISTRIBUTED BY HASH(mock_041) BUCKETS 4 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"bloom_filter_columns\" = \"mock_041, mock_038, mock_058\",\n\"dynamic_partition.enable\" = \"true\",\n\"dynamic_partition.time_unit\" = \"DAY\",\n\"dynamic_partition.time_zone\" = \"Asia/Shanghai\",\n\"dynamic_partition.start\" = \"-365\",\n\"dynamic_partition.end\" = \"2\",\n\"dynamic_partition.prefix\" = \"p\",\n\"dynamic_partition.buckets\" = \"11\",\n\"dynamic_partition.history_partition_num\" = \"0\"\n);", "db_mock_060.tbl_mock_061": "CREATE MATERIALIZED VIEW `tbl_mock_061` (`mock_019`, `mock_038`, `mock_036`, `mock_004`, `mock_063`, `mock_062`)\n\nPARTITION BY (mock_019)\n\nDISTRIBUTED BY HASH(mock_038) BUCKETS 2 \nREFRESH MANUAL\nPROPERTIES (\n\"replication_num\" = \"1\"\n)\nAS SELECT tbl_mock_001.mock_019, tbl_mock_001.mock_038, tbl_mock_001.mock_036, tbl_mock_001.mock_004, time_slice(from_unixtime(tbl_mock_001.mock_032 / 1000), INTERVAL 5 minute, ceil) AS mock_063, count(1) AS mock_062\nFROM db_mock_000.tbl_mock_001\nGROUP BY tbl_mock_001.mock_019, tbl_mock_001.mock_038, tbl_mock_001.mock_036, tbl_mock_001.mock_004, mock_063;", "db_mock_064.tbl_mock_065": "CREATE MATERIALIZED VIEW `tbl_mock_065` (`mock_019`, `mock_004`, `mock_036`, `mock_063`, `mock_062`, `mock_066`)\n\nPARTITION BY (mock_019)\n\nDISTRIBUTED BY HASH(mock_004) BUCKETS 1 \nREFRESH MANUAL\nPROPERTIES (\n\"replication_num\" = \"1\"\n)\nAS SELECT tbl_mock_061.mock_019, tbl_mock_061.mock_004, tbl_mock_061.mock_036, tbl_mock_061.mock_063, sum(tbl_mock_061.mock_062) AS mock_062, count(DISTINCT tbl_mock_061.mock_038) AS mock_066\nFROM db_mock_060.tbl_mock_061\nGROUP BY tbl_mock_061.mock_019, tbl_mock_061.mock_004, tbl_mock_061.mock_036, tbl_mock_061.mock_063;", "db_mock_064.tbl_mock_067": "CREATE MATERIALIZED VIEW `tbl_mock_067` (`mock_019`, `mock_004`, `mock_063`, `mock_062`, `mock_066`)\n\nPARTITION BY (mock_019)\n\nDISTRIBUTED BY HASH(mock_004) BUCKETS 1 \nREFRESH MANUAL\nPROPERTIES (\n\"replication_num\" = \"1\"\n)\nAS SELECT tbl_mock_061.mock_019, tbl_mock_061.mock_004, tbl_mock_061.mock_063, sum(tbl_mock_061.mock_062) AS mock_062, count(DISTINCT tbl_mock_061.mock_038) AS mock_066\nFROM db_mock_060.tbl_mock_061\nGROUP BY tbl_mock_061.mock_019, tbl_mock_061.mock_004, tbl_mock_061.mock_063;"}, "table_row_count": {}, "column_statistics": {"db_mock_064.tbl_mock_065": {"mock_019": "[-Infinity, Infinity, 0.0, 0.0, 1.0] ESTIMATE", "mock_062": "[-Infinity, Infinity, 0.0, 0.0, 1.0] ESTIMATE"}, "db_mock_064.tbl_mock_067": {"mock_019": "[-Infinity, Infinity, 0.0, 0.0, 1.0] ESTIMATE", "mock_062": "[-Infinity, Infinity, 0.0, 0.0, 1.0] ESTIMATE"}}, "explain_info": "PLAN FRAGMENT 0(F00)\n  Output Exprs:59: count\n  Input Partition: <PERSON>NDOM\n  RESULT SINK\n\n  3:Project\n  |  output columns:\n  |  59 <-> [80: count, BIGINT, true]\n  |  79 <-> coalesce[([80: count, BIGINT, true], 0); args: BIGINT; result: BIGINT; args nullable: true; result nullable: true]\n  |  80 <-> clone([80: count, BIGINT, true])\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  \n  2:AGGREGATE (update finalize)\n  |  aggregate: sum[([75: mock_062, BIGINT, true]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: true]\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-<PERSON>, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  \n  1:Project\n  |  output columns:\n  |  75 <-> [75: mock_062, BIGINT, true]\n  |  cardinality: 1\n  |  column statistics: \n  |  * mock_062-->[-Infinity, Infinity, 0.0, 0.0, 1.0] ESTIMATE\n  |  \n  0:OlapScanNode\n     table: mock_067, rollup: mock_067\n     preAggregation: on\n     Predicates: [72: mock_019, DATE, false] = '2024-06-21'\n     partitionsRatio=0/120, tabletsRatio=0/0\n     tabletList=\n     actualRows=0, avgRowSize=0.0\n     MaterializedView: true\n     cardinality: 1\n     column statistics: \n     * mock_019-->[1.7188992E9, 1.7188992E9, 0.0, 0.0, 1.0] ESTIMATE\n     * mock_062-->[-Infinity, Infinity, 0.0, 0.0, 1.0] ESTIMATE\n", "session_variables": "{\"partial_update_mode\":\"auto\",\"cbo_cte_reuse\":false,\"character_set_connection\":\"utf8\",\"cbo_use_correlated_join_estimate\":true,\"enable_insert_strict\":false,\"enable_connector_adaptive_io_tasks\":true,\"tx_isolation\":\"REPEATABLE-READ\",\"enable_hive_metadata_cache_with_insert\":false,\"cbo_cte_reuse_rate_v2\":1.15,\"character_set_results\":\"utf8\",\"enable_iceberg_column_statistics\":true,\"materialized_view_union_rewrite_mode\":0,\"enable_count_star_optimization\":true,\"materialized_view_max_relation_mapping_size\":10,\"global_runtime_filter_build_min_size\":131072,\"enable_iceberg_identity_column_optimize\":true,\"query_excluding_mv_names\":\"\",\"enable_rewrite_simple_agg_to_meta_scan\":false,\"enable_adaptive_sink_dop\":false,\"consistent_hash_virtual_number\":32,\"enable_profile\":true,\"load_mem_limit\":0,\"cbo_eq_base_type\":\"varchar\",\"enable_materialized_view_for_insert\":false,\"large_decimal_underlying_type\":\"panic\",\"sql_safe_updates\":0,\"runtime_filter_early_return_selectivity\":0.05,\"enable_local_shuffle_agg\":true,\"disable_function_fold_constants\":false,\"enable_query_queue\":true,\"select_ratio_threshold\":0.15,\"query_delivery_timeout\":300,\"collation_database\":\"utf8_general_ci\",\"spill_mem_table_size\":104857600,\"follower_query_forward_mode\":\"\",\"orc_use_column_names\":false,\"cbo_use_lock_db\":false,\"new_planner_agg_stage\":0,\"enable_strict_order_by\":true,\"hash_join_interpolate_passthrough\":false,\"use_compute_nodes\":-1,\"collation_connection\":\"utf8_general_ci\",\"enable_rewrite_bitmap_union_to_bitamp_agg\":false,\"enable_force_rule_based_mv_rewrite\":false,\"enable_array_distinct_after_agg_opt\":true,\"resource_group\":\"\",\"enable_materialized_view_plan_cache\":true,\"spill_operator_max_bytes\":1048576000,\"cbo_max_reorder_node_use_dp\":10,\"enable_result_sink_accumulate\":true,\"enable_hive_column_stats\":true,\"enable_async_profile\":true,\"enable_groupby_use_output_alias\":false,\"global_runtime_filter_wait_timeout\":20,\"forward_to_leader\":false,\"count_distinct_column_buckets\":1024,\"cross_join_cost_penalty\":1000000,\"query_cache_agg_cardinality_limit\":5000000,\"enable_pipeline_query_statistic\":true,\"cboPushDownAggregateMode_v1\":-1,\"window_partition_mode\":1,\"enable_deliver_batch_fragments\":true,\"enable_tablet_internal_parallel_v2\":true,\"interpolate_passthrough\":true,\"enable_incremental_mv\":false,\"cbo_push_down_topn_limit\":0,\"SQL_AUTO_IS_NULL\":false,\"event_scheduler\":\"OFF\",\"max_pipeline_dop\":64,\"broadcast_right_table_scale_factor\":10,\"materialized_view_rewrite_mode\":\"DEFAULT\",\"enable_simplify_case_when\":true,\"runtime_join_filter_push_down_limit\":1024000,\"big_query_log_cpu_second_threshold\":480,\"div_precision_increment\":4,\"runtime_adaptive_dop_max_block_rows_per_driver_seq\":16384,\"log_rejected_record_num\":0,\"cbo_push_down_distinct_below_window\":true,\"sql_mode_v2\":32,\"prefer_cte_rewrite\":false,\"optimizer_materialized_view_timelimit\":1000,\"hdfs_backend_selector_scan_range_shuffle\":false,\"pipeline_profile_level\":1,\"parallel_fragment_exec_instance_num\":2,\"max_scan_key_num\":-1,\"net_read_timeout\":60,\"streaming_preaggregation_mode\":\"auto\",\"hive_partition_stats_sample_size\":5000,\"enable_mv_planner\":false,\"enable_collect_table_level_scan_stats\":true,\"query_debug_options\":\"\",\"profile_timeout\":2,\"cbo_push_down_aggregate\":\"global\",\"spill_encode_level\":7,\"enable_query_dump\":false,\"global_runtime_filter_build_max_size\":67108864,\"enable_rewrite_sum_by_associative_rule\":true,\"query_cache_hot_partition_num\":3,\"enable_prune_complex_types\":true,\"query_cache_type\":0,\"max_parallel_scan_instance_num\":-1,\"query_cache_entry_max_rows\":409600,\"enable_mv_optimizer_trace_log\":false,\"connector_io_tasks_per_scan_operator\":16,\"enable_materialized_view_union_rewrite\":true,\"sql_quote_show_create\":true,\"scan_or_to_union_threshold\":50000000,\"enable_materialized_view_rewrite_partition_compensate\":true,\"enable_exchange_pass_through\":true,\"runtime_profile_report_interval\":10,\"query_cache_entry_max_bytes\":4194304,\"enable_partition_column_value_only_optimization\":true,\"enable_iceberg_ndv\":true,\"enable_exchange_perf\":false,\"workgroup_id\":0,\"enable_rewrite_groupingsets_to_union_all\":false,\"transmission_compression_type\":\"LZ4\",\"interactive_timeout\":3600,\"use_page_cache\":true,\"big_query_log_scan_bytes_threshold\":10737418240,\"collation_server\":\"utf8_general_ci\",\"cbo_decimal_cast_string_strict\":true,\"tablet_internal_parallel_mode\":\"auto\",\"enable_pipeline\":true,\"spill_mode\":\"auto\",\"enable_query_debug_trace\":false,\"cbo_materialized_view_rewrite_related_mvs_limit\":64,\"enable_show_all_variables\":false,\"full_sort_max_buffered_bytes\":16777216,\"wait_timeout\":28800,\"enable_query_tablet_affinity\":false,\"transmission_encode_level\":7,\"query_including_mv_names\":\"\",\"transaction_isolation\":\"REPEATABLE-READ\",\"enable_global_runtime_filter\":true,\"enable_load_profile\":false,\"enable_plan_validation\":true,\"load_transmission_compression_type\":\"NO_COMPRESSION\",\"global_runtime_filter_rpc_http_min_size\":67108864,\"cbo_materialized_view_rewrite_rule_output_limit\":3,\"cbo_enable_low_cardinality_optimize\":true,\"scan_use_query_mem_ratio\":0.3,\"new_planner_optimize_timeout\":3000,\"enable_outer_join_reorder\":true,\"force_schedule_local\":false,\"hudi_mor_force_jni_reader\":false,\"enable_agg_spill_preaggregation\":true,\"full_sort_late_materialization\":false,\"cbo_enable_greedy_join_reorder\":true,\"range_pruner_max_predicate\":100,\"enable_rbo_table_prune\":false,\"spillable_operator_mask\":-1,\"rpc_http_min_size\":2147482624,\"cbo_debug_alive_backend_number\":0,\"global_runtime_filter_probe_min_size\":102400,\"scan_or_to_union_limit\":1,\"enable_cbo_table_prune\":false,\"enable_parallel_merge\":true,\"cbo_materialized_view_rewrite_candidate_limit\":12,\"nested_mv_rewrite_max_level\":3,\"big_query_profile_threshold\":\"0s\",\"net_write_timeout\":60,\"cbo_prune_shuffle_column_rate\":0.1,\"enable_persistent_index_by_default\":false,\"hash_join_push_down_right_table\":true,\"pipeline_sink_dop\":0,\"broadcast_row_limit\":15000000,\"enable_populate_block_cache\":true,\"exec_mem_limit\":17179869184,\"enable_sort_aggregate\":false,\"query_cache_force_populate\":false,\"runtime_filter_on_exchange_node\":false,\"disable_join_reorder\":false,\"enable_rule_based_materialized_view_rewrite\":true,\"global_runtime_filter_rpc_timeout\":400,\"connector_scan_use_query_mem_ratio\":0.3,\"net_buffer_length\":16384,\"cbo_prune_subfield\":true,\"full_sort_max_buffered_rows\":1024000,\"query_timeout\":300,\"connector_io_tasks_slow_io_latency_ms\":50,\"cbo_max_reorder_node\":50,\"enable_distinct_column_bucketization\":false,\"enable_big_query_log\":true,\"analyze_mv\":\"sample\",\"runtime_filter_scan_wait_time\":20,\"enable_sync_materialized_view_rewrite\":true,\"prefer_compute_node\":false,\"enable_strict_type\":false,\"enable_table_prune_on_update\":false,\"group_concat_max_len\":65535,\"parse_tokens_limit\":3500000,\"chunk_size\":4096,\"global_runtime_filter_probe_min_selectivity\":0.5,\"query_mem_limit\":0,\"enable_filter_unused_columns_in_scan_stage\":false,\"enable_scan_block_cache\":false,\"enable_materialized_view_single_table_view_delta_rewrite\":false,\"enable_prune_complex_types_in_unnest\":true,\"auto_increment_increment\":1,\"sql_dialect\":\"StarRocks\",\"big_query_log_scan_rows_threshold\":1000000000,\"character_set_client\":\"utf8\",\"autocommit\":true,\"enable_column_expr_predicate\":false,\"enable_runtime_adaptive_dop\":false,\"cbo_cte_max_limit\":10,\"storage_engine\":\"olap\",\"enable_optimizer_trace_log\":false,\"spill_operator_min_bytes\":10485760,\"cbo_enable_dp_join_reorder\":true,\"tx_visible_wait_timeout\":10,\"materialized_view_join_same_table_permutation_limit\":5,\"enable_materialized_view_view_delta_rewrite\":true,\"cbo_max_reorder_node_use_exhaustive\":4,\"enable_sql_digest\":false,\"spill_mem_table_num\":2,\"enable_spill\":false,\"enable_materialized_view_rewrite_greedy_mode\":false,\"pipeline_dop\":0,\"single_node_exec_plan\":false,\"join_implementation_mode_v2\":\"auto\",\"sql_select_limit\":9223372036854775807,\"enable_materialized_view_rewrite\":true,\"statistic_collect_parallel\":1,\"hdfs_backend_selector_hash_algorithm\":\"consistent\",\"enable_expr_prune_partition\":true,\"enable_topn_runtime_filter\":true,\"disable_colocate_join\":false,\"max_pushdown_conditions_per_column\":-1,\"default_table_compression\":\"lz4_frame\",\"runtime_adaptive_dop_max_output_amplification_factor\":0,\"skew_join_rand_range\":1000,\"choose_execute_instances_mode\":\"LOCALITY\",\"innodb_read_only\":true,\"spill_mem_limit_threshold\":0.8,\"cbo_reorder_threshold_use_exhaustive\":6,\"enable_predicate_reorder\":false,\"enable_query_cache\":false,\"transaction_read_only\":\"OFF\",\"max_allowed_packet\":10000000,\"time_zone\":\"Asia/Shanghai\",\"enable_multicolumn_global_runtime_filter\":false,\"character_set_server\":\"utf8\",\"cbo_use_nth_exec_plan\":0,\"io_tasks_per_scan_operator\":4,\"parallel_exchange_instance_num\":-1,\"enable_shared_scan\":false,\"cbo_derive_range_join_predicate\":false,\"allow_default_partition\":false,\"enable_pipeline_level_shuffle\":true}", "be_number": 11, "be_core_stat": {"numOfHardwareCoresPerBe": "{\"9105706\":64,\"5219005\":64,\"14091939\":64,\"11789062\":64,\"14091938\":64,\"17823178\":64,\"11333242\":64,\"14091937\":64,\"55017360\":96,\"9178096\":64,\"9244016\":64}", "cachedAvgNumOfHardwareCores": 66}, "exception": [], "version": "3.1.12", "commit_version": "fc2b9c3"}