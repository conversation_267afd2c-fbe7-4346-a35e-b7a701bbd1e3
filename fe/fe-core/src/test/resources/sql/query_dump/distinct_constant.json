{"statement": "SELECT   count(DISTINCT 1),   count(<PERSON>IS<PERSON><PERSON><PERSON> null),   count(DISTINCT a),   count(DISTINCT b),   count(DISTINCT (a + b)) FROM testData;\n", "table_meta": {"test_filter_3b1838b0_2170_11ef_8c32_00163e237e98.testData": "CREATE TABLE `testData` (\n  `a` bigint(20) NULL COMMENT \"\",\n  `b` bigint(20) NULL COMMENT \"\"\n) ENGINE=OLAP \nDUPLICATE KEY(`a`, `b`)\nDISTRIBUTED BY HASH(`a`) BUCKETS 3 \nPROPERTIES (\n\"compression\" = \"LZ4\",\n\"fast_schema_evolution\" = \"true\",\n\"replicated_storage\" = \"true\",\n\"replication_num\" = \"1\"\n);"}, "table_row_count": {"test_filter_3b1838b0_2170_11ef_8c32_00163e237e98.testData": {"testData": 7}}, "column_statistics": {"test_filter_3b1838b0_2170_11ef_8c32_00163e237e98.testData": {"a": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "b": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN"}}, "explain_info": "PLAN FRAGMENT 0(F02)\n  Output Exprs:4: count | 5: count | 6: count | 7: count | 8: count\n  Input Partition: UNPARTITIONED\n  RESULT SINK\n\n  47:NESTLOOP JOIN\n  |  join op: CROSS JOIN\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  |----46:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1\n  |    \n  37:NESTLOOP JOIN\n  |  join op: CROSS JOIN\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-<PERSON>, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  |----36:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1\n  |    \n  27:NESTLOOP JOIN\n  |  join op: CROSS JOIN\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  |----26:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1\n  |    \n  17:NESTLOOP JOIN\n  |  join op: CROSS JOIN\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  \n  |----16:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1\n  |    \n  8:AGGREGATE (merge finalize)\n  |  aggregate: count[([4: count, BIGINT, false]); args: TINYINT; result: BIGINT; args nullable: true; result nullable: false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  \n  7:AGGREGATE (update serialize)\n  |  aggregate: count[(1); args: TINYINT; result: BIGINT; args nullable: false; result nullable: false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  \n  6:AGGREGATE (merge serialize)\n  |  cardinality: 1\n  |  column statistics: \n  |  \n  5:EXCHANGE\n     distribution type: GATHER\n     cardinality: 1\n\nPLAN FRAGMENT 1(F16)\n\n  Input Partition: UNPARTITIONED\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 46\n\n  45:AGGREGATE (merge finalize)\n  |  aggregate: count[([8: count, BIGINT, false]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  44:EXCHANGE\n     distribution type: GATHER\n     cardinality: 1\n\nPLAN FRAGMENT 2(F15)\n\n  Input Partition: HASH_PARTITIONED: 13: expr\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 44\n\n  43:AGGREGATE (update serialize)\n  |  aggregate: count[([13: expr, BIGINT, true]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  42:AGGREGATE (merge serialize)\n  |  group by: [13: expr, BIGINT, true]\n  |  cardinality: 2\n  |  column statistics: \n  |  * expr-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  41:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [13: expr, BIGINT, true]\n     cardinality: 4\n\nPLAN FRAGMENT 3(F14)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 13: expr\n  OutPut Exchange Id: 41\n\n  40:AGGREGATE (update serialize)\n  |  STREAMING\n  |  group by: [13: expr, BIGINT, true]\n  |  cardinality: 4\n  |  column statistics: \n  |  * expr-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  39:Project\n  |  output columns:\n  |  13 <-> [3: expr, BIGINT, true]\n  |  cardinality: 7\n  |  column statistics: \n  |  * expr-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  38:EXCHANGE\n     distribution type: SHUFFLE\n     cardinality: 7\n\nPLAN FRAGMENT 4(F12)\n\n  Input Partition: UNPARTITIONED\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 36\n\n  35:AGGREGATE (merge finalize)\n  |  aggregate: count[([7: count, BIGINT, false]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  34:EXCHANGE\n     distribution type: GATHER\n     cardinality: 1\n\nPLAN FRAGMENT 5(F11)\n\n  Input Partition: HASH_PARTITIONED: 12: b\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 34\n\n  33:AGGREGATE (update serialize)\n  |  aggregate: count[([12: b, BIGINT, true]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  32:AGGREGATE (merge serialize)\n  |  group by: [12: b, BIGINT, true]\n  |  cardinality: 2\n  |  column statistics: \n  |  * b-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  31:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [12: b, BIGINT, true]\n     cardinality: 4\n\nPLAN FRAGMENT 6(F10)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 12: b\n  OutPut Exchange Id: 31\n\n  30:AGGREGATE (update serialize)\n  |  STREAMING\n  |  group by: [12: b, BIGINT, true]\n  |  cardinality: 4\n  |  column statistics: \n  |  * b-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  29:Project\n  |  output columns:\n  |  12 <-> [2: b, BIGINT, true]\n  |  cardinality: 7\n  |  column statistics: \n  |  * b-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  28:EXCHANGE\n     distribution type: SHUFFLE\n     cardinality: 7\n\nPLAN FRAGMENT 7(F08)\n\n  Input Partition: UNPARTITIONED\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 26\n\n  25:AGGREGATE (merge finalize)\n  |  aggregate: count[([6: count, BIGINT, false]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  24:EXCHANGE\n     distribution type: GATHER\n     cardinality: 1\n\nPLAN FRAGMENT 8(F07)\n\n  Input Partition: HASH_PARTITIONED: 11: a\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 24\n\n  23:AGGREGATE (update serialize)\n  |  aggregate: count[([11: a, BIGINT, true]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  22:AGGREGATE (merge serialize)\n  |  group by: [11: a, BIGINT, true]\n  |  cardinality: 2\n  |  column statistics: \n  |  * a-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  21:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [11: a, BIGINT, true]\n     cardinality: 4\n\nPLAN FRAGMENT 9(F06)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 11: a\n  OutPut Exchange Id: 21\n\n  20:AGGREGATE (update serialize)\n  |  STREAMING\n  |  group by: [11: a, BIGINT, true]\n  |  cardinality: 4\n  |  column statistics: \n  |  * a-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  19:Project\n  |  output columns:\n  |  11 <-> [1: a, BIGINT, true]\n  |  cardinality: 7\n  |  column statistics: \n  |  * a-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  18:EXCHANGE\n     distribution type: SHUFFLE\n     cardinality: 7\n\nPLAN FRAGMENT 10(F04)\n\n  Input Partition: UNPARTITIONED\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 16\n\n  15:AGGREGATE (merge finalize)\n  |  aggregate: count[([5: count, BIGINT, false]); args: BOOLEAN; result: BIGINT; args nullable: true; result nullable: false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  \n  14:AGGREGATE (update serialize)\n  |  aggregate: count[(NULL); args: BOOLEAN; result: BIGINT; args nullable: true; result nullable: false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 8.0, 1.0] ESTIMATE\n  |  \n  13:AGGREGATE (merge serialize)\n  |  cardinality: 1\n  |  column statistics: \n  |  \n  12:EXCHANGE\n     distribution type: GATHER\n     cardinality: 1\n\nPLAN FRAGMENT 11(F03)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 12\n\n  11:AGGREGATE (update serialize)\n  |  cardinality: 1\n  |  column statistics: \n  |  \n  10:Project\n  |  output columns:\n  |  10 <-> [1: a, BIGINT, true]\n  |  cardinality: 7\n  |  column statistics: \n  |  * a-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  9:EXCHANGE\n     distribution type: SHUFFLE\n     cardinality: 7\n\nPLAN FRAGMENT 12(F01)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 05\n\n  4:AGGREGATE (update serialize)\n  |  cardinality: 1\n  |  column statistics: \n  |  \n  3:Project\n  |  output columns:\n  |  9 <-> [1: a, BIGINT, true]\n  |  cardinality: 7\n  |  column statistics: \n  |  * a-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  2:EXCHANGE\n     distribution type: SHUFFLE\n     cardinality: 7\n\nPLAN FRAGMENT 13(F00)\n  Output Exprs:1: a | 2: b | 3: expr\n  Input Partition: RANDOM\n  MultiCastDataSinks:\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 02\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 09\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 18\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 28\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 38\n\n  1:Project\n  |  output columns:\n  |  1 <-> [1: a, BIGINT, true]\n  |  2 <-> [2: b, BIGINT, true]\n  |  3 <-> [1: a, BIGINT, true] + [2: b, BIGINT, true]\n  |  cardinality: 7\n  |  column statistics: \n  |  * a-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * b-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * expr-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  0:OlapScanNode\n     table: testData, rollup: testData\n     preAggregation: on\n     partitionsRatio=1/1, tabletsRatio=3/3\n     tabletList=88057,88059,88061\n     actualRows=7, avgRowSize=3.0\n     cardinality: 7\n     column statistics: \n     * a-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * b-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * expr-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n", "session_variables": "{\"partial_update_mode\":\"auto\",\"cbo_cte_reuse\":true,\"character_set_connection\":\"utf8\",\"cbo_use_correlated_join_estimate\":true,\"enable_insert_strict\":true,\"enable_connector_adaptive_io_tasks\":true,\"tx_isolation\":\"REPEATABLE-READ\",\"enable_hive_metadata_cache_with_insert\":false,\"hive_temp_staging_dir\":\"/tmp/starrocks\",\"cbo_cte_reuse_rate_v2\":1.15,\"spill_rand_ratio\":0.1,\"enable_datacache_io_adaptor\":false,\"character_set_results\":\"utf8\",\"materialized_view_union_rewrite_mode\":0,\"enable_count_star_optimization\":true,\"enable_iceberg_column_statistics\":true,\"thrift_plan_protocol\":\"binary\",\"enable_plan_serialize_concurrently\":true,\"materialized_view_max_relation_mapping_size\":10,\"global_runtime_filter_build_min_size\":131072,\"enable_iceberg_identity_column_optimize\":true,\"enable_view_based_mv_rewrite\":false,\"query_excluding_mv_names\":\"\",\"enable_rewrite_simple_agg_to_meta_scan\":false,\"enable_ukfk_opt\":false,\"enable_adaptive_sink_dop\":true,\"enable_ukfk_join_reorder\":false,\"consistent_hash_virtual_number\":128,\"warehouse\":\"default_warehouse\",\"enable_profile\":false,\"load_mem_limit\":0,\"spill_storage_volume\":\"\",\"cbo_eq_base_type\":\"varchar\",\"enable_materialized_view_for_insert\":false,\"large_decimal_underlying_type\":\"panic\",\"sql_safe_updates\":0,\"runtime_filter_early_return_selectivity\":0.05,\"enable_local_shuffle_agg\":true,\"disable_function_fold_constants\":false,\"enable_predicate_move_around\":true,\"enable_query_queue\":true,\"enable_cbo_view_based_mv_rewrite\":false,\"select_ratio_threshold\":0.15,\"enable_connector_sink_global_shuffle\":true,\"query_delivery_timeout\":300,\"collation_database\":\"utf8_general_ci\",\"spill_mem_table_size\":104857600,\"enable_gin_filter\":true,\"follower_query_forward_mode\":\"\",\"orc_use_column_names\":false,\"spill_enable_compaction\":true,\"cbo_use_lock_db\":false,\"new_planner_agg_stage\":0,\"enable_strict_order_by\":true,\"hash_join_interpolate_passthrough\":false,\"use_compute_nodes\":-1,\"enable_metadata_profile\":false,\"collation_connection\":\"utf8_general_ci\",\"enable_rewrite_bitmap_union_to_bitamp_agg\":false,\"enable_force_rule_based_mv_rewrite\":true,\"enable_array_distinct_after_agg_opt\":true,\"resource_group\":\"\",\"group_execution_max_groups\":128,\"scan_hive_partition_num_limit\":0,\"enable_materialized_view_plan_cache\":true,\"spill_operator_max_bytes\":1048576000,\"enable_materialized_view_agg_pushdown_rewrite\":false,\"cbo_max_reorder_node_use_dp\":10,\"cbo_prepare_metadata_thread_pool_size\":16,\"enable_result_sink_accumulate\":true,\"enable_hive_column_stats\":true,\"enable_async_profile\":true,\"enable_groupby_use_output_alias\":false,\"global_runtime_filter_wait_timeout\":20,\"forward_to_leader\":false,\"enable_prune_column_after_index_filter\":true,\"count_distinct_column_buckets\":1024,\"cross_join_cost_penalty\":1000000,\"query_cache_agg_cardinality_limit\":5000000,\"cboPushDownAggregateMode_v1\":-1,\"window_partition_mode\":1,\"enable_tablet_internal_parallel_v2\":true,\"interpolate_passthrough\":true,\"enable_incremental_mv\":false,\"cbo_push_down_topn_limit\":0,\"SQL_AUTO_IS_NULL\":false,\"event_scheduler\":\"OFF\",\"max_pipeline_dop\":64,\"broadcast_right_table_scale_factor\":10,\"materialized_view_rewrite_mode\":\"DEFAULT\",\"enable_read_iceberg_puffin_ndv\":true,\"enable_write_hive_external_table\":false,\"enable_simplify_case_when\":true,\"enable_aggregation_pipeline_share_limit\":true,\"runtime_join_filter_push_down_limit\":1024000,\"trace_log_mode\":\"command\",\"enable_scan_datacache\":false,\"big_query_log_cpu_second_threshold\":480,\"div_precision_increment\":4,\"runtime_adaptive_dop_max_block_rows_per_driver_seq\":16384,\"log_rejected_record_num\":0,\"enable_short_circuit\":false,\"cbo_push_down_distinct_below_window\":true,\"sql_mode_v2\":32,\"prefer_cte_rewrite\":false,\"optimizer_materialized_view_timelimit\":1000,\"hdfs_backend_selector_scan_range_shuffle\":false,\"pipeline_profile_level\":1,\"parallel_fragment_exec_instance_num\":1,\"max_scan_key_num\":-1,\"net_read_timeout\":60,\"streaming_preaggregation_mode\":\"auto\",\"hive_partition_stats_sample_size\":3000,\"enable_mv_planner\":false,\"enable_collect_table_level_scan_stats\":true,\"enable_fine_grained_range_predicate\":false,\"enable_populate_datacache\":true,\"enable_connector_sink_writer_scaling\":true,\"query_debug_options\":\"\",\"profile_timeout\":2,\"cbo_push_down_aggregate\":\"global\",\"spill_encode_level\":7,\"enable_query_dump\":false,\"global_runtime_filter_build_max_size\":67108864,\"enable_pushdown_or_predicate\":true,\"enable_rewrite_sum_by_associative_rule\":true,\"query_cache_hot_partition_num\":3,\"enable_prune_complex_types\":true,\"metadata_collect_query_timeout\":60,\"query_cache_type\":0,\"max_parallel_scan_instance_num\":-1,\"enable_dynamic_prune_scan_range\":true,\"query_cache_entry_max_rows\":409600,\"connector_io_tasks_per_scan_operator\":16,\"disable_spill_to_local_disk\":false,\"enable_materialized_view_union_rewrite\":true,\"sql_quote_show_create\":true,\"enable_constant_execute_in_fe\":true,\"scan_or_to_union_threshold\":50000000,\"enable_materialized_view_rewrite_partition_compensate\":true,\"enable_exchange_pass_through\":true,\"runtime_profile_report_interval\":10,\"query_cache_entry_max_bytes\":4194304,\"enable_partition_column_value_only_optimization\":true,\"connector_max_split_size\":67108864,\"array_low_cardinality_optimize\":true,\"interleaving_group_size\":10,\"enable_exchange_perf\":false,\"enable_prepare_stmt\":true,\"workgroup_id\":0,\"enable_pipeline_level_multi_partitioned_rf\":false,\"enable_rewrite_groupingsets_to_union_all\":false,\"spill_enable_direct_io\":false,\"transmission_compression_type\":\"NO_COMPRESSION\",\"interactive_timeout\":3600,\"use_page_cache\":true,\"big_query_log_scan_bytes_threshold\":10737418240,\"enable_execution_only\":false,\"collation_server\":\"utf8_general_ci\",\"cbo_decimal_cast_string_strict\":true,\"enable_hyperscan_vec\":true,\"enable_datacache_async_populate_mode\":false,\"cbo_enable_predicate_subfield_path\":true,\"tablet_internal_parallel_mode\":\"auto\",\"enable_pipeline\":true,\"spill_mode\":\"auto\",\"allow_hive_without_partition_filter\":true,\"enable_query_debug_trace\":false,\"cbo_materialized_view_rewrite_related_mvs_limit\":64,\"enable_lake_tablet_internal_parallel\":false,\"enable_show_all_variables\":false,\"full_sort_max_buffered_bytes\":16777216,\"catalog\":\"default_catalog\",\"wait_timeout\":28800,\"max_buckets_per_be_to_use_balancer_assignment\":6,\"enable_query_tablet_affinity\":false,\"transmission_encode_level\":7,\"query_including_mv_names\":\"\",\"transaction_isolation\":\"REPEATABLE-READ\",\"enable_global_runtime_filter\":true,\"enable_load_profile\":false,\"enable_rewrite_simple_agg_to_hdfs_scan\":false,\"enable_plan_validation\":true,\"load_transmission_compression_type\":\"NO_COMPRESSION\",\"global_runtime_filter_rpc_http_min_size\":67108864,\"cbo_materialized_view_rewrite_rule_output_limit\":3,\"cbo_enable_low_cardinality_optimize\":true,\"scan_use_query_mem_ratio\":0.3,\"connector_huge_file_size\":536870912,\"new_planner_optimize_timeout\":3000,\"enable_outer_join_reorder\":true,\"force_schedule_local\":false,\"hudi_mor_force_jni_reader\":false,\"enable_agg_spill_preaggregation\":true,\"cbo_enable_greedy_join_reorder\":true,\"range_pruner_max_predicate\":100,\"enable_rbo_table_prune\":false,\"spillable_operator_mask\":-1,\"enable_wait_dependent_event\":false,\"rpc_http_min_size\":2147482624,\"low_cardinality_optimize_v2\":true,\"enable_file_metacache\":true,\"cbo_debug_alive_backend_number\":0,\"global_runtime_filter_probe_min_size\":102400,\"scan_or_to_union_limit\":4,\"enable_cbo_table_prune\":false,\"enable_parallel_merge\":true,\"cbo_materialized_view_rewrite_candidate_limit\":12,\"skew_join_use_mcv_count\":5,\"cbo_derive_join_is_null_predicate\":true,\"nested_mv_rewrite_max_level\":3,\"enable_materialized_view_text_match_rewrite\":true,\"big_query_profile_threshold\":\"0s\",\"net_write_timeout\":60,\"cbo_prune_shuffle_column_rate\":0.1,\"spill_revocable_max_bytes\":0,\"hash_join_push_down_right_table\":true,\"connector_sink_target_max_file_size\":1073741824,\"max_ukfk_join_reorder_scale_ratio\":100,\"pipeline_sink_dop\":0,\"broadcast_row_limit\":15000000,\"enable_prune_iceberg_manifest\":true,\"exec_mem_limit\":2147483648,\"enable_sort_aggregate\":false,\"query_cache_force_populate\":false,\"computation_fragment_scheduling_policy\":\"COMPUTE_NODES_ONLY\",\"runtime_filter_on_exchange_node\":false,\"disable_join_reorder\":false,\"global_runtime_filter_rpc_timeout\":400,\"connector_scan_use_query_mem_ratio\":0.3,\"net_buffer_length\":16384,\"cbo_prune_subfield\":true,\"full_sort_max_buffered_rows\":1024000,\"query_timeout\":300,\"connector_io_tasks_slow_io_latency_ms\":50,\"cbo_max_reorder_node\":50,\"enable_distinct_column_bucketization\":false,\"enable_big_query_log\":true,\"analyze_mv\":\"sample\",\"group_execution_min_scan_rows\":5000000,\"runtime_filter_scan_wait_time\":20,\"enable_sync_materialized_view_rewrite\":true,\"prefer_compute_node\":false,\"enable_strict_type\":false,\"enable_table_prune_on_update\":false,\"group_concat_max_len\":1024,\"enable_stats_to_optimize_skew_join\":true,\"parse_tokens_limit\":3500000,\"chunk_size\":4096,\"global_runtime_filter_probe_min_selectivity\":0.5,\"query_mem_limit\":0,\"enable_filter_unused_columns_in_scan_stage\":true,\"enable_materialized_view_single_table_view_delta_rewrite\":false,\"cbo_prune_json_subfield\":true,\"enable_spill_to_remote_storage\":false,\"enable_materialized_view_transparent_union_rewrite\":true,\"enable_prune_complex_types_in_unnest\":true,\"auto_increment_increment\":1,\"sql_dialect\":\"StarRocks\",\"enable_per_bucket_optimize\":true,\"enable_group_execution\":true,\"big_query_log_scan_rows_threshold\":1000000000,\"character_set_client\":\"utf8\",\"autocommit\":true,\"enable_column_expr_predicate\":true,\"enable_partition_bucket_optimize\":false,\"max_ukfk_join_reorder_fk_rows\":100000000,\"enable_show_predicate_tree_in_profile\":false,\"enable_subfield_no_copy\":true,\"jit_level\":1,\"enable_runtime_adaptive_dop\":false,\"cbo_cte_max_limit\":10,\"connector_sink_compression_codec\":\"uncompressed\",\"storage_engine\":\"olap\",\"spill_operator_min_bytes\":10485760,\"cbo_enable_dp_join_reorder\":true,\"tx_visible_wait_timeout\":10,\"materialized_view_join_same_table_permutation_limit\":5,\"enable_materialized_view_view_delta_rewrite\":true,\"cbo_max_reorder_node_use_exhaustive\":4,\"enable_sql_digest\":false,\"spill_mem_table_num\":2,\"enable_spill\":false,\"enable_materialized_view_rewrite_greedy_mode\":false,\"pipeline_dop\":0,\"single_node_exec_plan\":false,\"full_sort_late_materialization_v2\":false,\"join_implementation_mode_v2\":\"auto\",\"enable_connector_split_io_tasks\":true,\"sql_select_limit\":9223372036854775807,\"enable_materialized_view_rewrite\":true,\"materialized_view_subuqery_text_match_max_count\":4,\"statistic_collect_parallel\":1,\"hdfs_backend_selector_hash_algorithm\":\"consistent\",\"enable_expr_prune_partition\":true,\"plan_mode\":\"local\",\"enable_topn_runtime_filter\":true,\"disable_colocate_join\":false,\"max_pushdown_conditions_per_column\":-1,\"default_table_compression\":\"lz4_frame\",\"runtime_adaptive_dop_max_output_amplification_factor\":0,\"skew_join_rand_range\":1000,\"choose_execute_instances_mode\":\"LOCALITY\",\"skew_join_data_skew_threshold\":0.2,\"innodb_read_only\":true,\"spill_mem_limit_threshold\":0.8,\"cbo_reorder_threshold_use_exhaustive\":6,\"enable_predicate_reorder\":false,\"enable_query_cache\":false,\"group_execution_group_scale\":64,\"transaction_read_only\":\"OFF\",\"max_allowed_packet\":33554432,\"enable_parallel_prepare_metadata\":false,\"time_zone\":\"Asia/Shanghai\",\"enable_multicolumn_global_runtime_filter\":false,\"character_set_server\":\"utf8\",\"cbo_use_nth_exec_plan\":0,\"io_tasks_per_scan_operator\":4,\"parallel_exchange_instance_num\":-1,\"enable_shared_scan\":false,\"join_late_materialization\":false,\"audit_execute_stmt\":false,\"cbo_derive_range_join_predicate\":false,\"allow_default_partition\":false,\"paimon_force_jni_reader\":false,\"enable_pipeline_level_shuffle\":true}", "be_number": 1, "be_core_stat": {"numOfHardwareCoresPerBe": "{\"10005\":104}", "cachedAvgNumOfHardwareCores": 104}, "exception": [], "version": "main", "commit_version": "c7c7cf7"}