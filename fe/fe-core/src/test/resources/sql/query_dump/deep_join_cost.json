{"statement": "with salesarea_permission as( with sales_path as ( select distinct sales_area_path as path from ( select s1.sales_area_path from mockt.v_dim_salesarea_permission p inner join mockt.v_dim_sale_area_chain s1 on p.sales_area_id = s1.sales_market_id where p.user_id = '202108100063214891' union all select s1.sales_area_path from mockt.v_dim_salesarea_permission p inner join mockt.v_dim_sale_area_chain s1 on p.sales_area_id = s1.first_area_id where p.user_id = '202108100063214891' union all select s2.sales_area_path from mockt.v_dim_salesarea_permission p inner join mockt.v_dim_sale_area_chain s2 on p.sales_area_id = s2.first_theater_id where p.user_id = '202108100063214891' union all select s3.sales_area_path from mockt.v_dim_salesarea_permission p inner join mockt.v_dim_sale_area_chain s3 on p.sales_area_id = s3.second_area_id where p.user_id = '202108100063214891' union all select s4.sales_area_path from mockt.v_dim_salesarea_permission p inner join mockt.v_dim_sale_area_chain s4 on p.sales_area_id = s4.second_theater_id where p.user_id = '202108100063214891' union all select s5.sales_area_path from mockt.v_dim_salesarea_permission p inner join mockt.v_dim_sale_area_chain s5 on p.sales_area_id = s5.town_id where p.user_id = '202108100063214891' union all select s6.sales_area_path from mockt.v_dim_salesarea_permission p inner join mockt.v_dim_sale_area_chain s6 on p.sales_area_id = s6.sales_unit_id where p.user_id = '202108100063214891') t ) select branch_id,status from mockt.sales_area where path in ( select path from sales_path ) and deleted_flag = 0 and status in (3, 6) and op in ('+U', '+I') group by branch_id,status ) , imei_remark as ( select imei1 , serial_code_value, i.vivo_source,row_number() over (partition by imei1,i.vivo_source order by update_time desc) as rn from vk_sale_center.serialcode_ext i where i.vivo_source in (\"INGJ01\",\"vwork_sale_ruichuang\") and i.op in('+I', '+U') and i.del_flag = 'N' and i.serial_code_key = 'firstAgentRemark' ) , imei_result as ( select * from ( select a.id, a.imei1, a.imei2, a.meid, a.item_code, a.box_no, b.customer_code, b.sales_area_id, a.customer_warehouse_code, c.warehouse_name, c.superior_id, a.customer_account_code, a.sku_code, a.register_machine_flag, a.machine_type, a.machine_status, a.business_status, a.activation_status, a.activation_time, a.is_self_activation, coalesce(a.update_time, a.create_time) as update_time ,a.retail_operator_label as operator ,a.vivo_source ,null as agent1_code from wide_vwork.v_wide_serialcode_center a left join mockt.warehouse c on a.customer_warehouse_code = c.warehouse_code and c.op in ('+I', '+U') and c.deleted_flag = 0 and c.status in (3,6) left join mockt.v_customer b on coalesce(c.superior_id,c.customer_id) = b.customer_id left join mockt.v_dim_customer_chain b1 on coalesce(c.superior_id,c.customer_id) = b1.customer_id left join mockt.v_dim_customer_permission dp1 on dp1.user_id ='202108100063214891' and dp1.customer_id = coalesce(c.superior_id,c.customer_id) left join salesarea_permission dp2 on dp2.branch_id = b.sales_area_id where a.op in ('+I', '+U') and a.vivo_source in (\"INGJ01\",\"vwork_sale_ruichuang\") and a.del_flag='N' and a.business_status <> 'RETURN_FACTORY'  AND ( dp1.customer_id is not null or (dp2.branch_id is not null  and dp2.status in ( '3' , '6' ) ))  union all select a.id, a.imei1, a.imei2, a.meid, a.item_code, a.box_no, b.customer_code, b.sales_area_id, a.customer_warehouse_code, c.warehouse_name, c.superior_id, a.customer_account_code, a.sku_code, a.register_machine_flag, a.machine_type, a.machine_status, a.business_status, a.activation_status, a.activation_time, a.is_self_activation, coalesce(a.update_time, a.create_time) as update_time ,a.retail_operator_label as operator ,a.vivo_source ,pp.agent1_code as agent1_code from wide_vwork.v_wide_serialcode_center a left join mockt.warehouse c on a.customer_warehouse_code = c.warehouse_code and c.op in ('+I', '+U') and c.deleted_flag = 0 and c.status in (3,6) left join vk_hive.dim_vwork_province_mapping2 pp on a.first_agent_code = pp.erp_code left join mockt.v_customer b on pp.agent1_code = b.customer_code left join mockt.v_dim_customer_chain b1 on pp.agent1_code = b1.customer_code left join salesarea_permission dp2 on dp2.branch_id = b.sales_area_id where a.op in ('+I', '+U') and a.vivo_source in (\"INGJ01\",\"vwork_sale_ruichuang\") and a.del_flag = 'N' and a.business_status = 'RETURN_FACTORY'  AND dp2.branch_id is not null  and dp2.status in ( '3' , '6' )  ) a where 1 = 1 ) select count(1) from imei_result a left join mockt.v_dim_product b on a.sku_code=b.sku_code left join mockt.v_dim_customer_chain vcc on vcc.customer_code = a.customer_code left join mockt.v_dim_sale_area_chain d ON a.sales_area_id = d.sales_area_id left join mockt.account f on a.customer_account_code = f.number and f.op in ('+I','+U') and f.delete_flag =0 left join [shuffle] ( select a.imei1, a.first_report_time,  a.report_time,  a.first_sales_store_code, a.first_sales_store_name ,  a.report_user_code ,  a.vivo_source, row_number() over(partition by a.imei1, a.vivo_source order by a.report_time desc ) as rn from vk_sale_center.serialcode_sales_report_detail a where a.vivo_source in (\"INGJ01\",\"vwork_sale_ruichuang\") and a.op in ('+I', '+U') and a.del_flag = 'N' and a.sales_report_type in('SALE', 'EXCHANGE') ) h on a.imei1 = h.imei1 and h.rn = 1 and a.vivo_source = h.vivo_source left join vk_sale_center.serialcode_ext ol on a.imei1 = ol.imei1 and ol.vivo_source in (\"INGJ01\",\"vwork_sale_ruichuang\") and ol.op in('+I','+U') and ol.del_flag='N' and ol.serial_code_key ='Tag' and a.vivo_source = ol.vivo_source left join mockt.data_dictionary_type olt on olt.val_china = ol.serial_code_value and olt.p_id = 202203181000000590 and olt.op in('+I','+U') and olt.delete_flag = 0 left join imei_remark i on a.imei1 = i.imei1 and i.rn = 1 and a.vivo_source = i.vivo_source left join vk_sale_center.serialcode_remark o on a.imei1 = o.imei1 and o.vivo_source in (\"INGJ01\",\"vwork_sale_ruichuang\") and o.op in('+I','+U') and o.del_flag='N' and a.vivo_source = o.vivo_source left join mockt.v_dim_employee p on h.report_user_code = p.employee_code and p.status in (0,1) left join mockt.user u1 on h.report_user_code = u1.employee_code and u1.delete_flag = 0 and u1.op in('+I','+U') left join mockt.store s2 on a.superior_id = s2.branch_id and s2.delete_flag=0 and s2.op in ('+I','+U') left join mockt.language_support signal_system on b.signal_system_id = signal_system.data_id and signal_system.delete_flag = 0 and signal_system.op in ('+I', '+U') and signal_system.language = 'en' left join mockt.language_support category on b.category_id = category.data_id and category.delete_flag = 0 and category.op in ('+I', '+U') and category.language = 'en' left join vk_sale_center.serialcode_ext dpr on a.imei1 = dpr.imei1 and a.vivo_source = dpr.vivo_source and dpr.del_flag = 'N' and dpr.serial_code_key = 'demoPhoneRecord' left join mockt.retailer vdr on vcc.retailer_code = vdr.number and vdr.delete_flag = 0 and vdr.op in('+I','+U') left join mockt.retailer_channel rc on vdr.branch_id = rc.master_branch_id and rc.delete_flag = 0 and rc.op in('+I','+U') LEFT JOIN mockt.language_support retailer_type ON rc.type = retailer_type.data_id AND retailer_type.delete_flag = 0 AND retailer_type.op IN ('+I', '+U') AND retailer_type.LANGUAGE = 'en' LEFT JOIN mockt.language_support key_acount ON vdr.key_account_name = key_acount.data_id AND key_acount.delete_flag = 0 AND key_acount.op IN ('+I', '+U') AND key_acount.LANGUAGE = 'en' where 1=1 and b.brand_id IN ( '*****************' , '202112300116786598' , '*****************' , '202103183007929182' , '*****************' , '202009090000052432' , '202009090000052435' , '202009090000052453' , '*****************' )  and b.category_id in ( '*****************' )\n", "table_meta": {"mockt.sales_area_link": "CREATE TABLE `sales_area_link` (\n  `id` largeint(40) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `master_id` largeint(40) NULL,\n  `master_branch_id` largeint(40) NULL,\n  `sales_area_id` largeint(40) NOT NULL,\n  `sales_area_name` varchar(65533) NULL,\n  `type` tinyint(4) NULL,\n  `status` tinyint(4) NULL,\n  `delete_flag` tinyint(4) NOT NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 1 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.sales_area": "CREATE TABLE `sales_area` (\n  `id` largeint(40) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `branch_id` largeint(40) NULL,\n  `p_id` largeint(40) NULL,\n  `sales_area_code` varchar(65533) NULL,\n  `sales_area_name` varchar(65533) NOT NULL,\n  `type_id` largeint(40) NOT NULL,\n  `org_id` largeint(40) NULL,\n  `point` varchar(65533) NULL,\n  `entity_flag` tinyint(4) NULL,\n  `operation_entity_id` largeint(40) NULL,\n  `entity_type` tinyint(4) NULL,\n  `remark` varchar(65533) NULL,\n  `version` int(11) NULL DEFAULT \"1\",\n  `create_time` datetime NULL,\n  `update_time` datetime NULL,\n  `create_by` largeint(40) NULL,\n  `update_by` largeint(40) NULL,\n  `status` tinyint(4) NULL,\n  `deleted_flag` tinyint(4) NULL DEFAULT \"0\",\n  `lang_type` tinyint(4) NULL,\n  `path` varchar(65533) NULL,\n  `item1` varchar(65533) NULL,\n  `item2` varchar(65533) NULL,\n  `item3` varchar(65533) NULL,\n  `item4` varchar(65533) NULL,\n  `item5` varchar(65533) NULL,\n  `item6` largeint(40) NULL,\n  `item7` largeint(40) NULL,\n  `item8` largeint(40) NULL,\n  `item9` largeint(40) NULL,\n  `item10` largeint(40) NULL,\n  `item11` double NULL,\n  `item12` double NULL,\n  `item13` double NULL,\n  `item14` double NULL,\n  `item15` double NULL,\n  `item16` datetime NULL,\n  `item17` datetime NULL,\n  `item18` datetime NULL,\n  `item19` datetime NULL,\n  `item20` datetime NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 5 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.dim_sales_area_chain": "CREATE TABLE `dim_sales_area_chain` (\n  `id` bigint(20) NOT NULL,\n  `sales_area_id` bigint(20) NULL,\n  `sales_area_code` varchar(200) NULL,\n  `sales_area_name` varchar(200) NULL,\n  `sales_area_status` varchar(200) NULL,\n  `sales_area_path` varchar(200) NULL,\n  `sales_area_type` varchar(200) NULL,\n  `sales_area_type_id` int(11) NULL,\n  `p_id` bigint(20) NOT NULL,\n  `version` int(11) NOT NULL,\n  `deleted_flag` int(11) NOT NULL,\n  `op` varchar(26) NOT NULL,\n  `sales_market_id` bigint(20) NULL,\n  `sales_market_code` varchar(200) NULL,\n  `sales_market_name` varchar(200) NULL,\n  `sales_market_type` varchar(200) NULL,\n  `sales_market_type_id` int(11) NULL,\n  `sales_market_status` varchar(200) NULL,\n  `sales_market_path` varchar(200) NULL,\n  `sales_market_p_id` bigint(20) NULL,\n  `sales_biggest_area_id` bigint(20) NULL,\n  `sales_biggest_area_name` varchar(200) NULL,\n  `sales_biggest_area_type` varchar(200) NULL,\n  `first_area_id` bigint(20) NULL,\n  `first_area_code` varchar(200) NULL,\n  `first_area_name` varchar(200) NULL,\n  `first_area_type` varchar(200) NULL,\n  `first_theater_id` bigint(20) NULL,\n  `first_theater_code` varchar(200) NULL,\n  `first_theater_name` varchar(200) NULL,\n  `first_theater_type` varchar(200) NULL,\n  `second_area_id` bigint(20) NULL,\n  `second_area_code` varchar(200) NULL,\n  `second_area_name` varchar(200) NULL,\n  `second_area_type` varchar(200) NULL,\n  `second_theater_id` bigint(20) NULL,\n  `second_theater_code` varchar(200) NULL,\n  `second_theater_name` varchar(200) NULL,\n  `second_theater_type` varchar(200) NULL,\n  `town_id` bigint(20) NULL,\n  `town_code` varchar(200) NULL,\n  `town_name` varchar(200) NULL,\n  `town_type` varchar(200) NULL,\n  `sales_unit_id` bigint(20) NULL,\n  `sales_unit_code` varchar(200) NULL,\n  `sales_unit_name` varchar(200) NULL,\n  `sales_unit_type` varchar(200) NULL,\n  `update_time` bigint(20) NULL,\n  `wh_update_time` bigint(20) NULL,\n  `level_code` bigint(20) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 1 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "vk_sale_center.serialcode_ext": "CREATE TABLE `serialcode_ext` (\n  `id` bigint(20) NOT NULL,\n  `vivo_source` varchar(128) NOT NULL,\n  `imei1` varchar(128) NOT NULL,\n  `create_date` date NULL,\n  `op` varchar(10) NOT NULL,\n  `uuid` bigint(20) NULL,\n  `imei2` varchar(128) NULL,\n  `meid` varchar(128) NULL,\n  `item_code` varchar(128) NULL,\n  `box_no` varchar(128) NULL,\n  `first_agent_code` varchar(64) NULL,\n  `serial_code_key` varchar(64) NULL,\n  `serial_code_value` varchar(300) NULL,\n  `operator_name` varchar(128) NULL,\n  `operator_code` varchar(64) NULL,\n  `operator_time` datetime NULL,\n  `create_time` datetime NULL,\n  `create_user_id` varchar(32) NULL,\n  `update_time` datetime NULL,\n  `update_user_id` varchar(32) NULL,\n  `del_flag` varchar(32) NULL,\n  `sales_hierarchy1` varchar(2048) NULL,\n  `sales_hierarchy2` varchar(2048) NULL,\n  `sales_hierarchy3` varchar(2048) NULL,\n  `sales_hierarchy4` varchar(2048) NULL,\n  `buyer_id` bigint(20) NULL,\n  `buyer_shop_id` bigint(20) NULL,\n  `buyer_account_id` bigint(20) NULL,\n  `buyer_wh_id` bigint(20) NULL,\n  `buyer_shop_type` varchar(255) NULL,\n  `buyer_carrier_type` varchar(255) NULL,\n  `seller_id` bigint(20) NULL,\n  `seller_shop_id` bigint(20) NULL,\n  `seller_account_id` bigint(20) NULL,\n  `seller_wh_id` bigint(20) NULL,\n  `seller_shop_type` varchar(128) NULL,\n  `seller_carrier_type` varchar(128) NULL,\n  `product_brand_category` varchar(1024) NULL,\n  `request_no` varchar(255) NULL,\n  `request_system` varchar(128) NULL,\n  `request_date` datetime NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`, `vivo_source`, `imei1`)\n\nDISTRIBUTED BY HASH(`imei1`, `vivo_source`) BUCKETS 3 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"true\",\n\"compression\" = \"LZ4\"\n);", "wide_vwork.wide_serialcode_center": "CREATE TABLE `wide_serialcode_center` (\n  `id` bigint(20) NOT NULL,\n  `imei1` varchar(127) NOT NULL,\n  `vivo_source` varchar(128) NOT NULL,\n  `op` varchar(8) NOT NULL,\n  `uuid` bigint(20) NULL,\n  `imei2` varchar(127) NULL,\n  `meid` varchar(127) NULL,\n  `item_code` varchar(127) NULL,\n  `box_no` varchar(127) NULL,\n  `first_agent_code` varchar(128) NULL,\n  `customer_code` varchar(128) NULL,\n  `customer_account_code` varchar(128) NULL,\n  `customer_warehouse_code` varchar(128) NULL,\n  `sku_code` varchar(128) NULL,\n  `machine_status` bigint(20) NULL,\n  `machine_type` bigint(20) NULL,\n  `stock_status` varchar(128) NULL,\n  `business_status` varchar(128) NULL,\n  `activation_status` bigint(20) NULL,\n  `activation_time` datetime NULL,\n  `relation_order_no` varchar(128) NULL,\n  `create_time` datetime NULL,\n  `create_user_id` varchar(128) NULL,\n  `update_time` datetime NULL,\n  `update_user_id` varchar(128) NULL,\n  `del_flag` varchar(128) NULL,\n  `sales_hierarchy1` varchar(2048) NULL,\n  `sales_hierarchy2` varchar(2048) NULL,\n  `sales_hierarchy3` varchar(2048) NULL,\n  `sales_hierarchy4` varchar(2048) NULL,\n  `buyer_id` bigint(20) NULL,\n  `buyer_shop_id` bigint(20) NULL,\n  `buyer_account_id` bigint(20) NULL,\n  `buyer_wh_id` bigint(20) NULL,\n  `buyer_shop_type` varchar(128) NULL,\n  `buyer_carrier_type` varchar(128) NULL,\n  `seller_id` bigint(20) NULL,\n  `seller_shop_id` bigint(20) NULL,\n  `seller_account_id` bigint(20) NULL,\n  `seller_wh_id` bigint(20) NULL,\n  `seller_shop_type` varchar(128) NULL,\n  `seller_carrier_type` varchar(128) NULL,\n  `product_brand_category` varchar(1024) NULL,\n  `register_machine_flag` bigint(20) NULL,\n  `remark` varchar(512) NULL,\n  `activation_status_overall` bigint(20) NULL,\n  `activation_time_overall` datetime NULL,\n  `retail_operator` int(11) NULL,\n  `retail_operator_label` varchar(16) NULL,\n  `is_self_activation` int(11) NULL,\n  INDEX indx_serialcode_source (`vivo_source`) USING BITMAP COMMENT 'vivo_source索引',\n  INDEX indx_serialcode_op (`op`) USING BITMAP COMMENT 'op 索引',\n  INDEX indx_serialcode_del (`del_flag`) USING BITMAP COMMENT 'del_flag 索引',\n  INDEX indx_serialcode_status (`business_status`) USING BITMAP COMMENT 'business_status 索引'\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 40 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"true\",\n\"compression\" = \"LZ4\"\n);", "wide_vwork.wide_return_serialcode_center": "CREATE TABLE `wide_return_serialcode_center` (\n  `imei1` varchar(127) NOT NULL,\n  `vivo_source` varchar(128) NOT NULL,\n  `id` bigint(20) NOT NULL,\n  `op` varchar(8) NOT NULL,\n  `uuid` bigint(20) NULL,\n  `imei2` varchar(127) NULL,\n  `meid` varchar(127) NULL,\n  `item_code` varchar(127) NULL,\n  `box_no` varchar(127) NULL,\n  `first_agent_code` varchar(128) NULL,\n  `customer_code` varchar(128) NULL,\n  `customer_account_code` varchar(128) NULL,\n  `customer_warehouse_code` varchar(128) NULL,\n  `sku_code` varchar(128) NULL,\n  `machine_status` bigint(20) NULL,\n  `machine_type` bigint(20) NULL,\n  `stock_status` varchar(128) NULL,\n  `business_status` varchar(128) NULL,\n  `activation_status` bigint(20) NULL,\n  `activation_time` datetime NULL,\n  `relation_order_no` varchar(128) NULL,\n  `create_time` datetime NULL,\n  `create_user_id` varchar(128) NULL,\n  `update_time` datetime NULL,\n  `update_user_id` varchar(128) NULL,\n  `del_flag` varchar(128) NULL,\n  `sales_hierarchy1` varchar(2048) NULL,\n  `sales_hierarchy2` varchar(2048) NULL,\n  `sales_hierarchy3` varchar(2048) NULL,\n  `sales_hierarchy4` varchar(2048) NULL,\n  `buyer_id` bigint(20) NULL,\n  `buyer_shop_id` bigint(20) NULL,\n  `buyer_account_id` bigint(20) NULL,\n  `buyer_wh_id` bigint(20) NULL,\n  `buyer_shop_type` varchar(128) NULL,\n  `buyer_carrier_type` varchar(128) NULL,\n  `seller_id` bigint(20) NULL,\n  `seller_shop_id` bigint(20) NULL,\n  `seller_account_id` bigint(20) NULL,\n  `seller_wh_id` bigint(20) NULL,\n  `seller_shop_type` varchar(128) NULL,\n  `seller_carrier_type` varchar(128) NULL,\n  `product_brand_category` varchar(1024) NULL,\n  `register_machine_flag` bigint(20) NULL,\n  `remark` varchar(512) NULL,\n  `activation_status_overall` bigint(20) NULL,\n  `activation_time_overall` datetime NULL,\n  `retail_operator` int(11) NULL,\n  `retail_operator_label` varchar(16) NULL,\n  `is_self_activation` int(11) NULL,\n  `one_agency_number` varchar(120) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`imei1`, `vivo_source`)\n\nDISTRIBUTED BY HASH(`imei1`) BUCKETS 10 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"colocate_with\" = \"center_imei\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.warehouse": "CREATE TABLE `warehouse` (\n  `id` largeint(40) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `branch_id` largeint(40) NOT NULL,\n  `warehouse_code` varchar(65533) NULL,\n  `warehouse_name` varchar(65533) NOT NULL,\n  `abbreviation` varchar(65533) NOT NULL,\n  `customer_id` largeint(40) NOT NULL,\n  `customer_name` varchar(65533) NULL,\n  `customer_type` tinyint(4) NULL,\n  `operation_entity_id` largeint(40) NULL,\n  `warehouse_type` largeint(40) NOT NULL,\n  `superior_id` largeint(40) NULL,\n  `whether_in_use` tinyint(4) NULL,\n  `province` varchar(65533) NULL,\n  `city` varchar(65533) NULL,\n  `district` varchar(65533) NULL,\n  `county` varchar(65533) NULL,\n  `address` varchar(65533) NULL,\n  `whether_default` tinyint(4) NOT NULL,\n  `whether_location_enabled` tinyint(4) NOT NULL,\n  `remark` varchar(65533) NULL,\n  `create_time` datetime NULL,\n  `update_time` datetime NULL,\n  `create_by` largeint(40) NULL,\n  `update_by` largeint(40) NULL,\n  `version` int(11) NULL DEFAULT \"1\",\n  `status` tinyint(4) NULL,\n  `disable_reason` varchar(65533) NULL,\n  `deleted_flag` tinyint(4) NULL DEFAULT \"0\",\n  `account_id` largeint(40) NULL,\n  `subordinate_customer_id` largeint(40) NULL,\n  `idc_cwh_code` varchar(65533) NULL,\n  `idc_cWhCode` varchar(65533) NULL,\n  `dms_id` varchar(65533) NULL,\n  `wms_butt_or_not` tinyint(4) NULL,\n  `address_line_1` varchar(65533) NULL,\n  `address_line_2` varchar(65533) NULL,\n  `address_line_3` varchar(65533) NULL,\n  `postal_code` varchar(65533) NULL,\n  `destination_warehouse_code` varchar(65533) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 2 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.retailer": "CREATE TABLE `retailer` (\n  `id` bigint(20) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `branch_id` bigint(20) NULL,\n  `number` varchar(100) NULL,\n  `name` varchar(250) NULL,\n  `abbreviation` varchar(100) NULL,\n  `business_number` varchar(100) NULL,\n  `operation_entity_id` bigint(20) NULL,\n  `superior_type` tinyint(4) NULL,\n  `superior_id` bigint(20) NULL,\n  `superior_name` varchar(100) NULL,\n  `parent_company` varchar(200) NULL,\n  `headquarters_name` varchar(100) NULL,\n  `headquarters_province` bigint(20) NULL,\n  `headquarters_city` bigint(20) NULL,\n  `headquarters_district` bigint(20) NULL,\n  `headquarters_county` bigint(20) NULL,\n  `headquarters_address` varchar(200) NULL,\n  `company_prov_name` varchar(100) NULL,\n  `settlement_currency` bigint(20) NULL,\n  `payment` tinyint(4) NULL,\n  `sales_type` tinyint(4) NULL,\n  `direct_flag` tinyint(4) NULL,\n  `across_flag` tinyint(4) NULL,\n  `pickup_point` varchar(50) NULL,\n  `entity_flag` tinyint(4) NULL,\n  `enable_flag` tinyint(4) NULL,\n  `remark` varchar(500) NULL,\n  `share_imei_flag` tinyint(4) NULL,\n  `disable_reason` tinyint(4) NULL,\n  `disable_remark` varchar(250) NULL,\n  `disable_file_path` varchar(250) NULL,\n  `delete_flag` tinyint(4) NULL,\n  `create_time` datetime NULL,\n  `update_time` datetime NULL,\n  `create_by` bigint(20) NULL,\n  `update_by` bigint(20) NULL,\n  `version` int(11) NULL,\n  `status` tinyint(4) NULL,\n  `share_account_flag` tinyint(4) NULL,\n  `default_store_branch_id` bigint(20) NULL,\n  `default_account_branch_id` bigint(20) NULL,\n  `sign_china_mobile_flag` tinyint(4) NULL,\n  `contract_number` varchar(100) NULL,\n  `path` varchar(90) NULL,\n  `dms_id` varchar(50) NULL,\n  `dealer_id` varchar(50) NULL,\n  `dealer_relation_id` bigint(20) NULL,\n  `dealer_code` varchar(50) NULL,\n  `spliced_address` varchar(200) NULL,\n  `address_line_3` varchar(200) NULL,\n  `postal_code` varchar(200) NULL,\n  `auto_inbound` tinyint(4) NULL,\n  `key_account_name` bigint(20) NULL,\n  `item1` varchar(100) NULL,\n  `item2` varchar(100) NULL,\n  `item3` varchar(100) NULL,\n  `item4` varchar(100) NULL,\n  `item5` varchar(100) NULL,\n  `item6` bigint(20) NULL,\n  `item7` bigint(20) NULL,\n  `item8` bigint(20) NULL,\n  `item9` bigint(20) NULL,\n  `item10` bigint(20) NULL,\n  `item11` double NULL,\n  `item12` double NULL,\n  `item13` double NULL,\n  `item14` double NULL,\n  `item15` double NULL,\n  `item16` datetime NULL,\n  `item17` datetime NULL,\n  `item18` datetime NULL,\n  `item19` datetime NULL,\n  `item20` datetime NULL,\n  `retailer_category` tinyint(4) NULL,\n  `agency_account_id` bigint(20) NULL,\n  `agency_account_name` varchar(250) NULL,\n  `signed_account_id` bigint(20) NULL,\n  `cooperate_date` datetime NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 16 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.agency": "CREATE TABLE `agency` (\n  `id` bigint(20) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `branch_id` bigint(20) NULL,\n  `number` varchar(100) NULL,\n  `name` varchar(250) NULL,\n  `abbreviation` varchar(100) NULL,\n  `business_number` varchar(100) NULL,\n  `operation_entity_id` bigint(20) NULL,\n  `level` tinyint(4) NULL,\n  `country` bigint(20) NULL,\n  `group_name` varchar(250) NULL,\n  `superior_id` bigint(20) NULL,\n  `superior_name` varchar(250) NULL,\n  `settlement_currency` bigint(20) NULL,\n  `payment` tinyint(4) NULL,\n  `sales_type` tinyint(4) NULL,\n  `social_ratio` varchar(50) NULL,\n  `province` bigint(20) NULL,\n  `city` bigint(20) NULL,\n  `district` bigint(20) NULL,\n  `county` bigint(20) NULL,\n  `address` varchar(200) NULL,\n  `pickup_point` double NULL,\n  `entity_flag` tinyint(4) NULL,\n  `phone_number` varchar(100) NULL,\n  `mail` varchar(200) NULL,\n  `fax` varchar(100) NULL,\n  `url` varchar(200) NULL,\n  `remark` varchar(500) NULL,\n  `format` bigint(20) NULL,\n  `channel` tinyint(4) NULL,\n  `disable_reason` tinyint(4) NULL,\n  `disable_remark` varchar(250) NULL,\n  `disable_file_path` varchar(250) NULL,\n  `status` tinyint(4) NULL,\n  `share_imei_flag` tinyint(4) NULL,\n  `share_account_flag` tinyint(4) NULL,\n  `version` int(11) NULL,\n  `delete_flag` tinyint(4) NULL,\n  `create_by` bigint(20) NULL,\n  `create_time` datetime NULL,\n  `update_by` bigint(20) NULL,\n  `update_time` datetime NULL,\n  `idc_area` varchar(100) NULL,\n  `path` varchar(90) NULL,\n  `dms_id` varchar(50) NULL,\n  `dealer_id` varchar(50) NULL,\n  `dealer_relation_id` varchar(50) NULL,\n  `spliced_address` varchar(200) NULL,\n  `postal_code` varchar(200) NULL,\n  `dealer_code` varchar(50) NULL,\n  `address_line_3` varchar(200) NULL,\n  `channel_type` tinyint(4) NULL,\n  `item1` varchar(100) NULL,\n  `item2` varchar(100) NULL,\n  `item3` varchar(100) NULL,\n  `item4` varchar(100) NULL,\n  `item5` varchar(100) NULL,\n  `item6` bigint(20) NULL,\n  `item7` bigint(20) NULL,\n  `item8` bigint(20) NULL,\n  `item9` bigint(20) NULL,\n  `item10` bigint(20) NULL,\n  `item11` double NULL,\n  `item12` double NULL,\n  `item13` double NULL,\n  `item14` double NULL,\n  `item15` double NULL,\n  `item16` datetime NULL,\n  `item17` datetime NULL,\n  `item18` datetime NULL,\n  `item19` datetime NULL,\n  `item20` datetime NULL,\n  `signed_account_id` bigint(20) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 16 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.store": "CREATE TABLE `store` (\n  `id` largeint(40) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `branch_id` largeint(40) NOT NULL,\n  `store_code` varchar(65533) NULL,\n  `store_name` varchar(65533) NOT NULL,\n  `bussiness_number` varchar(65533) NULL,\n  `customer_id` largeint(40) NULL,\n  `customer_type` tinyint(4) NULL,\n  `abbreviation` varchar(65533) NOT NULL,\n  `superior_id` largeint(40) NOT NULL,\n  `store_type` largeint(40) NOT NULL,\n  `circle_name` varchar(65533) NULL,\n  `circle_type` largeint(40) NULL,\n  `mall_name` varchar(65533) NULL,\n  `store_market_type` largeint(40) NOT NULL,\n  `store_city_level` varchar(65533) NULL,\n  `store_create_time` datetime NULL,\n  `store_open_time` datetime NULL,\n  `sale_status` largeint(40) NULL,\n  `closed_time` datetime NULL,\n  `closed_reason` varchar(65533) NULL,\n  `province` varchar(65533) NULL,\n  `city` varchar(65533) NULL,\n  `district` varchar(65533) NULL,\n  `county` largeint(40) NULL,\n  `store_address` varchar(65533) NULL,\n  `remark` varchar(65533) NULL,\n  `longitude` varchar(65533) NULL,\n  `latitude` varchar(65533) NULL,\n  `status` tinyint(4) NULL,\n  `create_time` datetime NULL,\n  `update_time` datetime NULL,\n  `create_by` largeint(40) NULL,\n  `update_by` largeint(40) NULL,\n  `delete_flag` tinyint(4) NOT NULL DEFAULT \"0\",\n  `version` int(11) NULL,\n  `disable_reason` tinyint(4) NULL,\n  `stop_type` tinyint(4) NULL,\n  `disable_remark` varchar(65533) NULL,\n  `disable_file_path` varchar(65533) NULL,\n  `disable_time` datetime NULL,\n  `entity_flag` tinyint(4) NULL,\n  `sales_area_id` largeint(40) NULL,\n  `dealer_relation_id` largeint(40) NULL,\n  `path` varchar(65533) NULL,\n  `dealer_id` varchar(65533) NULL,\n  `dms_id` varchar(65533) NULL,\n  `dms_code` varchar(65533) NULL,\n  `dealer_code` varchar(65533) NULL,\n  `store_personnel_attribute` largeint(40) NOT NULL DEFAULT \"202005060000077182\",\n  `store_classification` largeint(40) NULL,\n  `zone` largeint(40) NULL,\n  `ho_club_category` largeint(40) NULL,\n  `address_line_3` varchar(65533) NULL,\n  `promoter_available` tinyint(4) NULL,\n  `spliced_address` varchar(65533) NULL,\n  `email_id` varchar(65533) NULL,\n  `approval_time` datetime NULL,\n  `location_type` tinyint(4) NULL,\n  `location_address` varchar(65533) NULL,\n  `item1` varchar(65533) NULL,\n  `item2` varchar(65533) NULL,\n  `item3` varchar(65533) NULL,\n  `item4` varchar(65533) NULL,\n  `item5` varchar(65533) NULL,\n  `item6` largeint(40) NULL,\n  `item7` largeint(40) NULL,\n  `item8` largeint(40) NULL,\n  `item9` largeint(40) NULL,\n  `item10` largeint(40) NULL,\n  `item11` double NULL,\n  `item12` double NULL,\n  `item13` double NULL,\n  `item14` double NULL,\n  `item15` double NULL,\n  `item16` datetime NULL,\n  `item17` datetime NULL,\n  `item18` datetime NULL,\n  `item19` datetime NULL,\n  `item20` datetime NULL,\n  `position_update_time` datetime NULL,\n  `store_category` tinyint(4) NOT NULL DEFAULT \"3\",\n  `gst_number` varchar(65533) NULL,\n  `store_area` double NULL,\n  `shop_open_time` datetime NULL,\n  `close_shop_time` datetime NULL,\n  `business_level` largeint(40) NULL,\n  `business_trend` largeint(40) NULL,\n  `pin_code` varchar(65533) NULL,\n  `terminal_control` tinyint(4) NULL DEFAULT \"1\",\n  `store_level` largeint(40) NULL,\n  `position_update_user_id` largeint(40) NULL,\n  `position_update_user_name` varchar(65533) NULL,\n  `position_complete_flag` tinyint(4) NULL DEFAULT \"0\",\n  `store_level_agent` largeint(40) NULL,\n  `position_audit_user_id` largeint(40) NULL,\n  `position_audit_user_name` varchar(65533) NULL,\n  `store_level_factory` bigint(20) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 2 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.dim_customer_chain": "CREATE TABLE `dim_customer_chain` (\n  `customer_id` bigint(20) NOT NULL,\n  `id` bigint(20) NOT NULL,\n  `customer_code` varchar(256) NULL,\n  `customer_name` varchar(256) NULL,\n  `customer_abbreviation` varchar(256) NULL,\n  `customer_status` tinyint(4) NULL,\n  `customer_path` varchar(256) NULL,\n  `customer_level` tinyint(4) NULL,\n  `customer_level_name` varchar(256) NULL,\n  `factory_id` bigint(20) NULL,\n  `factory_code` varchar(256) NULL,\n  `factory_name` varchar(256) NULL,\n  `factory_abbreviation` varchar(256) NULL,\n  `factory_status` tinyint(4) NULL,\n  `agent1_id` bigint(20) NULL,\n  `agent1_code` varchar(256) NULL,\n  `agent1_name` varchar(256) NULL,\n  `agent1_abbreviation` varchar(256) NULL,\n  `agent1_status` tinyint(4) NULL,\n  `agent2_id` bigint(20) NULL,\n  `agent2_code` varchar(256) NULL,\n  `agent2_name` varchar(256) NULL,\n  `agent2_abbreviation` varchar(256) NULL,\n  `agent2_status` tinyint(4) NULL,\n  `agent3_id` bigint(20) NULL,\n  `agent3_code` varchar(256) NULL,\n  `agent3_name` varchar(256) NULL,\n  `agent3_abbreviation` varchar(256) NULL,\n  `agent3_status` tinyint(4) NULL,\n  `agent4_id` bigint(20) NULL,\n  `agent4_code` varchar(256) NULL,\n  `agent4_name` varchar(256) NULL,\n  `agent4_abbreviation` varchar(256) NULL,\n  `agent4_status` tinyint(4) NULL,\n  `retailer_id` bigint(20) NULL,\n  `retailer_code` varchar(256) NULL,\n  `retailer_name` varchar(256) NULL,\n  `retailer_abbreviation` varchar(256) NULL,\n  `retailer_status` tinyint(4) NULL,\n  `store_id` bigint(20) NULL,\n  `store_code` varchar(256) NULL,\n  `store_name` varchar(256) NULL,\n  `store_abbreviation` varchar(256) NULL,\n  `store_status` tinyint(4) NULL,\n  `province` bigint(20) NULL,\n  `city` bigint(20) NULL,\n  `district` bigint(20) NULL,\n  `county` bigint(20) NULL,\n  `superior_id` bigint(20) NULL,\n  `op` varchar(20) NULL,\n  `delete_flag` tinyint(4) NULL,\n  `version` tinyint(4) NULL,\n  `update_time` bigint(20) NULL,\n  `wh_update_time` bigint(20) NULL,\n  `level_code` bigint(20) NULL,\n  `customer_type` bigint(20) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`customer_id`)\n\nDISTRIBUTED BY HASH(`customer_id`) BUCKETS 5 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.data_dictionary_type": "CREATE TABLE `data_dictionary_type` (\n  `id` largeint(40) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `number` varchar(65533) NOT NULL,\n  `type_code` varchar(65533) NULL,\n  `val_china` varchar(65533) NULL,\n  `val_english` varchar(65533) NULL,\n  `status` tinyint(4) NULL DEFAULT \"0\",\n  `create_time` datetime NULL,\n  `update_time` datetime NULL,\n  `create_by` largeint(40) NULL,\n  `update_by` largeint(40) NULL,\n  `entire_flag` tinyint(4) NULL,\n  `system_code` varchar(65533) NULL,\n  `subjoin_attribute` varchar(65533) NULL,\n  `delete_flag` tinyint(4) NULL DEFAULT \"0\",\n  `check_tissue_list_str` varchar(65533) NULL,\n  `customer_names` varchar(65533) NULL,\n  `p_id` largeint(40) NULL,\n  `superstratum_code_id` largeint(40) NULL,\n  `superstratum_code` varchar(65533) NULL,\n  `remark` varchar(65533) NULL,\n  `subjoin_attribut` varchar(65533) NULL,\n  `default_value` varchar(65533) NULL,\n  `module_id` largeint(40) NULL,\n  `data_dictionary_id` largeint(40) NULL,\n  `sort` int(11) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 1 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.data_permission": "CREATE TABLE `data_permission` (\n  `id` largeint(40) NOT NULL,\n  `op` varchar(65533) NULL,\n  `user_id` largeint(40) NULL,\n  `data_id` largeint(40) NULL,\n  `data_type` tinyint(4) NULL,\n  `wh_update_time` datetime NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 1 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "vk_hive.dim_vwork_province_mapping2": "CREATE TABLE `dim_vwork_province_mapping2` (\n  `agent1_code` varchar(65533) NOT NULL,\n  `agent1_name` varchar(65533) NULL,\n  `agent1_path` varchar(65533) NULL,\n  `vivo_source` varchar(65533) NULL,\n  `erp_code` varchar(65533) NULL,\n  `sales_area_id` bigint(20) NULL,\n  `sales_area_code` varchar(65533) NULL,\n  `sales_area_name` varchar(65533) NULL,\n  `sales_area_path` varchar(65533) NULL,\n  `vivo_source_id` bigint(20) NULL\n) ENGINE=OLAP \nDUPLICATE KEY(`agent1_code`)\n\nDISTRIBUTED BY HASH(`agent1_code`) BUCKETS 1 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.product": "CREATE TABLE `product` (\n  `id` largeint(40) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `tree_id` largeint(40) NOT NULL,\n  `category` largeint(40) NOT NULL,\n  `brand` largeint(40) NOT NULL,\n  `subordinate_agency` largeint(40) NULL,\n  `source` tinyint(4) NOT NULL,\n  `sku_code` varchar(65533) NOT NULL,\n  `sku_name` varchar(65533) NOT NULL,\n  `product_type` tinyint(4) NULL,\n  `product_series` largeint(40) NOT NULL,\n  `model_series` largeint(40) NULL,\n  `model_ver_id` largeint(40) NULL,\n  `marketing_id` largeint(40) NULL,\n  `product_abbr` varchar(65533) NULL,\n  `model_name` varchar(65533) NULL,\n  `material_serial` largeint(40) NULL,\n  `org_id` largeint(40) NOT NULL DEFAULT \"0\",\n  `sort` varchar(65533) NULL,\n  `status` tinyint(4) NULL,\n  `create_time` datetime NOT NULL,\n  `update_time` datetime NULL,\n  `create_by` largeint(40) NULL,\n  `update_by` largeint(40) NULL,\n  `deleted_flag` tinyint(4) NULL DEFAULT \"0\",\n  `lang_type` tinyint(4) NULL,\n  `color_id` varchar(65533) NULL,\n  `cgst` double NULL,\n  `sgst` double NULL,\n  `igst` double NULL,\n  `hsn` largeint(40) NULL,\n  `pms_product_series` largeint(40) NULL,\n  `pms_product_model_series` largeint(40) NULL,\n  `sort_order` bigint(20) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 1 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.product_tree": "CREATE TABLE `product_tree` (\n  `id` largeint(40) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `tree_name` varchar(65533) NOT NULL,\n  `tree_type` tinyint(4) NOT NULL,\n  `p_id` largeint(40) NOT NULL,\n  `subordinate_category` tinyint(4) NULL,\n  `status` tinyint(4) NULL,\n  `subordinate_agency` largeint(40) NULL,\n  `create_time` datetime NOT NULL,\n  `update_time` datetime NULL,\n  `create_by` largeint(40) NULL,\n  `update_by` largeint(40) NULL,\n  `deleted_flag` tinyint(4) NULL DEFAULT \"0\",\n  `level` tinyint(4) NULL,\n  `pms_id` largeint(40) NULL,\n  `pms_pid` largeint(40) NULL,\n  `sort_order` tinyint(4) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 1 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.product_exp": "CREATE TABLE `product_exp` (\n  `id` largeint(40) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `master_id` largeint(40) NOT NULL,\n  `marketing_name` varchar(65533) NULL,\n  `color` varchar(65533) NULL,\n  `memory` varchar(65533) NULL,\n  `signal_system` largeint(40) NULL,\n  `govern_cust_flag` tinyint(4) NULL DEFAULT \"0\",\n  `proj_custom` varchar(65533) NULL,\n  `go_market_time` datetime NULL,\n  `weight` varchar(65533) NULL,\n  `volume` varchar(65533) NULL,\n  `model_ver_series_id` largeint(40) NULL,\n  `product_model_id` largeint(40) NULL,\n  `model_ver_series` varchar(65533) NULL,\n  `product_model_name` varchar(65533) NULL,\n  `status` tinyint(4) NULL,\n  `sort_min_qty` varchar(65533) NULL,\n  `manfacture` varchar(65533) NULL,\n  `en_code` varchar(65533) NULL,\n  `unit` varchar(65533) NULL,\n  `min_package` varchar(65533) NULL,\n  `imei_control_flag` tinyint(4) NULL,\n  `stock_control_flag` tinyint(4) NULL,\n  `create_time` datetime NOT NULL,\n  `update_time` datetime NULL,\n  `create_by` largeint(40) NULL,\n  `update_by` largeint(40) NULL,\n  `deleted_flag` tinyint(4) NULL DEFAULT \"0\",\n  `lang_type` tinyint(4) NULL,\n  `branch_id` largeint(40) NULL,\n  `pms_model_ver_series_id` largeint(40) NULL,\n  `pms_product_model_id` largeint(40) NULL,\n  `material_code` varchar(65533) NULL,\n  `material_name` varchar(65533) NULL,\n  `specs_desc` varchar(65533) NULL,\n  `item20` datetime NULL,\n  `item6` largeint(40) NULL,\n  `item1` varchar(65533) NULL,\n  `item2` varchar(65533) NULL,\n  `item3` varchar(65533) NULL,\n  `item4` varchar(65533) NULL,\n  `item5` varchar(65533) NULL,\n  `item7` largeint(40) NULL,\n  `item8` largeint(40) NULL,\n  `item9` largeint(40) NULL,\n  `item10` largeint(40) NULL,\n  `item11` double NULL,\n  `item12` double NULL,\n  `item13` double NULL,\n  `item14` double NULL,\n  `item15` double NULL,\n  `item16` datetime NULL,\n  `item17` datetime NULL,\n  `item18` datetime NULL,\n  `item19` datetime NULL,\n  `operator_custom` tinyint(4) NULL,\n  `operator` varchar(65533) NULL,\n  `exemplar_tag` tinyint(4) NULL DEFAULT \"1\",\n  `material_quality` varchar(65533) NULL,\n  `length` double NULL,\n  `width` double NULL,\n  `higth` double NULL,\n  `is_put` tinyint(4) NULL DEFAULT \"0\",\n  `is_assets_management` tinyint(4) NULL DEFAULT \"0\",\n  `material_status` tinyint(4) NULL DEFAULT \"3\",\n  `original_material_code` varchar(65533) NULL,\n  `demo_model_flag` int(11) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 1 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.account": "CREATE TABLE `account` (\n  `id` bigint(20) NOT NULL,\n  `op` varchar(5) NULL,\n  `branch_id` bigint(20) NULL,\n  `number` varchar(100) NULL,\n  `name` varchar(65533) NULL,\n  `customer_id` bigint(20) NULL,\n  `customer_number` varchar(100) NULL,\n  `customer_name` varchar(65533) NULL,\n  `default_flag` tinyint(4) NULL,\n  `price_level` tinyint(4) NULL,\n  `province` bigint(20) NULL,\n  `city` bigint(20) NULL,\n  `district` bigint(20) NULL,\n  `county` bigint(20) NULL,\n  `billing_address` varchar(65533) NULL,\n  `billing_type` tinyint(4) NULL,\n  `tax_unit` varchar(65533) NULL,\n  `tax_id` varchar(65533) NULL,\n  `bank_account_id` varchar(65533) NULL,\n  `bank` varchar(65533) NULL,\n  `tel` varchar(65533) NULL,\n  `category` tinyint(4) NULL,\n  `license_file_id` varchar(65533) NULL,\n  `license_number` varchar(65533) NULL,\n  `credit_code` varchar(65533) NULL,\n  `validity_period` varchar(65533) NULL,\n  `validity_period_end` varchar(65533) NULL,\n  `est_date` varchar(65533) NULL,\n  `reg_capital` varchar(65533) NULL,\n  `reg_address` varchar(65533) NULL,\n  `legal_person_name` varchar(65533) NULL,\n  `legal_id_card` varchar(65533) NULL,\n  `stop_reason` varchar(65533) NULL,\n  `version` int(11) NULL,\n  `status` tinyint(4) NULL,\n  `create_time` datetime NULL,\n  `update_time` datetime NULL,\n  `create_by` bigint(20) NULL,\n  `update_by` bigint(20) NULL,\n  `delete_flag` tinyint(4) NULL,\n  `customer_type` tinyint(4) NULL,\n  `lock_flag` tinyint(4) NULL,\n  `aggregate_status` tinyint(4) NULL,\n  `idc_ccuscode` varchar(65533) NULL,\n  `province_app` varchar(65533) NULL,\n  `city_app` varchar(65533) NULL,\n  `district_app` varchar(65533) NULL,\n  `gst` varchar(65533) NULL,\n  `pan` varchar(65533) NULL,\n  `gstin` varchar(65533) NULL,\n  `cin` varchar(65533) NULL,\n  `bank_ifsc` varchar(65533) NULL,\n  `address_india` varchar(65533) NULL,\n  `postal_code` int(11) NULL,\n  `aggregate_id` varchar(65533) NULL,\n  `spliced_address` varchar(65533) NULL,\n  `state` bigint(20) NULL,\n  `loans_status` tinyint(4) NULL,\n  `loans_id` bigint(20) NULL,\n  `destination_account_code` varchar(65533) NULL,\n  `erp_org_code` varchar(65533) NULL,\n  `finance_number` varchar(65533) NULL,\n  `main_flag` tinyint(4) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 1 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "vk_sale_center.serialcode_sales_report_detail": "CREATE TABLE `serialcode_sales_report_detail` (\n  `id` bigint(20) NOT NULL,\n  `vivo_source` varchar(128) NOT NULL,\n  `create_date` date NULL,\n  `op` varchar(10) NOT NULL,\n  `uuid` bigint(20) NULL,\n  `imei1` varchar(128) NULL,\n  `imei2` varchar(128) NULL,\n  `meid` varchar(128) NULL,\n  `item_code` varchar(128) NULL,\n  `box_no` varchar(128) NULL,\n  `first_agent_code` varchar(128) NULL,\n  `sales_report_type` varchar(128) NULL,\n  `relation_order_no` varchar(128) NULL,\n  `report_time` datetime NULL,\n  `relation_order_type` varchar(128) NULL,\n  `report_user_code` varchar(128) NULL,\n  `first_sales_store_code` varchar(128) NULL,\n  `first_sales_store_name` varchar(127) NULL,\n  `first_report_time` datetime NULL,\n  `create_time` datetime NULL,\n  `create_user_id` varchar(128) NULL,\n  `update_time` datetime NULL,\n  `update_user_id` varchar(128) NULL,\n  `del_flag` varchar(128) NULL,\n  `sales_hierarchy1` varchar(2048) NULL,\n  `sales_hierarchy2` varchar(2048) NULL,\n  `sales_hierarchy3` varchar(2048) NULL,\n  `sales_hierarchy4` varchar(2048) NULL,\n  `buyer_id` bigint(20) NULL,\n  `buyer_shop_id` bigint(20) NULL,\n  `buyer_account_id` bigint(20) NULL,\n  `buyer_wh_id` bigint(20) NULL,\n  `buyer_shop_type` varchar(128) NULL,\n  `buyer_carrier_type` varchar(128) NULL,\n  `seller_id` bigint(20) NULL,\n  `seller_shop_id` bigint(20) NULL,\n  `seller_account_id` bigint(20) NULL,\n  `seller_wh_id` bigint(20) NULL,\n  `seller_shop_type` varchar(128) NULL,\n  `seller_carrier_type` varchar(128) NULL,\n  `product_brand_category` varchar(2048) NULL,\n  `request_no` varchar(255) NULL,\n  `request_system` varchar(255) NULL,\n  `request_date` datetime NULL,\n  INDEX vivo_source_index (`vivo_source`) USING BITMAP COMMENT 'vivo_source_index',\n  INDEX op_index (`op`) USING BITMAP COMMENT 'op_index',\n  INDEX del_flag_index (`del_flag`) USING BITMAP COMMENT 'del_flag_index',\n  INDEX sales_report_type_index (`sales_report_type`) USING BITMAP COMMENT 'sales_report_type_index'\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 40 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"true\",\n\"compression\" = \"LZ4\"\n);", "vk_sale_center.serialcode_remark": "CREATE TABLE `serialcode_remark` (\n  `id` bigint(20) NOT NULL,\n  `vivo_source` varchar(128) NOT NULL,\n  `create_date` date NOT NULL,\n  `op` varchar(10) NOT NULL,\n  `uuid` bigint(20) NULL,\n  `imei1` varchar(128) NULL,\n  `imei2` varchar(128) NULL,\n  `meid` varchar(128) NULL,\n  `item_code` varchar(128) NULL,\n  `box_no` varchar(128) NULL,\n  `first_agent_code` varchar(128) NULL,\n  `imei_remark` varchar(1024) NULL,\n  `remark_operator_name` varchar(128) NULL,\n  `remark_operator_code` varchar(128) NULL,\n  `remark_update_time` datetime NULL,\n  `create_time` datetime NULL,\n  `create_user_id` varchar(128) NULL,\n  `update_time` datetime NULL,\n  `update_user_id` varchar(128) NULL,\n  `del_flag` varchar(128) NULL,\n  `sales_hierarchy1` varchar(2048) NULL,\n  `sales_hierarchy2` varchar(2048) NULL,\n  `sales_hierarchy3` varchar(2048) NULL,\n  `sales_hierarchy4` varchar(2048) NULL,\n  `buyer_id` bigint(20) NULL,\n  `buyer_shop_id` bigint(20) NULL,\n  `buyer_account_id` bigint(20) NULL,\n  `buyer_wh_id` bigint(20) NULL,\n  `buyer_shop_type` varchar(128) NULL,\n  `buyer_carrier_type` varchar(128) NULL,\n  `seller_id` bigint(20) NULL,\n  `seller_shop_id` bigint(20) NULL,\n  `seller_account_id` bigint(20) NULL,\n  `seller_wh_id` bigint(20) NULL,\n  `seller_shop_type` varchar(128) NULL,\n  `seller_carrier_type` varchar(128) NULL,\n  `product_brand_category` varchar(1024) NULL,\n  `request_no` varchar(255) NULL,\n  `request_system` varchar(128) NULL,\n  `request_date` datetime NULL,\n  `remark` varchar(1024) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`, `vivo_source`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 3 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.employee": "CREATE TABLE `employee` (\n  `id` largeint(40) NOT NULL,\n  `op` varchar(65533) NULL,\n  `employee_code` varchar(65533) NULL,\n  `branch_employee_code` varchar(65533) NULL,\n  `name` varchar(65533) NULL,\n  `alias_name` varchar(65533) NULL,\n  `status` tinyint(4) NOT NULL DEFAULT \"0\",\n  `active_flag` tinyint(4) NULL DEFAULT \"0\",\n  `probation` int(11) NULL,\n  `col2` varchar(65533) NULL,\n  `sex` tinyint(4) NULL,\n  `nationality` varchar(65533) NULL,\n  `birth_place` varchar(65533) NULL,\n  `birthday` date NULL,\n  `age` int(11) NULL,\n  `highest_education` varchar(65533) NULL,\n  `graduation_school` varchar(65533) NULL,\n  `graduation_date` date NULL,\n  `graduation_major` varchar(65533) NULL,\n  `marital_status` varchar(65533) NULL,\n  `col1` varchar(65533) NULL,\n  `political_status` varchar(65533) NULL,\n  `household_type` varchar(65533) NULL,\n  `household_registration` varchar(65533) NULL,\n  `clothing_size` varchar(65533) NULL,\n  `height` varchar(65533) NULL,\n  `fertility_status` varchar(65533) NULL,\n  `prenatal_test` tinyint(4) NULL,\n  `employee_license_photo` varchar(65533) NULL,\n  `create_time` datetime NULL,\n  `update_time` datetime NULL,\n  `create_by` largeint(40) NULL,\n  `update_by` largeint(40) NULL,\n  `delete_flag` tinyint(4) NULL DEFAULT \"0\",\n  `entry_date` date NULL,\n  `regular_date` date NULL,\n  `work_age` int(11) NULL,\n  `position_age` int(11) NULL,\n  `leave_date` date NULL,\n  `maternity_leave_status` largeint(40) NULL,\n  `last_name` varchar(65533) NULL,\n  `first_name` varchar(65533) NULL,\n  `vrs_employee_id` varchar(65533) NULL,\n  `vrs_employee_code` varchar(65533) NULL,\n  `internal_external` tinyint(4) NULL DEFAULT \"0\",\n  `employee_type` varchar(65533) NULL,\n  `phone_number` varchar(65533) NULL,\n  `email_address` varchar(65533) NULL,\n  `data_source` tinyint(4) NULL DEFAULT \"0\",\n  `agency_branch_id` largeint(40) NULL,\n  `retailer_branch_id` largeint(40) NULL,\n  `dms_id` varchar(65533) NULL,\n  `title` tinyint(4) NULL,\n  `template_name` varchar(65533) NULL,\n  `remark` varchar(65533) NULL,\n  `org_change_flag` tinyint(4) NULL,\n  `area` varchar(65533) NULL,\n  `district_group` varchar(65533) NULL,\n  `type` varchar(65533) NULL,\n  `first_work_time` datetime NULL,\n  `tax_card_account` varchar(65533) NULL,\n  `old_social_account` varchar(65533) NULL,\n  `old_medical_insurance_account` varchar(65533) NULL,\n  `residential_address` varchar(65533) NULL,\n  `permanent_residence_address` varchar(65533) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 1 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.user": "CREATE TABLE `user` (\n  `id` bigint(20) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `employee_id` bigint(20) NULL,\n  `employee_code` varchar(100) NULL,\n  `name` varchar(100) NULL,\n  `status` tinyint(4) NULL,\n  `email_address` varchar(128) NULL,\n  `user_type` tinyint(4) NULL,\n  `phone_number` varchar(100) NULL,\n  `bu_mobile` varchar(50) NULL,\n  `avatar` varchar(256) NULL,\n  `user_account` varchar(32) NULL,\n  `org_id` bigint(20) NULL,\n  `remark` varchar(256) NULL,\n  `create_time` datetime NULL,\n  `update_time` datetime NULL,\n  `create_by` bigint(20) NULL,\n  `update_by` bigint(20) NULL,\n  `delete_flag` tinyint(4) NULL,\n  `imei` varchar(32) NULL,\n  `manual_super_org_id` bigint(20) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 16 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.employee_position": "CREATE TABLE `employee_position` (\n  `id` bigint(20) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `employee_id` bigint(20) NULL,\n  `entry_date` date NULL,\n  `effective_date` date NULL,\n  `main_flag` tinyint(4) NULL,\n  `status` tinyint(4) NULL,\n  `operate_type` tinyint(4) NULL,\n  `operate_reason` varchar(512) NULL,\n  `employee_type` tinyint(4) NULL,\n  `probation` tinyint(4) NULL,\n  `work_place` varchar(100) NULL,\n  `teacher_employee_id` bigint(20) NULL,\n  `department_id` bigint(20) NULL,\n  `department_code` varchar(100) NULL,\n  `department_business_number` varchar(100) NULL,\n  `position_id` bigint(20) NULL,\n  `position_code` varchar(100) NULL,\n  `position_level_id` varchar(100) NULL,\n  `star_level` bigint(20) NULL,\n  `operation_entity_id` bigint(20) NULL,\n  `report_type` tinyint(4) NULL,\n  `report_to_employee_id` bigint(20) NULL,\n  `department_head_flag` tinyint(4) NULL,\n  `sales_area_head_flag` tinyint(4) NULL,\n  `create_time` datetime NULL,\n  `update_time` datetime NULL,\n  `create_by` bigint(20) NULL,\n  `update_by` bigint(20) NULL,\n  `delete_flag` tinyint(4) NULL,\n  `sales_area_branch_id` bigint(20) NULL,\n  `store_staff_flag` tinyint(4) NULL,\n  `store_sales_area_branch_id` bigint(20) NULL,\n  `retailer_staff_flag` tinyint(4) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 16 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.position": "CREATE TABLE `position` (\n  `id` bigint(20) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `business_number` varchar(100) NULL,\n  `level` bigint(20) NULL,\n  `status` tinyint(4) NULL,\n  `effective_date` datetime NULL,\n  `expiration_date` datetime NULL,\n  `department_id` bigint(20) NULL,\n  `operation_entity_id` bigint(20) NULL,\n  `duties` varchar(100) NULL,\n  `position_code` varchar(100) NULL,\n  `name` varchar(100) NULL,\n  `position_type` bigint(20) NULL,\n  `position_attr` bigint(20) NULL,\n  `branch_id` bigint(20) NULL,\n  `create_time` datetime NULL,\n  `update_time` datetime NULL,\n  `create_by` bigint(20) NULL,\n  `update_by` bigint(20) NULL,\n  `delete_flag` tinyint(4) NULL,\n  `number` varchar(100) NULL,\n  `vrs_company_id` varchar(100) NULL,\n  `vrs_company_code` varchar(50) NULL,\n  `idc_id` varchar(50) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 16 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.language_support": "CREATE TABLE `language_support` (\n  `id` bigint(20) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `data_id` bigint(20) NULL,\n  `language` varchar(65533) NULL,\n  `value` varchar(65533) NULL,\n  `create_time` datetime NULL,\n  `update_time` datetime NULL,\n  `create_by` bigint(20) NULL,\n  `update_by` bigint(20) NULL,\n  `delete_flag` tinyint(4) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 1 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);", "mockt.retailer_channel": "CREATE TABLE `retailer_channel` (\n  `id` bigint(20) NOT NULL,\n  `op` varchar(65533) NOT NULL,\n  `master_id` bigint(20) NULL,\n  `master_branch_id` bigint(20) NULL,\n  `channel` bigint(20) NULL,\n  `customer_level_hq` bigint(20) NULL,\n  `customer_level_local` bigint(20) NULL,\n  `customer_level_jiangsu` tinyint(4) NULL,\n  `type` bigint(20) NULL,\n  `status` tinyint(4) NULL,\n  `delete_flag` tinyint(4) NULL\n) ENGINE=OLAP \nPRIMARY KEY(`id`)\n\nDISTRIBUTED BY HASH(`id`) BUCKETS 16 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"enable_persistent_index\" = \"false\",\n\"compression\" = \"LZ4\"\n);"}, "table_row_count": {"mockt.dim_sales_area_chain": {"dim_sales_area_chain": 172063}, "mockt.retailer_channel": {"retailer_channel": 582754}, "mockt.sales_area_link": {"sales_area_link": 1886197}, "vk_hive.dim_vwork_province_mapping2": {"dim_vwork_province_mapping2": 24}, "mockt.user": {"user": 139349}, "mockt.retailer": {"retailer": 594696}, "mockt.employee_position": {"employee_position": 146016}, "mockt.store": {"store": 936027}, "mockt.data_dictionary_type": {"data_dictionary_type": 3326}, "mockt.position": {"position": 11185}, "wide_vwork.wide_serialcode_center": {"wide_serialcode_center": ********}, "mockt.data_permission": {"data_permission": 2556668}, "mockt.employee": {"employee": 216469}, "mockt.agency": {"agency": 12103}, "mockt.account": {"account": 121020}, "mockt.language_support": {"language_support": 190225}, "mockt.product_exp": {"product_exp": 23142}, "mockt.sales_area": {"sales_area": 371025}, "mockt.product": {"product": 23648}, "mockt.product_tree": {"product_tree": 412}, "wide_vwork.wide_return_serialcode_center": {"wide_return_serialcode_center": 1007879}, "vk_sale_center.serialcode_sales_report_detail": {"serialcode_sales_report_detail": 526958}, "mockt.warehouse": {"warehouse": 992749}, "mockt.dim_customer_chain": {"dim_customer_chain": 229990}}, "view_meta": {"mockt.v_dim_salesarea_permission": "SELECT `mockt`.`b`.`master_id` AS `user_id`, `mockt`.`b`.`sales_area_id`, `mockt`.`c`.`sales_area_name`, `mockt`.`c`.`path`\nFROM `mockt`.`sales_area_link` AS `b` LEFT OUTER JOIN `mockt`.`sales_area` AS `c` ON (((`mockt`.`b`.`sales_area_id` = `mockt`.`c`.`branch_id`) AND (`mockt`.`c`.`deleted_flag` = 0)) AND (`mockt`.`c`.`status` IN (3, 6))) AND (`mockt`.`c`.`op` IN ('+I', '+U'))\nWHERE ((`mockt`.`b`.`type` = 0) AND (`mockt`.`b`.`delete_flag` = 0)) AND (`mockt`.`b`.`op` IN ('+I', '+U'))", "mockt.v_dim_sale_area_chain": "SELECT `mockt`.`dsac`.`sales_area_id`, `mockt`.`dsac`.`sales_area_name`, `mockt`.`dsac`.`sales_area_type`, `mockt`.`dsac`.`sales_area_type_id` AS `type_id`, `mockt`.`dsac`.`sales_area_path`, `mockt`.`dsac`.`p_id`, `mockt`.`dsac`.`sales_market_id`, `mockt`.`dsac`.`sales_market_name`, `mockt`.`dsac`.`sales_market_type`, `mockt`.`dsac`.`first_area_id`, `mockt`.`dsac`.`first_area_name`, `mockt`.`dsac`.`first_area_type`, `mockt`.`dsac`.`first_theater_id`, `mockt`.`dsac`.`first_theater_name`, `mockt`.`dsac`.`first_theater_type`, `mockt`.`dsac`.`second_area_id`, `mockt`.`dsac`.`second_area_name`, `mockt`.`dsac`.`second_area_type`, `mockt`.`dsac`.`second_theater_id`, `mockt`.`dsac`.`second_theater_name`, `mockt`.`dsac`.`second_theater_type`, `mockt`.`dsac`.`town_id`, `mockt`.`dsac`.`town_name`, `mockt`.`dsac`.`town_type`, `mockt`.`dsac`.`sales_unit_id`, `mockt`.`dsac`.`sales_unit_name`, `mockt`.`dsac`.`sales_unit_type`, `mockt`.`dsac`.`sales_area_status`\nFROM `mockt`.`dim_sales_area_chain` AS `dsac`\nWHERE (`mockt`.`dsac`.`op` IN ('+I', '+U')) AND (`mockt`.`dsac`.`deleted_flag` = 0)", "wide_vwork.v_wide_serialcode_center": "SELECT `wide_vwork`.`t`.`id`, `wide_vwork`.`t`.`vivo_source`, `wide_vwork`.`t`.`op`, `wide_vwork`.`t`.`imei1`, `wide_vwork`.`t`.`imei2`, `wide_vwork`.`t`.`meid`, `wide_vwork`.`t`.`box_no`, `wide_vwork`.`t`.`first_agent_code`, `wide_vwork`.`t`.`customer_code`, `wide_vwork`.`t`.`customer_account_code`, `wide_vwork`.`t`.`customer_warehouse_code`, `wide_vwork`.`t`.`sku_code`, `wide_vwork`.`t`.`machine_status`, `wide_vwork`.`t`.`machine_type`, `wide_vwork`.`t`.`stock_status`, `wide_vwork`.`t`.`business_status`, `wide_vwork`.`t`.`activation_status`, `wide_vwork`.`t`.`activation_time`, `wide_vwork`.`t`.`is_self_activation`, `wide_vwork`.`t`.`relation_order_no`, `wide_vwork`.`t`.`create_time`, `wide_vwork`.`t`.`create_user_id`, `wide_vwork`.`t`.`update_time`, `wide_vwork`.`t`.`update_user_id`, `wide_vwork`.`t`.`register_machine_flag`, `wide_vwork`.`t`.`remark`, `wide_vwork`.`t`.`item_code`, `wide_vwork`.`t`.`del_flag`, `wide_vwork`.`t`.`retail_operator`, `wide_vwork`.`t`.`retail_operator_label`\nFROM `wide_vwork`.`wide_serialcode_center` AS `t`\nWHERE (`wide_vwork`.`t`.`op` IN ('+I', '+U')) AND (`wide_vwork`.`t`.`del_flag` = 'N') UNION ALL SELECT `wide_vwork`.`a`.`id`, `wide_vwork`.`a`.`vivo_source`, `wide_vwork`.`a`.`op`, `wide_vwork`.`a`.`imei1`, `wide_vwork`.`a`.`imei2`, `wide_vwork`.`a`.`meid`, `wide_vwork`.`a`.`box_no`, `wide_vwork`.`a`.`first_agent_code`, `wide_vwork`.`a`.`customer_code`, `wide_vwork`.`a`.`customer_account_code`, `wide_vwork`.`a`.`customer_warehouse_code`, `wide_vwork`.`a`.`sku_code`, `wide_vwork`.`a`.`machine_status`, `wide_vwork`.`a`.`machine_type`, `wide_vwork`.`a`.`stock_status`, `wide_vwork`.`a`.`business_status`, `wide_vwork`.`a`.`activation_status`, `wide_vwork`.`a`.`activation_time`, `wide_vwork`.`a`.`is_self_activation`, `wide_vwork`.`a`.`relation_order_no`, `wide_vwork`.`a`.`create_time`, `wide_vwork`.`a`.`create_user_id`, `wide_vwork`.`a`.`update_time`, NULL AS `update_by`, `wide_vwork`.`a`.`register_machine_flag`, `wide_vwork`.`a`.`remark`, `wide_vwork`.`a`.`item_code`, `wide_vwork`.`a`.`del_flag`, `wide_vwork`.`a`.`retail_operator`, `wide_vwork`.`a`.`retail_operator_label`\nFROM `wide_vwork`.`wide_return_serialcode_center` AS `a` LEFT OUTER JOIN [SHUFFLE] `wide_vwork`.`wide_serialcode_center` AS `s` ON (((`wide_vwork`.`a`.`imei1` = `wide_vwork`.`s`.`imei1`) AND (`wide_vwork`.`s`.`vivo_source` = `wide_vwork`.`a`.`vivo_source`)) AND (`wide_vwork`.`s`.`op` IN ('+I', '+U'))) AND (`wide_vwork`.`s`.`del_flag` = 'N')\nWHERE ((`wide_vwork`.`a`.`del_flag` = 'N') AND (`wide_vwork`.`a`.`op` IN ('+I', '+U'))) AND (`wide_vwork`.`s`.`imei1` IS NULL)", "mockt.v_customer": "SELECT `a`.`customer_id`, `a`.`customer_code`, `a`.`customer_name`, `a`.`customer_path`, `a`.`customer_level`, `mockt`.`b`.`sales_area_id`, `mockt`.`c`.`path` AS `sales_area_path`, `a`.`is_closed`\nFROM (SELECT `mockt`.`retailer`.`branch_id` AS `customer_id`, `mockt`.`retailer`.`number` AS `customer_code`, `mockt`.`retailer`.`name` AS `customer_name`, `mockt`.`retailer`.`path` AS `customer_path`, 5 AS `customer_level`, 0 AS `is_closed`\nFROM `mockt`.`retailer`\nWHERE ((`mockt`.`retailer`.`delete_flag` = 0) AND (`mockt`.`retailer`.`status` IN (3, 6))) AND (`mockt`.`retailer`.`op` IN ('+I', '+U')) UNION ALL SELECT `mockt`.`agency`.`branch_id` AS `customer_id`, `mockt`.`agency`.`number` AS `customer_code`, `mockt`.`agency`.`name` AS `customer_name`, `mockt`.`agency`.`path` AS `customer_path`, `mockt`.`agency`.`level` AS `customer_level`, 0 AS `is_closed`\nFROM `mockt`.`agency`\nWHERE ((`mockt`.`agency`.`delete_flag` = 0) AND (`mockt`.`agency`.`status` IN (3, 6))) AND (`mockt`.`agency`.`op` IN ('+I', '+U')) UNION ALL SELECT `mockt`.`store`.`branch_id` AS `customer_id`, `mockt`.`store`.`store_code` AS `customer_code`, `mockt`.`store`.`store_name` AS `customer_name`, `mockt`.`store`.`path` AS `customer_path`, 6 AS `customer_level`, if(`mockt`.`store`.`sale_status` = 20191202000002890, 1, 0) AS `is_closed`\nFROM `mockt`.`store`\nWHERE ((`mockt`.`store`.`delete_flag` = 0) AND (`mockt`.`store`.`status` IN (3, 6))) AND (`mockt`.`store`.`op` IN ('+I', '+U'))) `a` LEFT OUTER JOIN `mockt`.`sales_area_link` AS `b` ON ((((`a`.`customer_id` = `mockt`.`b`.`master_branch_id`) AND (`mockt`.`b`.`delete_flag` = 0)) AND (`mockt`.`b`.`status` IN (3, 6))) AND (`mockt`.`b`.`type` IN (1, 2, 3))) AND (`mockt`.`b`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`sales_area` AS `c` ON (((`mockt`.`b`.`sales_area_id` = `mockt`.`c`.`branch_id`) AND (`mockt`.`c`.`deleted_flag` = 0)) AND (`mockt`.`c`.`status` IN (3, 6))) AND (`mockt`.`c`.`op` IN ('+I', '+U'))", "mockt.v_dim_customer_chain": "SELECT `a`.`customer_id`, `a`.`customer_code`, `a`.`customer_name`, `a`.`customer_abbreviation`, `a`.`customer_status`, `a`.`customer_path`, `a`.`customer_level`, `a`.`superior_id`, `a`.`agent1_id`, `a`.`agent1_code`, `a`.`agent1_name`, `a`.`agent1_abbreviation`, `a`.`agent1_status`, `a`.`agent2_id`, `a`.`agent2_code`, `a`.`agent2_name`, `a`.`agent2_abbreviation`, `a`.`agent2_status`, `a`.`agent3_id`, `a`.`agent3_code`, `a`.`agent3_name`, `a`.`agent3_abbreviation`, `a`.`agent3_status`, `a`.`agent4_id`, `a`.`agent4_code`, `a`.`agent4_name`, `a`.`agent4_abbreviation`, `a`.`agent4_status`, `a`.`retailer_id`, `a`.`retailer_code`, `a`.`retailer_name`, `a`.`retailer_abbreviation`, `a`.`retailer_status`, `a`.`store_id`, `a`.`store_code`, `a`.`store_name`, `a`.`store_abbreviation`, `a`.`store_status`, `mockt`.`b`.`sales_area_id`, `mockt`.`b`.`sales_area_name`, `a`.`province`, `mockt`.`dp`.`val_china` AS `province_name`, `a`.`city`, `mockt`.`dc`.`val_china` AS `city_name`, `a`.`district`, `mockt`.`dd`.`val_china` AS `district_name`, `a`.`county`\nFROM (SELECT `mockt`.`a`.`customer_id`, `mockt`.`a`.`customer_code`, `mockt`.`a`.`customer_name`, `mockt`.`a`.`customer_abbreviation`, `mockt`.`a`.`customer_status`, `mockt`.`a`.`customer_path`, `mockt`.`a`.`customer_level`, `mockt`.`a`.`superior_id`, `mockt`.`a`.`agent1_id`, `mockt`.`a`.`agent1_code`, `mockt`.`a`.`agent1_name`, `mockt`.`a`.`agent1_abbreviation`, `mockt`.`a`.`agent1_status`, if(`mockt`.`a`.`agent2_id` = 0, NULL, `mockt`.`a`.`agent2_id`) AS `agent2_id`, `mockt`.`a`.`agent2_code`, `mockt`.`a`.`agent2_name`, `mockt`.`a`.`agent2_abbreviation`, `mockt`.`a`.`agent2_status`, if(`mockt`.`a`.`agent3_id` = 0, NULL, `mockt`.`a`.`agent3_id`) AS `agent3_id`, `mockt`.`a`.`agent3_code`, `mockt`.`a`.`agent3_name`, `mockt`.`a`.`agent3_abbreviation`, `mockt`.`a`.`agent3_status`, if(`mockt`.`a`.`agent4_id` = 0, NULL, `mockt`.`a`.`agent4_id`) AS `agent4_id`, `mockt`.`a`.`agent4_code`, `mockt`.`a`.`agent4_name`, `mockt`.`a`.`agent4_abbreviation`, `mockt`.`a`.`agent4_status`, if(`mockt`.`a`.`retailer_id` = 0, NULL, `mockt`.`a`.`retailer_id`) AS `retailer_id`, `mockt`.`a`.`retailer_code`, `mockt`.`a`.`retailer_name`, `mockt`.`a`.`retailer_abbreviation`, `mockt`.`a`.`retailer_status`, `mockt`.`a`.`store_id`, `mockt`.`a`.`store_code`, `mockt`.`a`.`store_name`, `mockt`.`a`.`store_abbreviation`, `mockt`.`a`.`store_status`, `mockt`.`a`.`province`, `mockt`.`a`.`city`, `mockt`.`a`.`district`, `mockt`.`a`.`county`\nFROM `mockt`.`dim_customer_chain` AS `a`\nWHERE ((`mockt`.`a`.`op` IN ('+I', '+U')) AND (`mockt`.`a`.`customer_status` IN (3, 6))) AND (`mockt`.`a`.`delete_flag` = 0)) `a` LEFT OUTER JOIN `mockt`.`sales_area_link` AS `b` ON (((`a`.`customer_id` = `mockt`.`b`.`master_branch_id`) AND (`mockt`.`b`.`delete_flag` = 0)) AND (`mockt`.`b`.`status` IN (3, 6))) AND (`mockt`.`b`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`data_dictionary_type` AS `dp` ON (((CAST(`a`.`province` AS VARCHAR(50))) = (CAST(`mockt`.`dp`.`id` AS VARCHAR(50)))) AND (`mockt`.`dp`.`delete_flag` = 0)) AND (`mockt`.`dp`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`data_dictionary_type` AS `dc` ON (((CAST(`a`.`city` AS VARCHAR(50))) = (CAST(`mockt`.`dc`.`id` AS VARCHAR(50)))) AND (`mockt`.`dc`.`delete_flag` = 0)) AND (`mockt`.`dc`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`data_dictionary_type` AS `dd` ON (((CAST(`a`.`district` AS VARCHAR(50))) = (CAST(`mockt`.`dd`.`id` AS VARCHAR(50)))) AND (`mockt`.`dd`.`delete_flag` = 0)) AND (`mockt`.`dd`.`op` IN ('+I', '+U'))", "mockt.v_dim_customer_permission": "SELECT `mockt`.`a`.`user_id`, `mockt`.`a`.`data_id` AS `customer_id`\nFROM `mockt`.`data_permission` AS `a`\nWHERE (`mockt`.`a`.`op` IN ('+I', '+U')) AND (`mockt`.`a`.`data_type` = 1) UNION SELECT `mockt`.`a`.`user_id`, if(`mockt`.`a`.`data_type` = 2, `mockt`.`a`.`data_id`, `mockt`.`c`.`branch_id`) AS `customer_id`\nFROM `mockt`.`data_permission` AS `a` LEFT OUTER JOIN `mockt`.`store` AS `c` ON ((((`mockt`.`a`.`data_type` = 1) AND (`mockt`.`a`.`data_id` = `mockt`.`c`.`superior_id`)) AND (`mockt`.`c`.`delete_flag` = 0)) AND (`mockt`.`c`.`status` IN (3, 6))) AND (`mockt`.`c`.`op` IN ('+I', '+U'))\nWHERE (`mockt`.`a`.`op` IN ('+I', '+U')) AND (`mockt`.`a`.`data_type` IN (1, 2))", "mockt.v_dim_product": "SELECT `mockt`.`t`.`id` AS `sku_id`, `mockt`.`t`.`sku_code`, `mockt`.`t`.`sku_name`, `mockt`.`t`.`sort_order` AS `sku_sort`, `mockt`.`t`.`model_name`, `mockt`.`t`.`model_series` AS `model_series_id`, `mockt`.`t1`.`tree_name` AS `model_series`, `mockt`.`t1`.`sort_order` AS `model_series_sort`, `mockt`.`t2`.`id` AS `product_series_id`, `mockt`.`t2`.`tree_name` AS `product_series`, `mockt`.`t2`.`sort_order` AS `product_series_sort`, `mockt`.`t`.`category` AS `category_id`, `mockt`.`t3`.`val_english` AS `category`, `mockt`.`t3`.`sort` AS `category_sort`, `mockt`.`t`.`brand` AS `brand_id`, `mockt`.`t4`.`val_english` AS `brand`, `mockt`.`t4`.`sort` AS `brand_sort`, `mockt`.`t`.`status`, `mockt`.`t1`.`subordinate_category`, `mockt`.`t`.`marketing_id`, `mockt`.`t5`.`marketing_name`, `mockt`.`t8`.`sort_order` AS `marketing_sort`, `mockt`.`t5`.`color`, `mockt`.`t5`.`memory`, `mockt`.`t5`.`signal_system` AS `signal_system_id`, `mockt`.`t6`.`val_english` AS `signal_system`, `mockt`.`t5`.`product_model_id`, `mockt`.`t5`.`product_model_name`, `mockt`.`t5`.`model_ver_series_id`, `mockt`.`t5`.`model_ver_series`, `mockt`.`t7`.`sort_order` AS `model_ver_series_sort`, `mockt`.`t5`.`govern_cust_flag`, `mockt`.`t5`.`imei_control_flag`, if(`mockt`.`t5`.`demo_model_flag` IS NULL, 0, `mockt`.`t5`.`demo_model_flag`) AS `demo_model_flag`, `mockt`.`t`.`product_type`\nFROM `mockt`.`product` AS `t` LEFT OUTER JOIN `mockt`.`product_tree` AS `t1` ON ((`mockt`.`t`.`model_series` = `mockt`.`t1`.`id`) AND (`mockt`.`t1`.`deleted_flag` = 0)) AND (`mockt`.`t1`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`product_tree` AS `t2` ON ((`mockt`.`t`.`product_series` = `mockt`.`t2`.`id`) AND (`mockt`.`t2`.`deleted_flag` = 0)) AND (`mockt`.`t2`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`data_dictionary_type` AS `t3` ON ((`mockt`.`t`.`category` = `mockt`.`t3`.`id`) AND (`mockt`.`t3`.`delete_flag` = 0)) AND (`mockt`.`t3`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`data_dictionary_type` AS `t4` ON ((`mockt`.`t`.`brand` = `mockt`.`t4`.`id`) AND (`mockt`.`t4`.`delete_flag` = 0)) AND (`mockt`.`t4`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`product_exp` AS `t5` ON ((`mockt`.`t`.`id` = `mockt`.`t5`.`master_id`) AND (`mockt`.`t5`.`deleted_flag` = 0)) AND (`mockt`.`t5`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`product_tree` AS `t7` ON ((`mockt`.`t5`.`model_ver_series_id` = `mockt`.`t7`.`id`) AND (`mockt`.`t7`.`op` IN ('+I', '+U'))) AND (`mockt`.`t7`.`deleted_flag` = 0) LEFT OUTER JOIN `mockt`.`product_tree` AS `t8` ON ((`mockt`.`t`.`marketing_id` = `mockt`.`t8`.`id`) AND (`mockt`.`t8`.`op` IN ('+I', '+U'))) AND (`mockt`.`t8`.`deleted_flag` = 0) LEFT OUTER JOIN `mockt`.`data_dictionary_type` AS `t6` ON ((`mockt`.`t5`.`signal_system` = `mockt`.`t6`.`id`) AND (`mockt`.`t6`.`delete_flag` = 0)) AND (`mockt`.`t6`.`op` IN ('+I', '+U'))\nWHERE (`mockt`.`t`.`deleted_flag` = 0) AND (`mockt`.`t`.`op` IN ('+I', '+U'))", "mockt.v_dim_employee": "SELECT `mockt`.`e`.`id` AS `employee_id`, `mockt`.`e`.`employee_code`, `mockt`.`e`.`name`, `mockt`.`e`.`alias_name`, `mockt`.`u`.`id` AS `user_id`, `mockt`.`p`.`position_attr` AS `position_attr_id`, `mockt`.`d2`.`val_china` AS `position_attr`, `mockt`.`d2`.`val_english` AS `position_attr_en`, `mockt`.`e`.`employee_type` AS `employee_type_id`, `mockt`.`t1`.`val_china` AS `employee_type`, `mockt`.`t1`.`val_english` AS `employee_type_en`, `mockt`.`p`.`name` AS `position_name`, `mockt`.`e`.`status`, `mockt`.`eee`.`employee_code` AS `report_to_employee_code`, `mockt`.`eee`.`name` AS `report_to_employee_name`\nFROM `mockt`.`employee` AS `e` LEFT OUTER JOIN `mockt`.`user` AS `u` ON ((`mockt`.`e`.`id` = `mockt`.`u`.`employee_id`) AND (`mockt`.`u`.`delete_flag` = 0)) AND (`mockt`.`u`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`data_dictionary_type` AS `t1` ON ((`mockt`.`e`.`employee_type` = `mockt`.`t1`.`id`) AND (`mockt`.`t1`.`delete_flag` = 0)) AND (`mockt`.`t1`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`employee_position` AS `ep` ON (((`mockt`.`ep`.`employee_id` = `mockt`.`e`.`id`) AND (`mockt`.`ep`.`delete_flag` = 0)) AND (`mockt`.`ep`.`op` IN ('+I', '+U'))) AND (`mockt`.`ep`.`main_flag` = 1) LEFT OUTER JOIN `mockt`.`position` AS `p` ON ((`mockt`.`ep`.`position_id` = `mockt`.`p`.`id`) AND (`mockt`.`p`.`delete_flag` = 0)) AND (`mockt`.`p`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`data_dictionary_type` AS `d2` ON ((`mockt`.`p`.`position_attr` = `mockt`.`d2`.`id`) AND (`mockt`.`d2`.`delete_flag` = 0)) AND (`mockt`.`d2`.`op` IN ('+I', '+U')) LEFT OUTER JOIN `mockt`.`employee` AS `eee` ON ((`mockt`.`ep`.`report_to_employee_id` = `mockt`.`eee`.`id`) AND (`mockt`.`eee`.`op` IN ('+I', '+U'))) AND (`mockt`.`eee`.`delete_flag` = 0)\nWHERE ((`mockt`.`e`.`op` IN ('+I', '+U')) AND (`mockt`.`e`.`delete_flag` = 0)) AND (`mockt`.`u`.`id` IS NOT NULL)"}, "column_statistics": {"mockt.dim_sales_area_chain": {"first_theater_id": "[2.0210115000000112E17, 2.02402240000502304E17, 0.029465951424768835, 8.0, 323.0] ESTIMATE", "op": "[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE", "first_area_id": "[2.02101150000000032E17, 2.02403190000280864E17, 5.811824738613182E-6, 8.0, 27.0] ESTIMATE", "deleted_flag": "[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE", "town_id": "[2.02101150000001408E17, 2.02404220000477088E17, 0.052492401039154264, 8.0, 76843.0] ESTIMATE", "sales_area_id": "[2.01910110000001E14, 2.0240422000047728E17, 0.0, 8.0, 173170.0] ESTIMATE", "sales_area_path": "[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE", "sales_market_id": "[2.01910110000001E14, 2.01910110000001E14, 0.0, 8.0, 1.0] ESTIMATE", "second_area_id": "[2.02101150000001152E17, 2.02404220000359744E17, 0.05894933832375351, 8.0, 1667.0] ESTIMATE", "second_theater_id": "[2.02101150000001376E17, 2.02404130000135936E17, 0.5292654434712867, 8.0, 3207.0] ESTIMATE", "sales_unit_id": "[2.02101150000015584E17, 2.0240422000047728E17, 0.48440978013867014, 8.0, 89225.0] ESTIMATE"}, "mockt.employee_position": {"report_to_employee_id": "[-10000.0, 2.02404160000333152E17, 0.17815855796625027, 8.0, 3097.0] ESTIMATE", "op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "main_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "employee_id": "[2.****************E17, 2.02404220000453504E17, 0.0, 8.0, 146696.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "position_id": "[2.0210113E17, 2.02404220000239008E17, 1.643655489809336E-4, 8.0, 6556.0] ESTIMATE"}, "mockt.store": {"store_code": "[-Infinity, Infinity, 6.623740554492552E-5, 9.180534322193697, 130558.0] ESTIMATE", "op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "superior_id": "[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 16.0, 106980.0] ESTIMATE", "branch_id": "[2.02101160000110016E17, 2.0240513000048176E17, 0.0, 16.0, 130431.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "status": "[1.0, 6.0, 0.0, 1.0, 3.0] ESTIMATE"}, "wide_vwork.wide_serialcode_center": {"customer_warehouse_code": "[-Infinity, Infinity, 0.0, 0.985805, 18343.0] ESTIMATE", "first_agent_code": "[-Infinity, Infinity, 0.0, 1.8E-4, 6.0] ESTIMATE", "op": "[-Infinity, Infinity, 0.0, 3.0E-5, 3.0] ESTIMATE", "del_flag": "[-Infinity, Infinity, 0.0, 1.0E-5, 2.0] ESTIMATE", "imei1": "[-Infinity, Infinity, 0.0, 24.06498, 6.6922594E7] ESTIMATE", "business_status": "[-Infinity, Infinity, 0.0, 2.65E-4, 6.0] ESTIMATE", "vivo_source": "[-Infinity, Infinity, 0.0, 1.8E-4, 6.0] ESTIMATE", "sku_code": "[-Infinity, Infinity, 0.0, 0.01892, 473.0] ESTIMATE", "customer_account_code": "[-Infinity, Infinity, 0.0, 0.656275, 11059.0] ESTIMATE"}, "mockt.product_exp": {"master_id": "[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 23337.0] ESTIMATE", "op": "[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE", "signal_system": "[2.0191012000005492E16, 2.0191218000005496E16, 0.****************, 16.0, 2.0] ESTIMATE", "model_ver_series_id": "[1.0, 2.024041100002328E17, 0.****************, 16.0, 411.0] ESTIMATE", "deleted_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE"}, "mockt.sales_area": {"op": "[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE", "path": "[-Infinity, Infinity, 0.0, 17.***************, 313339.0] ESTIMATE", "branch_id": "[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE", "deleted_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "status": "[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE"}, "vk_sale_center.serialcode_remark": {"del_flag": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "op": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "imei1": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "vivo_source": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN"}, "mockt.warehouse": {"superior_id": "[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE", "op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "deleted_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "warehouse_code": "[-Infinity, Infinity, 4.029215844085464E-6, 11.***************, 701395.0] ESTIMATE", "customer_id": "[10001.0, 2.02404220000479136E17, 0.0, 16.0, 106760.0] ESTIMATE", "status": "[1.0, 6.0, 0.0, 1.0, 3.0] ESTIMATE"}, "mockt.dim_customer_chain": {"op": "[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE", "province": "[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE", "city": "[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE", "customer_status": "[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE", "district": "[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE", "retailer_code": "[-Infinity, Infinity, 0.016296360711335275, 9.***************, 103911.0] ESTIMATE", "delete_flag": "[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE", "customer_id": "[10001.0, 2.02404220000479872E17, 0.0, 8.0, 228870.0] ESTIMATE", "customer_code": "[-Infinity, Infinity, 0.0, 9.***************, 231627.0] ESTIMATE"}, "mockt.retailer_channel": {"master_branch_id": "[2.02101160000004032E17, 2.02404220000479136E17, 0.0, 8.0, 103646.0] ESTIMATE", "op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "type": "[2.0191022000012412E16, 2.02301090000460576E17, 0.0, 8.0, 16.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE"}, "mockt.sales_area_link": {"master_id": "[10001.0, 2.02404220000516992E17, 0.0, 16.0, 1532166.0] ESTIMATE", "master_branch_id": "[10001.0, 2.02404220000479872E17, 0.17710345207844144, 16.0, 231818.0] ESTIMATE", "op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "sales_area_id": "[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 153981.0] ESTIMATE", "type": "[0.0, 6.0, 0.0, 1.0, 5.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "status": "[1.0, 6.0, 0.17707164203951126, 1.0, 6.0] ESTIMATE"}, "vk_hive.dim_vwork_province_mapping2": {"erp_code": "[-Infinity, Infinity, 0.0, 6.25, 24.0] ESTIMATE", "agent1_code": "[-Infinity, Infinity, 0.0, 5.***************, 24.0] ESTIMATE"}, "mockt.user": {"op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "employee_id": "[1.0, 4.0202209121411744E17, 0.0, 8.0, 139305.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "employee_code": "[-Infinity, Infinity, 0.0, 9.***************, 139003.0] ESTIMATE"}, "mockt.retailer": {"number": "[-Infinity, Infinity, 1.7151620323661165E-4, 9.***************, 111546.0] ESTIMATE", "op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "branch_id": "[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 8.0, 109479.0] ESTIMATE", "key_account_name": "[2.02007070000223808E17, 2.02404160000157216E17, 0.****************, 8.0, 129.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "status": "[1.0, 6.0, 0.0, 1.0, 5.0] ESTIMATE"}, "mockt.data_dictionary_type": {"op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "val_china": "[-Infinity, Infinity, 0.0, 9.***************, 2520.0] ESTIMATE", "id": "[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 3322.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "p_id": "[2.0190927000000144E16, 2.0240304200335952E17, 0.0, 16.0, 262.0] ESTIMATE"}, "mockt.position": {"op": "[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE", "position_attr": "[2.02008280002393536E17, 2.02108180034518528E17, 0.0, 8.0, 12.0] ESTIMATE", "id": "[2.0210113E17, 2.02404220000263488E17, 0.0, 8.0, 11207.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE"}, "mockt.data_permission": {"op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "user_id": "[2.****************E17, 4.0202209121411808E17, 0.0, 16.0, 123872.0] ESTIMATE", "data_id": "[0.0, 2.0240422000036496E17, 0.0, 16.0, 105080.0] ESTIMATE", "data_type": "[0.0, 5.0, 0.0, 1.0, 6.0] ESTIMATE"}, "mockt.employee": {"op": "[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE", "employee_type": "[-Infinity, Infinity, 5.671884800683295E-4, 15.216759418665172, 9.0] ESTIMATE", "id": "[-10000.0, 2.02405130000485856E17, 0.0, 16.0, 149370.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "status": "[0.0, 2.0, 0.0, 1.0, 3.0] ESTIMATE", "employee_code": "[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 148445.0] ESTIMATE"}, "mockt.agency": {"op": "[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE", "number": "[-Infinity, Infinity, 4.957448566471123E-4, 8.***************, 4020.0] ESTIMATE", "branch_id": "[10001.0, 2.02405100000051104E17, 0.0, 8.0, 3981.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "status": "[1.0, 6.0, 0.0, 1.0, 3.0] ESTIMATE"}, "mockt.account": {"op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "number": "[-Infinity, Infinity, 9.915716410510659E-4, 12.***************, 106400.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE"}, "vk_sale_center.serialcode_ext": {"serial_code_key": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "del_flag": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "op": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "update_time": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "imei1": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "vivo_source": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "serial_code_value": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN"}, "mockt.language_support": {"op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "data_id": "[1.0000001E7, 3.0230818100753997E18, 0.0, 8.0, 17411.0] ESTIMATE", "language": "[-Infinity, Infinity, 0.0, 2.****************, 21.0] ESTIMATE", "delete_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE"}, "mockt.product": {"model_series": "[2.0210115000012E17, 2.02404110000232768E17, 0.0038058186738836267, 16.0, 210.0] ESTIMATE", "op": "[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE", "deleted_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "product_series": "[2.0210115000012E17, 2.02404150000425472E17, 0.0, 16.0, 67.0] ESTIMATE", "marketing_id": "[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE", "id": "[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 23822.0] ESTIMATE", "category": "[2.01909260000002E16, 2.02009090000052192E17, 0.0, 16.0, 4.0] ESTIMATE", "sku_code": "[-Infinity, Infinity, 0.0, 4.*************, 11379.0] ESTIMATE", "brand": "[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 8.0] ESTIMATE"}, "mockt.product_tree": {"op": "[-Infinity, Infinity, 0.0, 2.0, 3.0] ESTIMATE", "deleted_flag": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "id": "[2.0210115000012E17, 3.5210115000012211E17, 0.0, 16.0, 416.0] ESTIMATE"}, "wide_vwork.wide_return_serialcode_center": {"customer_warehouse_code": "[-Infinity, Infinity, 0.0, 11.0, 1.0] ESTIMATE", "first_agent_code": "[-Infinity, Infinity, 0.0, 6.****************, 23.0] ESTIMATE", "op": "[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE", "del_flag": "[-Infinity, Infinity, 0.0, 1.0, 1.0] ESTIMATE", "imei1": "[-Infinity, Infinity, 0.0, 24.**************, 900740.0] ESTIMATE", "business_status": "[-Infinity, Infinity, 0.0, 14.0, 1.0] ESTIMATE", "vivo_source": "[-Infinity, Infinity, 0.0, 6.****************, 23.0] ESTIMATE", "sku_code": "[-Infinity, Infinity, 0.0, 8.0, 586.0] ESTIMATE", "customer_account_code": "[-Infinity, Infinity, 0.0, 12.0, 1.0] ESTIMATE"}, "vk_sale_center.serialcode_sales_report_detail": {"op": "[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE", "del_flag": "[-Infinity, Infinity, 0.0, 1.0, 2.0] ESTIMATE", "imei1": "[-Infinity, Infinity, 0.003943769963200337, 23.***************, 519651.0] ESTIMATE", "report_time": "[1.608288282E9, 1.715594211E9, 0.0, 8.0, 321521.0] ESTIMATE", "report_user_code": "[-Infinity, Infinity, 0.0, 9.***************, 16006.0] ESTIMATE", "sales_report_type": "[-Infinity, Infinity, 0.0, 4.***************, 3.0] ESTIMATE", "vivo_source": "[-Infinity, Infinity, 0.0, 6.****************, 13.0] ESTIMATE"}}, "explain_info": "PLAN FRAGMENT 0(F255)\n  Output Exprs:5042: count\n  Input Partition: UNPARTITIONED\n  RESULT SINK\n\n  582:AGGREGATE (merge finalize)\n  |  aggregate: count[([5042: count, BIGINT, false]); args: TINYINT; result: BIGINT; args nullable: true; result nullable: false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[0.0, 4.665368246292599E11, 0.0, 8.0, 1.0] ESTIMATE\n  |  \n  581:EXCHANGE\n     distribution type: GATHER\n     cardinality: 1\n\nPLAN FRAGMENT 1(F214)\n\n  Input Partition: HASH_PARTITIONED: 4301: imei1, 4297: vivo_source\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 581\n\n  580:AGGREGATE (update serialize)\n  |  aggregate: count[(1); args: TINYINT; result: BIGINT; args nullable: false; result nullable: false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * count-->[0.0, 4.665368246292599E11, 0.0, 8.0, 1.0] ESTIMATE\n  |  \n  579:Project\n  |  output columns:\n  |  5100 <-> 1\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * auto_fill_col-->[1.0, 1.0, 0.0, 1.0, 1.0] ESTIMATE\n  |  \n  578:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [4985: key_account_name, BIGINT, true] = [5034: data_id, BIGINT, true]\n  |  output columns: 5034\n  |  can local shuffle: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * key_account_name-->[2.02007070000223808E17, 2.02404160000157216E17, 0.0, 8.0, 129.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.****************, 2.0, 2.0] ESTIMATE\n  |  * data_id-->[1.0000001E7, 3.0230818100753997E18, 0.****************, 8.0, 3019.************] ESTIMATE\n  |  * language-->[-Infinity, Infinity, 0.****************, 2.****************, 21.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.****************, 1.0, 2.0] ESTIMATE\n  |  * auto_fill_col-->[1.0, 1.0, 0.0, 1.0, 1.0] ESTIMATE\n  |  \n  |----577:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 3019\n  |    \n  574:Project\n  |  output columns:\n  |  4985 <-> [4985: key_account_name, BIGINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * key_account_name-->[2.02007070000223808E17, 2.02404160000157216E17, 0.****************, 8.0, 129.0] ESTIMATE\n  |  \n  573:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5019: type, BIGINT, true] = [5024: data_id, BIGINT, true]\n  |  output columns: 4985\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * key_account_name-->[2.02007070000223808E17, 2.02404160000157216E17, 0.****************, 8.0, 129.0] ESTIMATE\n  |  * type-->[2.0191022000012412E16, 2.02301090000460576E17, 0.0, 8.0, 16.0] ESTIMATE\n  |  * data_id-->[2.0191022000012412E16, 2.02301090000460576E17, 0.0, 8.0, 16.0] ESTIMATE\n  |  \n  |----572:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 3019\n  |    \n  569:Project\n  |  output columns:\n  |  4985 <-> [4985: key_account_name, BIGINT, true]\n  |  5019 <-> [5019: type, BIGINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * key_account_name-->[2.02007070000223808E17, 2.02404160000157216E17, 0.****************, 8.0, 129.0] ESTIMATE\n  |  * type-->[2.0191022000012412E16, 2.02301090000460576E17, 0.0, 8.0, 16.0] ESTIMATE\n  |  \n  568:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [4934: branch_id, BIGINT, true] = [5014: master_branch_id, BIGINT, true]\n  |  output columns: 4985, 5019\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * branch_id-->[2.02101160000004032E17, 2.02404220000479136E17, 0.0, 8.0, 103646.0] ESTIMATE\n  |  * key_account_name-->[2.02007070000223808E17, 2.02404160000157216E17, 0.****************, 8.0, 129.0] ESTIMATE\n  |  * master_branch_id-->[2.02101160000004032E17, 2.02404220000479136E17, 0.0, 8.0, 103646.0] ESTIMATE\n  |  * type-->[2.0191022000012412E16, 2.02301090000460576E17, 0.0, 8.0, 16.0] ESTIMATE\n  |  \n  |----567:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 194251\n  |    \n  564:Project\n  |  output columns:\n  |  4934 <-> [4934: branch_id, BIGINT, true]\n  |  4985 <-> [4985: key_account_name, BIGINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * branch_id-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 8.0, 109479.0] ESTIMATE\n  |  * key_account_name-->[2.02007070000223808E17, 2.02404160000157216E17, 0.****************, 8.0, 129.0] ESTIMATE\n  |  \n  563:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [4071: retailer_code, VARCHAR, true] = [4935: number, VARCHAR, true]\n  |  output columns: 4934, 4985\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * retailer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * branch_id-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 8.0, 109479.0] ESTIMATE\n  |  * number-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * key_account_name-->[2.02007070000223808E17, 2.02404160000157216E17, 0.****************, 8.0, 129.0] ESTIMATE\n  |  \n  |----562:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 198232\n  |    \n  559:Project\n  |  output columns:\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  \n  558:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3764: imei1, VARCHAR(127), true] = [4893: imei1, VARCHAR, true]\n  |  equal join conjunct: [3785: vivo_source, VARCHAR(128), true] = [4892: vivo_source, VARCHAR, true]\n  |  output columns: 4071\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.9374999999999999, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.9374999999999999, 1.0, 1.0] UNKNOWN\n  |  * serial_code_key-->[-Infinity, Infinity, 0.9374999999999999, 1.0, 1.0] UNKNOWN\n  |  * del_flag-->[-Infinity, Infinity, 0.9374999999999999, 1.0, 1.0] UNKNOWN\n  |  \n  |----557:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1\n  |    \n  554:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  \n  553:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3790: category, LARGEINT, true] = [5043: cast, LARGEINT, true]\n  |  output columns: 3764, 3785, 4071\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * cast-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----552:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 3019\n  |    \n  549:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  \n  548:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3913: signal_system, LARGEINT, true] = [5044: cast, LARGEINT, true]\n  |  output columns: 3764, 3785, 3790, 4071\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * cast-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----547:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 3019\n  |    \n  544:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  \n  543:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3773: superior_id, LARGEINT, true] = [4775: branch_id, LARGEINT, true]\n  |  output columns: 3764, 3785, 3790, 3913, 4071\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.0, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * branch_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.0, 16.0, 123837.0] ESTIMATE\n  |  \n  |----542:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 312009\n  |    \n  539:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3773 <-> [3773: superior_id, LARGEINT, true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  \n  538:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [4311: report_user_code, VARCHAR, true] = [4755: employee_code, VARCHAR, true]\n  |  output columns: 3764, 3773, 3785, 3790, 3913, 4071\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.0, 9.***************, 16006.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 1.3193684805235233E-16, 2.0, 2.0] ESTIMATE\n  |  * employee_code-->[-Infinity, Infinity, 1.3193684805235233E-16, 9.***************, 46449.666666666664] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 1.3193684805235233E-16, 1.0, 2.0] ESTIMATE\n  |  \n  |----537:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 46450\n  |    \n  534:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3773 <-> [3773: superior_id, LARGEINT, true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  4311 <-> [4311: report_user_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.0, 9.***************, 16006.0] ESTIMATE\n  |  \n  533:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [4311: report_user_code, VARCHAR, true] = [4493: employee_code, VARCHAR, true]\n  |  output columns: 3764, 3773, 3785, 3790, 3913, 4071, 4311\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.0, 9.***************, 16006.0] ESTIMATE\n  |  * employee_code-->[-Infinity, Infinity, 0.9954782030343032, 9.***************, 20011.683729811648] ESTIMATE\n  |  * id-->[-10000.0, 2.02405130000485856E17, 0.9980518442309236, 16.0, 108234.5] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.9980518442309236, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.9980518442309236, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-10000.0, 2.02404160000333152E17, 0.9954780823387923, 16.0, 3097.0] ESTIMATE\n  |  \n  |----532:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 46450\n  |    \n  499:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3773 <-> [3773: superior_id, LARGEINT, true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  4311 <-> [4311: report_user_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.9980517922308765, 9.***************, 16006.0] ESTIMATE\n  |  \n  498:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3764: imei1, VARCHAR(127), true] = [4455: imei1, VARCHAR, true]\n  |  equal join conjunct: [3785: vivo_source, VARCHAR(128), true] = [4451: vivo_source, VARCHAR, true]\n  |  output columns: 3764, 3773, 3785, 3790, 3913, 4071, 4311\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.9980517922308765, 9.***************, 16006.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * op-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * del_flag-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  \n  |----497:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1\n  |    \n  494:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3773 <-> [3773: superior_id, LARGEINT, true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  4311 <-> [4311: report_user_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.9980517922308765, 9.***************, 16006.0] ESTIMATE\n  |  \n  493:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3764: imei1, VARCHAR(127), true] = [4410: imei1, VARCHAR, true]\n  |  equal join conjunct: [3785: vivo_source, VARCHAR(128), true] = [4409: vivo_source, VARCHAR, true]\n  |  output columns: 3764, 3773, 3785, 3790, 3913, 4071, 4311\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.9980517922308765, 9.***************, 16006.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * op-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * serial_code_key-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * update_time-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * del_flag-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * row_number()-->[1.0, 1.0, 0.9375, 1.0, 1.0] UNKNOWN\n  |  \n  |----492:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1\n  |    \n  484:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3773 <-> [3773: superior_id, LARGEINT, true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  4311 <-> [4311: report_user_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.9980517922308765, 9.***************, 16006.0] ESTIMATE\n  |  \n  483:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [4353: serial_code_value, VARCHAR, true] = [4386: val_china, VARCHAR, true]\n  |  output columns: 3764, 3773, 3785, 3790, 3913, 4071, 4311\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.9980517922308765, 9.***************, 16006.0] ESTIMATE\n  |  * serial_code_value-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * op-->[-Infinity, Infinity, 0.75, 2.0, 2.0] ESTIMATE\n  |  * val_china-->[-Infinity, Infinity, 0.75, 9.***************, 4.231552162849873] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.75, 1.0, 2.0] ESTIMATE\n  |  * p_id-->[2.02203181000000576E17, 2.02203181000000576E17, 0.75, 16.0, 4.231552162849873] ESTIMATE\n  |  \n  |----482:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 4\n  |    \n  479:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3773 <-> [3773: superior_id, LARGEINT, true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  4311 <-> [4311: report_user_code, VARCHAR, true]\n  |  4353 <-> [4353: serial_code_value, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.9980517922308765, 9.***************, 16006.0] ESTIMATE\n  |  * serial_code_value-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  \n  478:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [3764: imei1, VARCHAR(127), true] = [4343: imei1, VARCHAR, true]\n  |  equal join conjunct: [3785: vivo_source, VARCHAR(128), true] = [4342: vivo_source, VARCHAR, true]\n  |  output columns: 3764, 3773, 3785, 3790, 3913, 4071, 4311, 4353\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.9980517922308765, 9.***************, 16006.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * op-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * serial_code_key-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * serial_code_value-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  * del_flag-->[-Infinity, Infinity, 0.9375, 1.0, 1.0] UNKNOWN\n  |  \n  |----477:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [4343: imei1, VARCHAR, true], [4342: vivo_source, VARCHAR, true]\n  |       cardinality: 1\n  |    \n  474:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3773 <-> [3773: superior_id, LARGEINT, true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  4311 <-> [4311: report_user_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.9980517922308765, 9.***************, 16006.0] ESTIMATE\n  |  \n  473:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [3764: imei1, VARCHAR(127), true] = [4301: imei1, VARCHAR, true]\n  |  equal join conjunct: [3785: vivo_source, VARCHAR(128), true] = [4297: vivo_source, VARCHAR, true]\n  |  output columns: 3764, 3773, 3785, 3790, 3913, 4071, 4311\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 27023.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.9980517922308765, 6.****************, 2.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.9980517922308765, 2.0, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.9980594755141584, 23.***************, 27023.***********] ESTIMATE\n  |  * sales_report_type-->[-Infinity, Infinity, 0.9980517922308765, 4.***************, 2.0] ESTIMATE\n  |  * report_time-->[1.608288282E9, 1.715594211E9, 0.9980517922308765, 8.0, 27023.***********] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.9980517922308765, 9.***************, 16006.0] ESTIMATE\n  |  * del_flag-->[-Infinity, Infinity, 0.9980517922308765, 1.0, 2.0] ESTIMATE\n  |  * row_number()-->[1.0, 1.0, 0.9980517922308765, 1.0, 1.0] UNKNOWN\n  |  \n  |----472:Project\n  |    |  output columns:\n  |    |  4297 <-> [4297: vivo_source, VARCHAR, false]\n  |    |  4301 <-> [4301: imei1, VARCHAR, true]\n  |    |  4311 <-> [4311: report_user_code, VARCHAR, true]\n  |    |  cardinality: 13512\n  |    |  column statistics: \n  |    |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |    |  * imei1-->[-Infinity, Infinity, 0.003943769963200337, 23.***************, 27023.***********] ESTIMATE\n  |    |  * report_user_code-->[-Infinity, Infinity, 0.0, 9.***************, 16006.0] ESTIMATE\n  |    |  \n  |    471:SELECT\n  |    |  predicates: 4340: row_number() = 1\n  |    |  cardinality: 13512\n  |    |  column statistics: \n  |    |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |    |  * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n  |    |  * imei1-->[-Infinity, Infinity, 0.003943769963200337, 23.***************, 27023.***********] ESTIMATE\n  |    |  * sales_report_type-->[-Infinity, Infinity, 0.0, 4.***************, 2.0] ESTIMATE\n  |    |  * report_time-->[1.608288282E9, 1.715594211E9, 0.0, 8.0, 27023.***********] ESTIMATE\n  |    |  * report_user_code-->[-Infinity, Infinity, 0.0, 9.***************, 16006.0] ESTIMATE\n  |    |  * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 2.0] ESTIMATE\n  |    |  * row_number()-->[1.0, 1.0, 0.0, 1.0, 1.0] UNKNOWN\n  |    |  \n  |    470:ANALYTIC\n  |    |  functions: [, row_number[(); args: ; result: BIGINT; args nullable: false; result nullable: true], ]\n  |    |  partition by: [4301: imei1, VARCHAR, true], [4297: vivo_source, VARCHAR, false]\n  |    |  order by: [4309: report_time, DATETIME, true] DESC\n  |    |  window: ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW\n  |    |  cardinality: 27023\n  |    |  column statistics: \n  |    |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |    |  * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n  |    |  * imei1-->[-Infinity, Infinity, 0.003943769963200337, 23.***************, 27023.***********] ESTIMATE\n  |    |  * sales_report_type-->[-Infinity, Infinity, 0.0, 4.***************, 2.0] ESTIMATE\n  |    |  * report_time-->[1.608288282E9, 1.715594211E9, 0.0, 8.0, 27023.***********] ESTIMATE\n  |    |  * report_user_code-->[-Infinity, Infinity, 0.0, 9.***************, 16006.0] ESTIMATE\n  |    |  * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 2.0] ESTIMATE\n  |    |  * row_number()-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |    |  \n  |    469:SORT\n  |    |  order by: [4301, VARCHAR, true] ASC, [4297, VARCHAR, false] ASC, [4309, DATETIME, true] DESC\n  |    |  offset: 0\n  |    |  cardinality: 27023\n  |    |  column statistics: \n  |    |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |    |  * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n  |    |  * imei1-->[-Infinity, Infinity, 0.003943769963200337, 23.***************, 27023.***********] ESTIMATE\n  |    |  * sales_report_type-->[-Infinity, Infinity, 0.0, 4.***************, 2.0] ESTIMATE\n  |    |  * report_time-->[1.608288282E9, 1.715594211E9, 0.0, 8.0, 27023.***********] ESTIMATE\n  |    |  * report_user_code-->[-Infinity, Infinity, 0.0, 9.***************, 16006.0] ESTIMATE\n  |    |  * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 2.0] ESTIMATE\n  |    |  \n  |    468:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [4301: imei1, VARCHAR, true], [4297: vivo_source, VARCHAR, false]\n  |       cardinality: 27023\n  |    \n  464:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [3764: imei1, VARCHAR(127), true], [3785: vivo_source, VARCHAR(128), true]\n     cardinality: ************\n\nPLAN FRAGMENT 2(F253)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 577\n\n  576:Project\n  |  output columns:\n  |  5034 <-> [5034: data_id, BIGINT, true]\n  |  cardinality: 3019\n  |  column statistics: \n  |  * data_id-->[1.0000001E7, 3.0230818100753997E18, 0.0, 8.0, 3019.************] ESTIMATE\n  |  \n  575:OlapScanNode\n     table: language_support, rollup: language_support\n     preAggregation: on\n     Predicates: [5041: delete_flag, TINYINT, true] = 0, 5033: op IN ('+I', '+U'), [5035: language, VARCHAR, true] = 'en'\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16841\n     actualRows=190225, avgRowSize=13.853589\n     cardinality: 3019\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * data_id-->[1.0000001E7, 3.0230818100753997E18, 0.0, 8.0, 3019.************] ESTIMATE\n     * language-->[-Infinity, Infinity, 0.0, 2.****************, 21.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 3(F251)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 572\n\n  571:Project\n  |  output columns:\n  |  5024 <-> [5024: data_id, BIGINT, true]\n  |  cardinality: 3019\n  |  column statistics: \n  |  * data_id-->[1.0000001E7, 3.0230818100753997E18, 0.0, 8.0, 3019.************] ESTIMATE\n  |  \n  570:OlapScanNode\n     table: language_support, rollup: language_support\n     preAggregation: on\n     Predicates: [5031: delete_flag, TINYINT, true] = 0, 5023: op IN ('+I', '+U'), [5025: language, VARCHAR, true] = 'en'\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16841\n     actualRows=190225, avgRowSize=13.853589\n     cardinality: 3019\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * data_id-->[1.0000001E7, 3.0230818100753997E18, 0.0, 8.0, 3019.************] ESTIMATE\n     * language-->[-Infinity, Infinity, 0.0, 2.****************, 21.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 4(F249)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 567\n\n  566:Project\n  |  output columns:\n  |  5014 <-> [5014: master_branch_id, BIGINT, true]\n  |  5019 <-> [5019: type, BIGINT, true]\n  |  cardinality: 194251\n  |  column statistics: \n  |  * master_branch_id-->[2.02101160000004032E17, 2.02404220000479136E17, 0.0, 8.0, 103646.0] ESTIMATE\n  |  * type-->[2.0191022000012412E16, 2.02301090000460576E17, 0.0, 8.0, 16.0] ESTIMATE\n  |  \n  565:OlapScanNode\n     table: retailer_channel, rollup: retailer_channel\n     preAggregation: on\n     Predicates: [5021: delete_flag, TINYINT, true] = 0, 5012: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=16/16\n     tabletList=16136,16137,16138,16139,16140,16141,16142,16143,16144,16145 ...\n     actualRows=582833, avgRowSize=19.0\n     cardinality: 194251\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * master_branch_id-->[2.02101160000004032E17, 2.02404220000479136E17, 0.0, 8.0, 103646.0] ESTIMATE\n     * type-->[2.0191022000012412E16, 2.02301090000460576E17, 0.0, 8.0, 16.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 5(F247)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 562\n\n  561:Project\n  |  output columns:\n  |  4934 <-> [4934: branch_id, BIGINT, true]\n  |  4935 <-> [4935: number, VARCHAR, true]\n  |  4985 <-> [4985: key_account_name, BIGINT, true]\n  |  cardinality: 198232\n  |  column statistics: \n  |  * branch_id-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 8.0, 109479.0] ESTIMATE\n  |  * number-->[-Infinity, Infinity, 1.7151620323661165E-4, 9.***************, 111546.0] ESTIMATE\n  |  * key_account_name-->[2.02007070000223808E17, 2.02404160000157216E17, 0.****************, 8.0, 129.0] ESTIMATE\n  |  \n  560:OlapScanNode\n     table: retailer, rollup: retailer\n     preAggregation: on\n     Predicates: [4964: delete_flag, TINYINT, true] = 0, 4933: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=16/16\n     tabletList=16770,16771,16772,16773,16774,16775,16776,16777,16778,16779 ...\n     actualRows=601020, avgRowSize=28.181791\n     cardinality: 198232\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 8.0, 109479.0] ESTIMATE\n     * number-->[-Infinity, Infinity, 1.7151620323661165E-4, 9.***************, 111546.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * key_account_name-->[2.02007070000223808E17, 2.02404160000157216E17, 0.****************, 8.0, 129.0] ESTIMATE\n\nPLAN FRAGMENT 6(F245)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 557\n\n  556:Project\n  |  output columns:\n  |  4892 <-> [4892: vivo_source, VARCHAR, false]\n  |  4893 <-> [4893: imei1, VARCHAR, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  555:OlapScanNode\n     table: serialcode_ext, rollup: serialcode_ext\n     preAggregation: on\n     Predicates: [4911: del_flag, VARCHAR, true] = 'N', [4902: serial_code_key, VARCHAR, true] = 'demoPhoneRecord'\n     partitionsRatio=0/1, tabletsRatio=0/0\n     tabletList=\n     actualRows=0, avgRowSize=4.0\n     cardinality: 1\n     column statistics: \n     * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * serial_code_key-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n\nPLAN FRAGMENT 7(F243)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 552\n\n  551:Project\n  |  output columns:\n  |  5043 <-> cast([4883: data_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 3019\n  |  column statistics: \n  |  * cast-->[1.0000001E7, 3.0230818100753997E18, 0.0, 16.0, 3019.************] ESTIMATE\n  |  \n  550:OlapScanNode\n     table: language_support, rollup: language_support\n     preAggregation: on\n     Predicates: [4890: delete_flag, TINYINT, true] = 0, 4882: op IN ('+I', '+U'), [4884: language, VARCHAR, true] = 'en'\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16841\n     actualRows=190225, avgRowSize=29.85359\n     cardinality: 3019\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * data_id-->[1.0000001E7, 3.0230818100753997E18, 0.0, 8.0, 3019.************] ESTIMATE\n     * language-->[-Infinity, Infinity, 0.0, 2.****************, 21.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[1.0000001E7, 3.0230818100753997E18, 0.0, 16.0, 3019.************] ESTIMATE\n\nPLAN FRAGMENT 8(F241)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 547\n\n  546:Project\n  |  output columns:\n  |  5044 <-> cast([4873: data_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 3019\n  |  column statistics: \n  |  * cast-->[1.0000001E7, 3.0230818100753997E18, 0.0, 16.0, 3019.************] ESTIMATE\n  |  \n  545:OlapScanNode\n     table: language_support, rollup: language_support\n     preAggregation: on\n     Predicates: [4880: delete_flag, TINYINT, true] = 0, 4872: op IN ('+I', '+U'), [4874: language, VARCHAR, true] = 'en'\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16841\n     actualRows=190225, avgRowSize=29.85359\n     cardinality: 3019\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * data_id-->[1.0000001E7, 3.0230818100753997E18, 0.0, 8.0, 3019.************] ESTIMATE\n     * language-->[-Infinity, Infinity, 0.0, 2.****************, 21.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[1.0000001E7, 3.0230818100753997E18, 0.0, 16.0, 3019.************] ESTIMATE\n\nPLAN FRAGMENT 9(F239)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 542\n\n  541:Project\n  |  output columns:\n  |  4775 <-> [4775: branch_id, LARGEINT, false]\n  |  cardinality: 312009\n  |  column statistics: \n  |  * branch_id-->[2.02101160000110016E17, 2.0240513000048176E17, 0.0, 16.0, 130431.0] ESTIMATE\n  |  \n  540:OlapScanNode\n     table: store, rollup: store\n     preAggregation: on\n     Predicates: [4807: delete_flag, TINYINT, false] = 0, 4774: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=2/2\n     tabletList=16797,16798\n     actualRows=986567, avgRowSize=19.0\n     cardinality: 312009\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.02101160000110016E17, 2.0240513000048176E17, 0.0, 16.0, 130431.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 10(F237)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 537\n\n  536:Project\n  |  output columns:\n  |  4755 <-> [4755: employee_code, VARCHAR, true]\n  |  cardinality: 46450\n  |  column statistics: \n  |  * employee_code-->[-Infinity, Infinity, 0.0, 9.***************, 46449.666666666664] ESTIMATE\n  |  \n  535:OlapScanNode\n     table: user, rollup: user\n     preAggregation: on\n     Predicates: [4770: delete_flag, TINYINT, true] = 0, 4753: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=16/16\n     tabletList=17075,17076,17077,17078,17079,17080,17081,17082,17083,17084 ...\n     actualRows=145771, avgRowSize=12.132279\n     cardinality: 46450\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * employee_code-->[-Infinity, Infinity, 0.0, 9.***************, 46449.666666666664] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 11(F223)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 532\n\n  531:Project\n  |  output columns:\n  |  4493 <-> [4493: employee_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 46450\n  |  column statistics: \n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 20011.683729811648] ESTIMATE\n  |  \n  530:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (BUCKET_SHUFFLE)\n  |  equal join conjunct: [4687: id, LARGEINT, true] = [5045: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 38, build_expr = (5045: cast), remote = false\n  |  output columns: 4493\n  |  can local shuffle: false\n  |  cardinality: 46450\n  |  column statistics: \n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 20011.683729811648] ESTIMATE\n  |  * id-->[-10000.0, 2.02405130000485856E17, 0.5691748689302761, 16.0, 108234.5] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.5691748689302761, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.5691748689302761, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-10000.0, 2.02404160000333152E17, 0.0, 16.0, 3097.0] ESTIMATE\n  |  \n  |----529:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5045: cast, LARGEINT, true]\n  |       cardinality: 46450\n  |    \n  501:Project\n  |  output columns:\n  |  4687 <-> [4687: id, LARGEINT, false]\n  |  cardinality: 108235\n  |  column statistics: \n  |  * id-->[-10000.0, 2.02405130000485856E17, 0.0, 16.0, 108234.5] ESTIMATE\n  |  \n  500:OlapScanNode\n     table: employee, rollup: employee\n     preAggregation: on\n     Predicates: 4688: op IN ('+I', '+U'), [4720: delete_flag, TINYINT, true] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15957\n     actualRows=216318, avgRowSize=19.0\n     cardinality: 108235\n     probe runtime filters:\n     - filter_id = 38, probe_expr = (4687: id)\n     column statistics: \n     * id-->[-10000.0, 2.02405130000485856E17, 0.0, 16.0, 108234.5] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 12(F224)\n\n  Input Partition: RANDOM\n  OutPut Partition: BUCKET_SHUFFLE_HASH_PARTITIONED: 5045: cast\n  OutPut Exchange Id: 529\n\n  528:Project\n  |  output columns:\n  |  4493 <-> [4493: employee_code, VARCHAR, true]\n  |  5045 <-> cast([4625: report_to_employee_id, BIGINT, true] as LARGEINT)\n  |  hasNullableGenerateChild: true\n  |  cardinality: 46450\n  |  column statistics: \n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 20767.83632449906] ESTIMATE\n  |  * cast-->[-10000.0, 2.02404160000333152E17, 0.5691748689302761, 16.0, 3097.0] ESTIMATE\n  |  \n  527:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5046: cast, LARGEINT, true] = [4661: id, LARGEINT, true]\n  |  output columns: 4493, 4625\n  |  can local shuffle: false\n  |  cardinality: 46450\n  |  column statistics: \n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 20767.83632449906] ESTIMATE\n  |  * report_to_employee_id-->[-10000.0, 2.02404160000333152E17, 0.5691748689302761, 8.0, 3097.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.552895901847181, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.552895901847181, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.552895901847181, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-10000.0, 2.02404160000333152E17, 0.5691748689302761, 16.0, 3097.0] ESTIMATE\n  |  * cast-->[2.02008280002393536E17, 2.02108180034518528E17, 0.0, 16.0, 12.0] ESTIMATE\n  |  \n  |----526:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  523:Project\n  |  output columns:\n  |  4493 <-> [4493: employee_code, VARCHAR, true]\n  |  4625 <-> [4625: report_to_employee_id, BIGINT, true]\n  |  5046 <-> cast([4650: position_attr, BIGINT, true] as LARGEINT)\n  |  hasNullableGenerateChild: true\n  |  cardinality: 46450\n  |  column statistics: \n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 20767.83632449906] ESTIMATE\n  |  * report_to_employee_id-->[-10000.0, 2.02404160000333152E17, 0.5691748689302761, 8.0, 3097.0] ESTIMATE\n  |  * cast-->[2.02008280002393536E17, 2.02108180034518528E17, 0.552895901847181, 16.0, 12.0] ESTIMATE\n  |  \n  522:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [4619: position_id, BIGINT, true] = [4637: id, BIGINT, true]\n  |  output columns: 4493, 4625, 4650\n  |  can local shuffle: false\n  |  cardinality: 46450\n  |  column statistics: \n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 20767.83632449906] ESTIMATE\n  |  * position_id-->[2.0210113E17, 2.02404220000239008E17, 0.0, 8.0, 5592.5] ESTIMATE\n  |  * report_to_employee_id-->[-10000.0, 2.02404160000333152E17, 0.5691748689302761, 8.0, 3097.0] ESTIMATE\n  |  * id-->[2.0210113E17, 2.02404220000263488E17, 0.552895901847181, 8.0, 5592.5] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.552895901847181, 2.0, 2.0] ESTIMATE\n  |  * position_attr-->[2.02008280002393536E17, 2.02108180034518528E17, 0.552895901847181, 8.0, 12.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.552895901847181, 1.0, 2.0] ESTIMATE\n  |  * cast-->[2.02008280002393536E17, 2.02108180034518528E17, 0.552895901847181, 16.0, 12.0] ESTIMATE\n  |  \n  |----521:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 5593\n  |    \n  518:Project\n  |  output columns:\n  |  4493 <-> [4493: employee_code, VARCHAR, true]\n  |  4619 <-> [4619: position_id, BIGINT, true]\n  |  4625 <-> [4625: report_to_employee_id, BIGINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 46450\n  |  column statistics: \n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 24349.81093225261] ESTIMATE\n  |  * position_id-->[2.0210113E17, 2.02404220000239008E17, 0.*****************, 8.0, 6556.0] ESTIMATE\n  |  * report_to_employee_id-->[-10000.0, 2.02404160000333152E17, 0.5691748689302761, 8.0, 3097.0] ESTIMATE\n  |  \n  517:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BUCKET_SHUFFLE)\n  |  equal join conjunct: [4491: id, LARGEINT, true] = [5047: cast, LARGEINT, true]\n  |  output columns: 4493, 4619, 4625\n  |  can local shuffle: false\n  |  cardinality: 46450\n  |  column statistics: \n  |  * id-->[2.****************E17, 2.02404220000453504E17, 0.0, 16.0, 24336.0] ESTIMATE\n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 24349.81093225261] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.*****************, 2.0, 2.0] ESTIMATE\n  |  * employee_id-->[2.****************E17, 2.02404220000453504E17, 0.*****************, 8.0, 24336.0] ESTIMATE\n  |  * main_flag-->[1.0, 1.0, 0.*****************, 1.0, 2.0] ESTIMATE\n  |  * position_id-->[2.0210113E17, 2.02404220000239008E17, 0.*****************, 8.0, 6556.0] ESTIMATE\n  |  * report_to_employee_id-->[-10000.0, 2.02404160000333152E17, 0.5691748689302761, 8.0, 3097.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.*****************, 1.0, 2.0] ESTIMATE\n  |  * cast-->[2.****************E17, 2.02404220000453504E17, 0.*****************, 16.0, 24336.0] ESTIMATE\n  |  \n  |----516:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5047: cast, LARGEINT, true]\n  |       cardinality: 24336\n  |    \n  513:Project\n  |  output columns:\n  |  4491 <-> [4491: id, LARGEINT, false]\n  |  4493 <-> [4493: employee_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 46450\n  |  column statistics: \n  |  * id-->[1.0, 2.02405130000485856E17, 0.0, 16.0, 46423.320950830326] ESTIMATE\n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 46423.320950830326] ESTIMATE\n  |  \n  512:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [4536: employee_type, VARCHAR, true] = [5048: cast, VARCHAR(1048576), true]\n  |  output columns: 4491, 4493\n  |  can local shuffle: false\n  |  cardinality: 46450\n  |  column statistics: \n  |  * id-->[1.0, 2.02405130000485856E17, 0.0, 16.0, 46423.320950830326] ESTIMATE\n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 46423.320950830326] ESTIMATE\n  |  * employee_type-->[2.01912345E8, 1.20469697139566182E18, 0.0, 15.216759418665172, 9.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 5.671884800681324E-4, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 5.671884800681324E-4, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 5.671884800681324E-4, 1.0, 2.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 5.671884800681324E-4, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  |----511:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  508:Project\n  |  output columns:\n  |  4491 <-> [4491: id, LARGEINT, false]\n  |  4493 <-> [4493: employee_code, VARCHAR, true]\n  |  4536 <-> [4536: employee_type, VARCHAR, true]\n  |  cardinality: 46450\n  |  column statistics: \n  |  * id-->[1.0, 2.02405130000485856E17, 0.0, 16.0, 46449.666666666664] ESTIMATE\n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 46449.666666666664] ESTIMATE\n  |  * employee_type-->[-Infinity, Infinity, 5.671884800683295E-4, 15.216759418665172, 9.0] ESTIMATE\n  |  \n  507:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE)\n  |  equal join conjunct: [4491: id, LARGEINT, false] = [5049: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 37, build_expr = (5049: cast), remote = false\n  |  output columns: 4491, 4493, 4536\n  |  can local shuffle: false\n  |  cardinality: 46450\n  |  column statistics: \n  |  * id-->[1.0, 2.02405130000485856E17, 0.0, 16.0, 46449.666666666664] ESTIMATE\n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 46449.666666666664] ESTIMATE\n  |  * employee_type-->[-Infinity, Infinity, 5.671884800683295E-4, 15.216759418665172, 9.0] ESTIMATE\n  |  * cast-->[1.0, 2.02405130000485856E17, 0.0, 16.0, 46449.666666666664] ESTIMATE\n  |  \n  |----506:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5049: cast, LARGEINT, true]\n  |       cardinality: 46450\n  |    \n  503:Project\n  |  output columns:\n  |  4491 <-> [4491: id, LARGEINT, false]\n  |  4493 <-> [4493: employee_code, VARCHAR, true]\n  |  4536 <-> [4536: employee_type, VARCHAR, true]\n  |  cardinality: 72156\n  |  column statistics: \n  |  * id-->[-10000.0, 2.02405130000485856E17, 0.0, 16.0, 72156.33333333333] ESTIMATE\n  |  * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 72156.33333333333] ESTIMATE\n  |  * employee_type-->[-Infinity, Infinity, 5.671884800683295E-4, 15.216759418665172, 9.0] ESTIMATE\n  |  \n  502:OlapScanNode\n     table: employee, rollup: employee\n     preAggregation: on\n     Predicates: 4497: status IN (0, 1), 4492: op IN ('+I', '+U'), [4524: delete_flag, TINYINT, true] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15957\n     actualRows=216318, avgRowSize=44.352833\n     cardinality: 72156\n     probe runtime filters:\n     - filter_id = 37, probe_expr = (4491: id)\n     column statistics: \n     * id-->[-10000.0, 2.02405130000485856E17, 0.0, 16.0, 72156.33333333333] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * employee_code-->[-Infinity, Infinity, 2.66912225914508E-5, 9.***************, 72156.33333333333] ESTIMATE\n     * status-->[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * employee_type-->[-Infinity, Infinity, 5.671884800683295E-4, 15.216759418665172, 9.0] ESTIMATE\n\nPLAN FRAGMENT 13(F233)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 526\n\n  525:Project\n  |  output columns:\n  |  4661 <-> [4661: id, LARGEINT, false]\n  |  cardinality: 1109\n  |  column statistics: \n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  524:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [4675: delete_flag, TINYINT, true] = 0, 4662: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=19.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 14(F231)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 521\n\n  520:Project\n  |  output columns:\n  |  4637 <-> [4637: id, BIGINT, false]\n  |  4650 <-> [4650: position_attr, BIGINT, true]\n  |  cardinality: 5593\n  |  column statistics: \n  |  * id-->[2.0210113E17, 2.02404220000263488E17, 0.0, 8.0, 5592.5] ESTIMATE\n  |  * position_attr-->[2.02008280002393536E17, 2.02108180034518528E17, 0.0, 8.0, 12.0] ESTIMATE\n  |  \n  519:OlapScanNode\n     table: position, rollup: position\n     preAggregation: on\n     Predicates: [4656: delete_flag, TINYINT, true] = 0, 4638: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=16/16\n     tabletList=16350,16351,16352,16353,16354,16355,16356,16357,16358,16359 ...\n     actualRows=11187, avgRowSize=19.0\n     cardinality: 5593\n     column statistics: \n     * id-->[2.0210113E17, 2.02404220000263488E17, 0.0, 8.0, 5592.5] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * position_attr-->[2.02008280002393536E17, 2.02108180034518528E17, 0.0, 8.0, 12.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 15(F229)\n\n  Input Partition: RANDOM\n  OutPut Partition: BUCKET_SHUFFLE_HASH_PARTITIONED: 5047: cast\n  OutPut Exchange Id: 516\n\n  515:Project\n  |  output columns:\n  |  4619 <-> [4619: position_id, BIGINT, true]\n  |  4625 <-> [4625: report_to_employee_id, BIGINT, true]\n  |  5047 <-> cast([4605: employee_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 24336\n  |  column statistics: \n  |  * position_id-->[2.0210113E17, 2.02404220000239008E17, 1.643655489809336E-4, 8.0, 6556.0] ESTIMATE\n  |  * report_to_employee_id-->[-10000.0, 2.02404160000333152E17, 0.17815855796625027, 8.0, 3097.0] ESTIMATE\n  |  * cast-->[2.****************E17, 2.02404220000453504E17, 0.0, 16.0, 24336.0] ESTIMATE\n  |  \n  514:OlapScanNode\n     table: employee_position, rollup: employee_position\n     preAggregation: on\n     Predicates: [4632: delete_flag, TINYINT, true] = 0, 4604: op IN ('+I', '+U'), [4608: main_flag, TINYINT, true] = 1\n     partitionsRatio=1/1, tabletsRatio=16/16\n     tabletList=16331,16332,16333,16334,16335,16336,16337,16338,16339,16340 ...\n     actualRows=147019, avgRowSize=44.0\n     cardinality: 24336\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * employee_id-->[2.****************E17, 2.02404220000453504E17, 0.0, 8.0, 24336.0] ESTIMATE\n     * main_flag-->[1.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE\n     * position_id-->[2.0210113E17, 2.02404220000239008E17, 1.643655489809336E-4, 8.0, 6556.0] ESTIMATE\n     * report_to_employee_id-->[-10000.0, 2.02404160000333152E17, 0.17815855796625027, 8.0, 3097.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.****************E17, 2.02404220000453504E17, 0.0, 16.0, 24336.0] ESTIMATE\n\nPLAN FRAGMENT 16(F227)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 511\n\n  510:Project\n  |  output columns:\n  |  5048 <-> cast([4577: id, LARGEINT, false] as VARCHAR(1048576))\n  |  cardinality: 1109\n  |  column statistics: \n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  509:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [4591: delete_flag, TINYINT, true] = 0, 4578: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=35.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n\nPLAN FRAGMENT 17(F225)\n\n  Input Partition: RANDOM\n  OutPut Partition: BUCKET_SHUFFLE_HASH_PARTITIONED: 5049: cast\n  OutPut Exchange Id: 506\n\n  505:Project\n  |  output columns:\n  |  5049 <-> cast([4558: employee_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 46450\n  |  column statistics: \n  |  * cast-->[1.0, 4.0202209121411744E17, 0.0, 16.0, 46449.666666666664] ESTIMATE\n  |  \n  504:OlapScanNode\n     table: user, rollup: user\n     preAggregation: on\n     Predicates: [4574: delete_flag, TINYINT, true] = 0, 4557: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=16/16\n     tabletList=17075,17076,17077,17078,17079,17080,17081,17082,17083,17084 ...\n     actualRows=145771, avgRowSize=27.0\n     cardinality: 46450\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * employee_id-->[1.0, 4.0202209121411744E17, 0.0, 8.0, 46449.666666666664] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[1.0, 4.0202209121411744E17, 0.0, 16.0, 46449.666666666664] ESTIMATE\n\nPLAN FRAGMENT 18(F221)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 497\n\n  496:Project\n  |  output columns:\n  |  4451 <-> [4451: vivo_source, VARCHAR, false]\n  |  4455 <-> [4455: imei1, VARCHAR, true]\n  |  cardinality: 1\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  495:OlapScanNode\n     table: serialcode_remark, rollup: serialcode_remark\n     preAggregation: on\n     Predicates: 4451: vivo_source IN ('INGJ01', 'vwork_sale_ruichuang'), 4453: op IN ('+I', '+U'), [4469: del_flag, VARCHAR, true] = 'N'\n     partitionsRatio=0/1, tabletsRatio=0/0\n     tabletList=\n     actualRows=0, avgRowSize=4.0\n     cardinality: 1\n     column statistics: \n     * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * op-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n\nPLAN FRAGMENT 19(F219)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 492\n\n  491:Project\n  |  output columns:\n  |  4409 <-> [4409: vivo_source, VARCHAR, false]\n  |  4410 <-> [4410: imei1, VARCHAR, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  490:SELECT\n  |  predicates: 4449: row_number() = 1\n  |  cardinality: 1\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * op-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * serial_code_key-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * update_time-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * row_number()-->[1.0, 1.0, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  489:ANALYTIC\n  |  functions: [, row_number[(); args: ; result: BIGINT; args nullable: false; result nullable: true], ]\n  |  partition by: [4410: imei1, VARCHAR, false], [4409: vivo_source, VARCHAR, false]\n  |  order by: [4426: update_time, DATETIME, true] DESC\n  |  window: ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW\n  |  cardinality: 1\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * op-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * serial_code_key-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * update_time-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * row_number()-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  488:SORT\n  |  order by: [4410, VARCHAR, false] ASC, [4409, VARCHAR, false] ASC, [4426, DATETIME, true] DESC\n  |  offset: 0\n  |  cardinality: 1\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * op-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * serial_code_key-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * update_time-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  487:PARTITION-TOP-N\n  |  partition by: [4410: imei1, VARCHAR, false] , [4409: vivo_source, VARCHAR, false] \n  |  partition limit: 1\n  |  order by: [4410, VARCHAR, false] ASC, [4409, VARCHAR, false] ASC, [4426, DATETIME, true] DESC\n  |  offset: 0\n  |  cardinality: 1\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * op-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * serial_code_key-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * update_time-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  486:Project\n  |  output columns:\n  |  4409 <-> [4409: vivo_source, VARCHAR, false]\n  |  4410 <-> [4410: imei1, VARCHAR, false]\n  |  4426 <-> [4426: update_time, DATETIME, true]\n  |  cardinality: 1\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * update_time-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  485:OlapScanNode\n     table: serialcode_ext, rollup: serialcode_ext\n     preAggregation: on\n     Predicates: 4409: vivo_source IN ('INGJ01', 'vwork_sale_ruichuang'), 4412: op IN ('+I', '+U'), [4428: del_flag, VARCHAR, true] = 'N', [4419: serial_code_key, VARCHAR, true] = 'firstAgentRemark'\n     partitionsRatio=0/1, tabletsRatio=0/0\n     tabletList=\n     actualRows=0, avgRowSize=6.0\n     cardinality: 1\n     column statistics: \n     * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * op-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * serial_code_key-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * update_time-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n\nPLAN FRAGMENT 20(F217)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 482\n\n  481:Project\n  |  output columns:\n  |  4386 <-> [4386: val_china, VARCHAR, true]\n  |  cardinality: 4\n  |  column statistics: \n  |  * val_china-->[-Infinity, Infinity, 0.0, 9.***************, 4.231552162849873] ESTIMATE\n  |  \n  480:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [4399: p_id, LARGEINT, true] = 202203181000000590, 4383: op IN ('+I', '+U'), [4396: delete_flag, TINYINT, true] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=28.734818\n     cardinality: 4\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * val_china-->[-Infinity, Infinity, 0.0, 9.***************, 4.231552162849873] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * p_id-->[2.02203181000000576E17, 2.02203181000000576E17, 0.0, 16.0, 4.231552162849873] ESTIMATE\n\nPLAN FRAGMENT 21(F215)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 4343: imei1, 4342: vivo_source\n  OutPut Exchange Id: 477\n\n  476:Project\n  |  output columns:\n  |  4342 <-> [4342: vivo_source, VARCHAR, false]\n  |  4343 <-> [4343: imei1, VARCHAR, false]\n  |  4353 <-> [4353: serial_code_value, VARCHAR, true]\n  |  cardinality: 1\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * serial_code_value-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  475:OlapScanNode\n     table: serialcode_ext, rollup: serialcode_ext\n     preAggregation: on\n     Predicates: 4342: vivo_source IN ('INGJ01', 'vwork_sale_ruichuang'), 4345: op IN ('+I', '+U'), [4361: del_flag, VARCHAR, true] = 'N', [4352: serial_code_key, VARCHAR, true] = 'Tag'\n     partitionsRatio=0/1, tabletsRatio=0/0\n     tabletList=\n     actualRows=0, avgRowSize=6.0\n     cardinality: 1\n     column statistics: \n     * vivo_source-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * imei1-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * op-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * serial_code_key-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * serial_code_value-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n\nPLAN FRAGMENT 22(F213)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 4301: imei1, 4297: vivo_source\n  OutPut Exchange Id: 468\n\n  467:PARTITION-TOP-N\n  |  partition by: [4301: imei1, VARCHAR, true] , [4297: vivo_source, VARCHAR, false] \n  |  partition limit: 1\n  |  order by: [4301, VARCHAR, true] ASC, [4297, VARCHAR, false] ASC, [4309, DATETIME, true] DESC\n  |  offset: 0\n  |  cardinality: 27023\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.003943769963200337, 23.***************, 27023.***********] ESTIMATE\n  |  * sales_report_type-->[-Infinity, Infinity, 0.0, 4.***************, 2.0] ESTIMATE\n  |  * report_time-->[1.608288282E9, 1.715594211E9, 0.0, 8.0, 27023.***********] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.0, 9.***************, 16006.0] ESTIMATE\n  |  * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 2.0] ESTIMATE\n  |  \n  466:Project\n  |  output columns:\n  |  4297 <-> [4297: vivo_source, VARCHAR, false]\n  |  4301 <-> [4301: imei1, VARCHAR, true]\n  |  4309 <-> [4309: report_time, DATETIME, true]\n  |  4311 <-> [4311: report_user_code, VARCHAR, true]\n  |  cardinality: 27023\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.003943769963200337, 23.***************, 27023.***********] ESTIMATE\n  |  * report_time-->[1.608288282E9, 1.715594211E9, 0.0, 8.0, 27023.***********] ESTIMATE\n  |  * report_user_code-->[-Infinity, Infinity, 0.0, 9.***************, 16006.0] ESTIMATE\n  |  \n  465:OlapScanNode\n     table: serialcode_sales_report_detail, rollup: serialcode_sales_report_detail\n     preAggregation: on\n     Predicates: 4297: vivo_source IN ('INGJ01', 'vwork_sale_ruichuang'), 4299: op IN ('+I', '+U'), [4319: del_flag, VARCHAR, true] = 'N', 4307: sales_report_type IN ('SALE', 'EXCHANGE')\n     partitionsRatio=1/1, tabletsRatio=40/40\n     tabletList=241277,241278,241279,241280,241281,241282,241283,241284,241285,241286 ...\n     actualRows=526892, avgRowSize=54.084953\n     cardinality: 27023\n     column statistics: \n     * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * imei1-->[-Infinity, Infinity, 0.003943769963200337, 23.***************, 27023.***********] ESTIMATE\n     * sales_report_type-->[-Infinity, Infinity, 0.0, 4.***************, 2.0] ESTIMATE\n     * report_time-->[1.608288282E9, 1.715594211E9, 0.0, 8.0, 27023.***********] ESTIMATE\n     * report_user_code-->[-Infinity, Infinity, 0.0, 9.***************, 16006.0] ESTIMATE\n     * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 23(F00)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3764: imei1, 3785: vivo_source\n  OutPut Exchange Id: 464\n\n  463:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3773 <-> [3773: superior_id, LARGEINT, true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 6217309.*********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  \n  462:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3774: customer_account_code, VARCHAR(128), true] = [4236: number, VARCHAR, true]\n  |  output columns: 3764, 3773, 3785, 3790, 3913, 4071\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 6217309.*********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 9.91571641050901E-4, 2.0, 2.0] ESTIMATE\n  |  * number-->[-Infinity, Infinity, 0.0019821600677826304, 12.***************, 40340.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 9.91571641050901E-4, 1.0, 2.0] ESTIMATE\n  |  \n  |----461:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 40340\n  |    \n  458:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3773 <-> [3773: superior_id, LARGEINT, true]\n  |  3774 <-> [3774: customer_account_code, VARCHAR(128), true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 6217309.*********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  \n  457:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3770: sales_area_id, LARGEINT, true] = [5050: cast, LARGEINT, true]\n  |  output columns: 3764, 3773, 3774, 3785, 3790, 3913, 4071\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 6217309.*********] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 1.3193684805235233E-16, 8.0, 172063.0] ESTIMATE\n  |  * deleted_flag-->[0.0, 0.0, 1.3193684805235233E-16, 4.0, 1.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 1.3193684805235233E-16, 2.0, 2.0] ESTIMATE\n  |  * cast-->[2.01910110000001E14, 2.0240422000047728E17, 1.3193684805235233E-16, 16.0, 172063.0] ESTIMATE\n  |  \n  |----456:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 172063\n  |    \n  453:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3770 <-> [3770: sales_area_id, LARGEINT, true]\n  |  3773 <-> [3773: superior_id, LARGEINT, true]\n  |  3774 <-> [3774: customer_account_code, VARCHAR(128), true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 6217309.*********] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  \n  452:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3769: cast, VARCHAR(65533), true] = [4038: customer_code, VARCHAR, true]\n  |  output columns: 3764, 3770, 3773, 3774, 3785, 3790, 3913, 4071\n  |  can local shuffle: false\n  |  cardinality: ************\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 6217309.*********] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  \n  |----451:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 229990\n  |    \n  428:Project\n  |  output columns:\n  |  3764 <-> [3764: imei1, VARCHAR(127), true]\n  |  3769 <-> [3769: cast, VARCHAR(65533), true]\n  |  3770 <-> [3770: sales_area_id, LARGEINT, true]\n  |  3773 <-> [3773: superior_id, LARGEINT, true]\n  |  3774 <-> [3774: customer_account_code, VARCHAR(128), true]\n  |  3785 <-> [3785: vivo_source, VARCHAR(128), true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ***********\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 6217309.*********] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 4.820541606740809E-8, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  427:HASH JOIN\n  |  join op: INNER JOIN (BROADCAST)\n  |  equal join conjunct: [3775: sku_code, VARCHAR(128), true] = [3794: sku_code, VARCHAR, true]\n  |  build runtime filters:\n  |  - filter_id = 36, build_expr = (3794: sku_code), remote = true\n  |  output columns: 3764, 3769, 3770, 3773, 3774, 3785, 3790, 3913\n  |  can local shuffle: false\n  |  cardinality: ***********\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 6217309.*********] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 4.820541606740809E-8, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 1.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----426:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 2956\n  |    \n  0:UNION\n  |  output exprs:\n  |      [3764, VARCHAR(127), true] | [3769, VARCHAR(65533), true] | [3770, LARGEINT, true] | [3773, LARGEINT, true] | [3774, VARCHAR(128), true] | [3775, VARCHAR(128), true] | [3785, VARCHAR(128), true]\n  |  child exprs:\n  |      [908: imei1, VARCHAR(127), true] | [1240: cast, VARCHAR(65533), true] | [1249: sales_area_id, LARGEINT, true] | [946: superior_id, LARGEINT, true] | [914: customer_account_code, VARCHAR(128), true] | [916: sku_code, VARCHAR(128), true] | [906: vivo_source, VARCHAR(128), true]\n  |      [2466: imei1, VARCHAR(127), true] | [2808: cast, VARCHAR(65533), true] | [2817: sales_area_id, LARGEINT, true] | [2504: superior_id, LARGEINT, true] | [2472: customer_account_code, VARCHAR(128), true] | [2474: sku_code, VARCHAR(128), true] | [2464: vivo_source, VARCHAR(128), true]\n  |  pass-through-operands: all\n  |  hasNullableGenerateChild: true\n  |  cardinality: ***********\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 6217309.*********] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 4.820541606740809E-8, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.000************, 123837.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 586.0] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.046070450443945206, 2.0] ESTIMATE\n  |  \n  |----382:EXCHANGE\n  |       cardinality: ***********\n  |       probe runtime filters:\n  |       - filter_id = 36, probe_expr = (2474: sku_code)\n  |    \n  199:EXCHANGE\n     cardinality: 6221692\n     probe runtime filters:\n     - filter_id = 36, probe_expr = (916: sku_code)\n\nPLAN FRAGMENT 24(F210)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 461\n\n  460:Project\n  |  output columns:\n  |  4236 <-> [4236: number, VARCHAR, true]\n  |  cardinality: 40340\n  |  column statistics: \n  |  * number-->[-Infinity, Infinity, 9.915716410510659E-4, 12.***************, 40340.0] ESTIMATE\n  |  \n  459:OlapScanNode\n     table: account, rollup: account\n     preAggregation: on\n     Predicates: 4234: op IN ('+I', '+U'), [4272: delete_flag, TINYINT, true] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=196800\n     actualRows=121095, avgRowSize=15.083342\n     cardinality: 40340\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * number-->[-Infinity, Infinity, 9.915716410510659E-4, 12.***************, 40340.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 25(F208)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 456\n\n  455:Project\n  |  output columns:\n  |  5050 <-> cast([4184: sales_area_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 172063\n  |  column statistics: \n  |  * cast-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 172063.0] ESTIMATE\n  |  \n  454:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 4194: op IN ('+I', '+U'), [4193: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=30.0\n     cardinality: 172063\n     column statistics: \n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 8.0, 172063.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * cast-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 172063.0] ESTIMATE\n\nPLAN FRAGMENT 26(F198)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 451\n\n  450:Project\n  |  output columns:\n  |  4038 <-> [4038: customer_code, VARCHAR, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  \n  449:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5051: cast, VARCHAR(50), true] = [5052: cast, VARCHAR(50), true]\n  |  output columns: 4038, 4071\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.999995651984869, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.999995651984869, 1.0, 2.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  |----448:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  445:Project\n  |  output columns:\n  |  4038 <-> [4038: customer_code, VARCHAR, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  5051 <-> cast([4082: district, BIGINT, true] as VARCHAR(50))\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  \n  444:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5053: cast, VARCHAR(50), true] = [5054: cast, VARCHAR(50), true]\n  |  output columns: 4038, 4071, 4082\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.999995651984869, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.999995651984869, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  |----443:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  440:Project\n  |  output columns:\n  |  4038 <-> [4038: customer_code, VARCHAR, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  4082 <-> [4082: district, BIGINT, true]\n  |  5053 <-> cast([4081: city, BIGINT, true] as VARCHAR(50))\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  \n  439:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5055: cast, VARCHAR(50), true] = [5056: cast, VARCHAR(50), true]\n  |  output columns: 4038, 4071, 4081, 4082\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 1.0] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.999995651984869, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.999995651984869, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  |----438:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  435:Project\n  |  output columns:\n  |  4038 <-> [4038: customer_code, VARCHAR, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  4081 <-> [4081: city, BIGINT, true]\n  |  4082 <-> [4082: district, BIGINT, true]\n  |  5055 <-> cast([4080: province, BIGINT, true] as VARCHAR(50))\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 173304.50986246436] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 103911.0] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  \n  434:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5057: cast, LARGEINT, false] = [4099: master_branch_id, LARGEINT, true]\n  |  output columns: 4038, 4071, 4080, 4081, 4082\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 173304.50986246436] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 103911.0] ESTIMATE\n  |  * province-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.2464693688314085, 2.0, 2.0] ESTIMATE\n  |  * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.37992224485821285, 16.0, 209577.4444444444] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.3798982750195003, 1.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.2464693688314085, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 173304.50986246436] ESTIMATE\n  |  \n  |----433:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 209577\n  |    \n  430:Project\n  |  output columns:\n  |  4038 <-> [4038: customer_code, VARCHAR, true]\n  |  4071 <-> [4071: retailer_code, VARCHAR, true]\n  |  4080 <-> [4080: province, BIGINT, true]\n  |  4081 <-> [4081: city, BIGINT, true]\n  |  4082 <-> [4082: district, BIGINT, true]\n  |  5057 <-> cast([4036: customer_id, BIGINT, false] as LARGEINT)\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 229990.0] ESTIMATE\n  |  * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 103911.0] ESTIMATE\n  |  * province-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 228870.0] ESTIMATE\n  |  \n  429:OlapScanNode\n     table: dim_customer_chain, rollup: dim_customer_chain\n     preAggregation: on\n     Predicates: 4085: op IN ('+I', '+U'), 4041: customer_status IN (3, 6), [4086: delete_flag, TINYINT, true] = 0\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=243024,243025,243026,243027,243028\n     actualRows=239125, avgRowSize=70.23687\n     cardinality: 229990\n     column statistics: \n     * customer_id-->[10001.0, 2.02404220000479872E17, 0.0, 8.0, 228870.0] ESTIMATE\n     * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 229990.0] ESTIMATE\n     * customer_status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * retailer_code-->[-Infinity, Infinity, 0.016296360711335275, 9.***************, 103911.0] ESTIMATE\n     * province-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n     * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n     * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 228870.0] ESTIMATE\n\nPLAN FRAGMENT 27(F205)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 448\n\n  447:Project\n  |  output columns:\n  |  5052 <-> cast([4157: id, LARGEINT, false] as VARCHAR(50))\n  |  cardinality: 1109\n  |  column statistics: \n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  446:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [4171: delete_flag, TINYINT, true] = 0, 4158: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=35.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n\nPLAN FRAGMENT 28(F203)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 443\n\n  442:Project\n  |  output columns:\n  |  5054 <-> cast([4131: id, LARGEINT, false] as VARCHAR(50))\n  |  cardinality: 1109\n  |  column statistics: \n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  441:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [4145: delete_flag, TINYINT, true] = 0, 4132: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=35.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n\nPLAN FRAGMENT 29(F201)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 438\n\n  437:Project\n  |  output columns:\n  |  5056 <-> cast([4105: id, LARGEINT, false] as VARCHAR(50))\n  |  cardinality: 1109\n  |  column statistics: \n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  436:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [4119: delete_flag, TINYINT, true] = 0, 4106: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=35.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n\nPLAN FRAGMENT 30(F199)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 433\n\n  432:Project\n  |  output columns:\n  |  4099 <-> [4099: master_branch_id, LARGEINT, true]\n  |  cardinality: 209577\n  |  column statistics: \n  |  * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.17710345207844144, 16.0, 209577.4444444444] ESTIMATE\n  |  \n  431:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [4104: delete_flag, TINYINT, false] = 0, 4103: status IN (3, 6), 4097: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=20.0\n     cardinality: 209577\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.17710345207844144, 16.0, 209577.4444444444] ESTIMATE\n     * status-->[3.0, 6.0, 0.17707164203951126, 1.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 31(F190)\n\n  Input Partition: HASH_PARTITIONED: 3909: master_id\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 426\n\n  425:Project\n  |  output columns:\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3794 <-> [3794: sku_code, VARCHAR, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 2956\n  |  column statistics: \n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  424:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3913: signal_system, LARGEINT, true] = [4009: id, LARGEINT, true]\n  |  output columns: 3790, 3794, 3913\n  |  can local shuffle: false\n  |  cardinality: 2956\n  |  column statistics: \n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.9669000086422953, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.9669000086422953, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.9669000086422953, 1.0, 2.0] ESTIMATE\n  |  \n  |----423:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  420:Project\n  |  output columns:\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3794 <-> [3794: sku_code, VARCHAR, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 2956\n  |  column statistics: \n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.****************, 16.0, 1.0] ESTIMATE\n  |  \n  419:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3800: marketing_id, LARGEINT, true] = [3992: id, LARGEINT, true]\n  |  output columns: 3790, 3794, 3913\n  |  can local shuffle: false\n  |  cardinality: 2956\n  |  column statistics: \n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 1.0] ESTIMATE\n  |  * marketing_id-->[2.0210115000012E17, 3.5210115000012211E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.****************, 16.0, 1.0] ESTIMATE\n  |  * id-->[2.0210115000012E17, 3.5210115000012211E17, 0.9996617050067659, 16.0, 137.33333333333331] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.9996617050067659, 2.0, 2.0] ESTIMATE\n  |  * deleted_flag-->[0.0, 0.0, 0.9996617050067659, 1.0, 2.0] ESTIMATE\n  |  \n  |----418:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 137\n  |    \n  415:Project\n  |  output columns:\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3794 <-> [3794: sku_code, VARCHAR, true]\n  |  3800 <-> [3800: marketing_id, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 2956\n  |  column statistics: \n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 36.577877560192405] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.****************, 16.0, 2.0] ESTIMATE\n  |  \n  414:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3919: model_ver_series_id, LARGEINT, true] = [3975: id, LARGEINT, true]\n  |  output columns: 3790, 3794, 3800, 3913\n  |  can local shuffle: false\n  |  cardinality: 2956\n  |  column statistics: \n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 36.577877560192405] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.****************, 16.0, 2.0] ESTIMATE\n  |  * model_ver_series_id-->[2.0210115000012E17, 2.024041100002328E17, 0.0, 16.0, 36.577877560192405] ESTIMATE\n  |  * id-->[2.0210115000012E17, 3.5210115000012211E17, 0.9876258871582569, 16.0, 137.33333333333331] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.9876258871582569, 2.0, 2.0] ESTIMATE\n  |  * deleted_flag-->[0.0, 0.0, 0.9876258871582569, 1.0, 2.0] ESTIMATE\n  |  \n  |----413:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 137\n  |    \n  410:Project\n  |  output columns:\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3794 <-> [3794: sku_code, VARCHAR, true]\n  |  3800 <-> [3800: marketing_id, LARGEINT, true]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  3919 <-> [3919: model_ver_series_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 2956\n  |  column statistics: \n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 739.0] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.****************, 16.0, 2.0] ESTIMATE\n  |  * model_ver_series_id-->[1.0, 2.024041100002328E17, 0.****************, 16.0, 411.0] ESTIMATE\n  |  \n  409:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [3909: master_id, LARGEINT, true] = [3787: id, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 35, build_expr = (3787: id), remote = true\n  |  output columns: 3790, 3794, 3800, 3913, 3919\n  |  can local shuffle: false\n  |  cardinality: 2956\n  |  column statistics: \n  |  * id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 739.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 739.0] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  * master_id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 739.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.****************, 16.0, 2.0] ESTIMATE\n  |  * model_ver_series_id-->[1.0, 2.024041100002328E17, 0.****************, 16.0, 411.0] ESTIMATE\n  |  \n  |----408:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [3787: id, LARGEINT, true]\n  |       cardinality: 2956\n  |    \n  385:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [3909: master_id, LARGEINT, true]\n     cardinality: 11571\n\nPLAN FRAGMENT 32(F195)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 423\n\n  422:Project\n  |  output columns:\n  |  4009 <-> [4009: id, LARGEINT, false]\n  |  cardinality: 1109\n  |  column statistics: \n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  421:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [4023: delete_flag, TINYINT, true] = 0, 4010: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=19.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 33(F193)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 418\n\n  417:Project\n  |  output columns:\n  |  3992 <-> [3992: id, LARGEINT, false]\n  |  cardinality: 137\n  |  column statistics: \n  |  * id-->[2.0210115000012E17, 3.5210115000012211E17, 0.0, 16.0, 137.33333333333331] ESTIMATE\n  |  \n  416:OlapScanNode\n     table: product_tree, rollup: product_tree\n     preAggregation: on\n     Predicates: 3993: op IN ('+I', '+U'), [4004: deleted_flag, TINYINT, true] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16101\n     actualRows=412, avgRowSize=19.0\n     cardinality: 137\n     column statistics: \n     * id-->[2.0210115000012E17, 3.5210115000012211E17, 0.0, 16.0, 137.33333333333331] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 34(F191)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 413\n\n  412:Project\n  |  output columns:\n  |  3975 <-> [3975: id, LARGEINT, false]\n  |  cardinality: 137\n  |  column statistics: \n  |  * id-->[2.0210115000012E17, 3.5210115000012211E17, 0.0, 16.0, 137.33333333333331] ESTIMATE\n  |  \n  411:OlapScanNode\n     table: product_tree, rollup: product_tree\n     preAggregation: on\n     Predicates: 3976: op IN ('+I', '+U'), [3987: deleted_flag, TINYINT, true] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16101\n     actualRows=412, avgRowSize=19.0\n     cardinality: 137\n     column statistics: \n     * id-->[2.0210115000012E17, 3.5210115000012211E17, 0.0, 16.0, 137.33333333333331] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 35(F180)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3787: id\n  OutPut Exchange Id: 408\n\n  407:Project\n  |  output columns:\n  |  3787 <-> [3787: id, LARGEINT, true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3794 <-> [3794: sku_code, VARCHAR, true]\n  |  3800 <-> [3800: marketing_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 2956\n  |  column statistics: \n  |  * id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 739.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 739.0] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  \n  406:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3791: brand, LARGEINT, true] = [3881: id, LARGEINT, true]\n  |  output columns: 3787, 3790, 3794, 3800\n  |  can local shuffle: false\n  |  cardinality: 2956\n  |  column statistics: \n  |  * id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 739.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * brand-->[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 3.0036122817579773] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 739.0] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  * id-->[2.0190926000000212E16, 2.02112300116786592E17, 0.6245484647802528, 16.0, 3.0036122817579773] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.6245484647802528, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.6245484647802528, 1.0, 2.0] ESTIMATE\n  |  \n  |----405:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 3\n  |    \n  402:Project\n  |  output columns:\n  |  3787 <-> [3787: id, LARGEINT, true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3791 <-> [3791: brand, LARGEINT, true]\n  |  3794 <-> [3794: sku_code, VARCHAR, true]\n  |  3800 <-> [3800: marketing_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 2956\n  |  column statistics: \n  |  * id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 739.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * brand-->[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 8.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 739.0] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  \n  401:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3790: category, LARGEINT, true] = [3855: id, LARGEINT, true]\n  |  output columns: 3787, 3790, 3791, 3794, 3800\n  |  can local shuffle: false\n  |  cardinality: 2956\n  |  column statistics: \n  |  * id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 739.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  * brand-->[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 8.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 739.0] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  * id-->[2.01909260000002E16, 2.01909260000002E16, 0.75, 16.0, 1.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.75, 2.0, 1.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.75, 1.0, 1.0] ESTIMATE\n  |  \n  |----400:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1\n  |    \n  397:Project\n  |  output columns:\n  |  3787 <-> [3787: id, LARGEINT, true]\n  |  3790 <-> [3790: category, LARGEINT, true]\n  |  3791 <-> [3791: brand, LARGEINT, true]\n  |  3794 <-> [3794: sku_code, VARCHAR, true]\n  |  3800 <-> [3800: marketing_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 2956\n  |  column statistics: \n  |  * id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 1925.7730158730155] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 4.0] ESTIMATE\n  |  * brand-->[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 8.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 1925.7730158730155] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  \n  396:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3797: product_series, LARGEINT, true] = [3838: id, LARGEINT, true]\n  |  output columns: 3787, 3790, 3791, 3794, 3800\n  |  can local shuffle: false\n  |  cardinality: 2956\n  |  column statistics: \n  |  * id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 1925.7730158730155] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 4.0] ESTIMATE\n  |  * brand-->[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 8.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 1925.7730158730155] ESTIMATE\n  |  * product_series-->[2.0210115000012E17, 2.02404150000425472E17, 0.0, 16.0, 67.0] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  * id-->[2.0210115000012E17, 2.02404150000425472E17, 0.0, 16.0, 67.0] ESTIMATE\n  |  \n  |----395:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 137\n  |    \n  392:Project\n  |  output columns:\n  |  3787 <-> [3787: id, LARGEINT, false]\n  |  3790 <-> [3790: category, LARGEINT, false]\n  |  3791 <-> [3791: brand, LARGEINT, false]\n  |  3794 <-> [3794: sku_code, VARCHAR, false]\n  |  3797 <-> [3797: product_series, LARGEINT, false]\n  |  3800 <-> [3800: marketing_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 2956\n  |  column statistics: \n  |  * id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 1925.7730158730155] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 4.0] ESTIMATE\n  |  * brand-->[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 8.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 1925.7730158730155] ESTIMATE\n  |  * product_series-->[2.0210115000012E17, 2.02404150000425472E17, 0.0, 16.0, 67.0] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  \n  391:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [3798: model_series, LARGEINT, true] = [3821: id, LARGEINT, true]\n  |  output columns: 3787, 3790, 3791, 3794, 3797, 3800\n  |  can local shuffle: false\n  |  cardinality: 2956\n  |  column statistics: \n  |  * id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 1925.7730158730155] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 4.0] ESTIMATE\n  |  * brand-->[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 8.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 1925.7730158730155] ESTIMATE\n  |  * product_series-->[2.0210115000012E17, 2.02404150000425472E17, 0.0, 16.0, 67.0] ESTIMATE\n  |  * model_series-->[2.0210115000012E17, 2.02404110000232768E17, 0.0, 16.0, 137.33333333333331] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  * id-->[2.0210115000012E17, 3.5210115000012211E17, 0.3485206306248256, 16.0, 137.33333333333331] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.3485206306248256, 2.0, 2.0] ESTIMATE\n  |  * deleted_flag-->[0.0, 0.0, 0.3485206306248256, 1.0, 2.0] ESTIMATE\n  |  \n  |----390:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 137\n  |    \n  387:Project\n  |  output columns:\n  |  3787 <-> [3787: id, LARGEINT, false]\n  |  3790 <-> [3790: category, LARGEINT, false]\n  |  3791 <-> [3791: brand, LARGEINT, false]\n  |  3794 <-> [3794: sku_code, VARCHAR, false]\n  |  3797 <-> [3797: product_series, LARGEINT, false]\n  |  3798 <-> [3798: model_series, LARGEINT, true]\n  |  3800 <-> [3800: marketing_id, LARGEINT, true]\n  |  cardinality: 2956\n  |  column statistics: \n  |  * id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 2956.0] ESTIMATE\n  |  * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 4.0] ESTIMATE\n  |  * brand-->[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 8.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 2956.0] ESTIMATE\n  |  * product_series-->[2.0210115000012E17, 2.02404150000425472E17, 0.0, 16.0, 67.0] ESTIMATE\n  |  * model_series-->[2.0210115000012E17, 2.02404110000232768E17, 0.0038058186738836267, 16.0, 210.0] ESTIMATE\n  |  * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n  |  \n  386:OlapScanNode\n     table: product, rollup: product\n     preAggregation: on\n     Predicates: 3791: brand IN (*****************, 202112300116786598, *****************, 202103183007929182, *****************, 202009090000052432, 202009090000052435, 202009090000052453, *****************), [3790: category, LARGEINT, false] = *****************, [3811: deleted_flag, TINYINT, true] = 0, 3788: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16747\n     actualRows=23648, avgRowSize=103.389885\n     cardinality: 2956\n     column statistics: \n     * id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 2956.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * category-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 4.0] ESTIMATE\n     * brand-->[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 8.0] ESTIMATE\n     * sku_code-->[-Infinity, Infinity, 0.0, 4.*************, 2956.0] ESTIMATE\n     * product_series-->[2.0210115000012E17, 2.02404150000425472E17, 0.0, 16.0, 67.0] ESTIMATE\n     * model_series-->[2.0210115000012E17, 2.02404110000232768E17, 0.0038058186738836267, 16.0, 210.0] ESTIMATE\n     * marketing_id-->[-Infinity, Infinity, 1.0, 16.0, 1.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 36(F187)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 405\n\n  404:Project\n  |  output columns:\n  |  3881 <-> [3881: id, LARGEINT, false]\n  |  cardinality: 3\n  |  column statistics: \n  |  * id-->[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 3.0036122817579773] ESTIMATE\n  |  \n  403:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: 3881: id IN (*****************, 202112300116786598, *****************, 202103183007929182, *****************, 202009090000052432, 202009090000052435, 202009090000052453, *****************), [3895: delete_flag, TINYINT, true] = 0, 3882: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=19.0\n     cardinality: 3\n     column statistics: \n     * id-->[2.0190926000000212E16, 2.02112300116786592E17, 0.0, 16.0, 3.0036122817579773] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 37(F185)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 400\n\n  399:Project\n  |  output columns:\n  |  3855 <-> [3855: id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * id-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  398:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [3855: id, LARGEINT, false] = *****************, [3869: delete_flag, TINYINT, true] = 0, 3856: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=19.0\n     cardinality: 1\n     column statistics: \n     * id-->[2.01909260000002E16, 2.01909260000002E16, 0.0, 16.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 38(F183)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 395\n\n  394:Project\n  |  output columns:\n  |  3838 <-> [3838: id, LARGEINT, false]\n  |  cardinality: 137\n  |  column statistics: \n  |  * id-->[2.0210115000012E17, 3.5210115000012211E17, 0.0, 16.0, 137.33333333333331] ESTIMATE\n  |  \n  393:OlapScanNode\n     table: product_tree, rollup: product_tree\n     preAggregation: on\n     Predicates: [3850: deleted_flag, TINYINT, true] = 0, 3839: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16101\n     actualRows=412, avgRowSize=19.0\n     cardinality: 137\n     column statistics: \n     * id-->[2.0210115000012E17, 3.5210115000012211E17, 0.0, 16.0, 137.33333333333331] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 39(F181)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 390\n\n  389:Project\n  |  output columns:\n  |  3821 <-> [3821: id, LARGEINT, false]\n  |  cardinality: 137\n  |  column statistics: \n  |  * id-->[2.0210115000012E17, 3.5210115000012211E17, 0.0, 16.0, 137.33333333333331] ESTIMATE\n  |  \n  388:OlapScanNode\n     table: product_tree, rollup: product_tree\n     preAggregation: on\n     Predicates: [3833: deleted_flag, TINYINT, true] = 0, 3822: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16101\n     actualRows=412, avgRowSize=19.0\n     cardinality: 137\n     column statistics: \n     * id-->[2.0210115000012E17, 3.5210115000012211E17, 0.0, 16.0, 137.33333333333331] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 40(F178)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3909: master_id\n  OutPut Exchange Id: 385\n\n  384:Project\n  |  output columns:\n  |  3909 <-> [3909: master_id, LARGEINT, false]\n  |  3913 <-> [3913: signal_system, LARGEINT, true]\n  |  3919 <-> [3919: model_ver_series_id, LARGEINT, true]\n  |  cardinality: 11571\n  |  column statistics: \n  |  * master_id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 11571.0] ESTIMATE\n  |  * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.****************, 16.0, 2.0] ESTIMATE\n  |  * model_ver_series_id-->[1.0, 2.024041100002328E17, 0.****************, 16.0, 411.0] ESTIMATE\n  |  \n  383:OlapScanNode\n     table: product_exp, rollup: product_exp\n     preAggregation: on\n     Predicates: [3935: deleted_flag, TINYINT, true] = 0, 3908: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16568\n     actualRows=23142, avgRowSize=51.0\n     cardinality: 11571\n     probe runtime filters:\n     - filter_id = 35, probe_expr = (3909: master_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * master_id-->[2.02101170000058176E17, 2.02404200000240064E17, 0.0, 16.0, 11571.0] ESTIMATE\n     * signal_system-->[2.0191012000005492E16, 2.0191218000005496E16, 0.****************, 16.0, 2.0] ESTIMATE\n     * model_ver_series_id-->[1.0, 2.024041100002328E17, 0.****************, 16.0, 411.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 41(F93)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 382\n\n  381:Project\n  |  output columns:\n  |  2464 <-> [2464: vivo_source, VARCHAR(128), true]\n  |  2466 <-> [2466: imei1, VARCHAR(127), true]\n  |  2472 <-> [2472: customer_account_code, VARCHAR(128), true]\n  |  2474 <-> [2474: sku_code, VARCHAR(128), true]\n  |  2504 <-> [2504: superior_id, LARGEINT, true]\n  |  2808 <-> [2808: cast, VARCHAR(65533), true]\n  |  2817 <-> [2817: sales_area_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ***********\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.*****************, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 1243461.**********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 0.0, 9.***************, 24.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  \n  380:HASH JOIN\n  |  join op: INNER JOIN (BROADCAST)\n  |  equal join conjunct: [2817: sales_area_id, LARGEINT, true] = [3013: branch_id, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 34, build_expr = (3013: branch_id), remote = true\n  |  output columns: 2464, 2466, 2472, 2474, 2504, 2808, 2817\n  |  can local shuffle: false\n  |  cardinality: ***********\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.*****************, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 1243461.**********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 0.0, 9.***************, 24.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  \n  |----379:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 170946\n  |    \n  268:Project\n  |  output columns:\n  |  2464 <-> [2464: vivo_source, VARCHAR(128), true]\n  |  2466 <-> [2466: imei1, VARCHAR(127), true]\n  |  2472 <-> [2472: customer_account_code, VARCHAR(128), true]\n  |  2474 <-> [2474: sku_code, VARCHAR(128), true]\n  |  2504 <-> [2504: superior_id, LARGEINT, true]\n  |  2808 <-> [2808: cast, VARCHAR(65533), true]\n  |  2817 <-> [2817: sales_area_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: ***********\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.*****************, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 1243461.**********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 0.0, 9.***************, 24.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  \n  267:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [2533: agent1_code, VARCHAR, true] = [2866: customer_code, VARCHAR, true]\n  |  output columns: 2464, 2466, 2472, 2474, 2504, 2808, 2817\n  |  can local shuffle: false\n  |  cardinality: ***********\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.*****************, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 1243461.**********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n  |  * agent1_code-->[-Infinity, Infinity, 0.0, 5.***************, 1.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 0.0, 9.***************, 24.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  \n  |----266:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 229990\n  |    \n  243:HASH JOIN\n  |  join op: INNER JOIN (BROADCAST)\n  |  equal join conjunct: [2533: agent1_code, VARCHAR, true] = [2808: cast, VARCHAR(65533), true]\n  |  build runtime filters:\n  |  - filter_id = 18, build_expr = (2808: cast), remote = false\n  |  can local shuffle: false\n  |  cardinality: 1357489\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.*****************, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 1243461.**********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n  |  * agent1_code-->[-Infinity, Infinity, 0.0, 5.***************, 24.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 0.0, 9.***************, 24.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  \n  |----242:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 141547\n  |       probe runtime filters:\n  |       - filter_id = 34, probe_expr = (2817: sales_area_id)\n  |    \n  221:Project\n  |  output columns:\n  |  2464 <-> [2464: vivo_source, VARCHAR(128), true]\n  |  2466 <-> [2466: imei1, VARCHAR(127), true]\n  |  2472 <-> [2472: customer_account_code, VARCHAR(128), true]\n  |  2474 <-> [2474: sku_code, VARCHAR(128), true]\n  |  2504 <-> [2504: superior_id, LARGEINT, true]\n  |  2533 <-> [2533: agent1_code, VARCHAR, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1252226\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.*****************, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 1243461.**********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n  |  * agent1_code-->[-Infinity, Infinity, 0.0, 5.***************, 24.0] ESTIMATE\n  |  \n  220:HASH JOIN\n  |  join op: INNER JOIN (BROADCAST)\n  |  equal join conjunct: [2470: first_agent_code, VARCHAR(128), true] = [2537: erp_code, VARCHAR, true]\n  |  build runtime filters:\n  |  - filter_id = 16, build_expr = (2537: erp_code), remote = true\n  |  output columns: 2464, 2466, 2472, 2474, 2504, 2533\n  |  can local shuffle: false\n  |  cardinality: 1252226\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.*****************, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 1243461.**********] ESTIMATE\n  |  * first_agent_code-->[-Infinity, Infinity, 0.0, 0.*****************, 23.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n  |  * agent1_code-->[-Infinity, Infinity, 0.0, 5.***************, 24.0] ESTIMATE\n  |  * erp_code-->[-Infinity, Infinity, 0.0, 6.25, 23.0] ESTIMATE\n  |  \n  |----219:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 24\n  |       probe runtime filters:\n  |       - filter_id = 18, probe_expr = (2533: agent1_code)\n  |    \n  217:Project\n  |  output columns:\n  |  2464 <-> [2464: vivo_source, VARCHAR(128), true]\n  |  2466 <-> [2466: imei1, VARCHAR(127), true]\n  |  2470 <-> [2470: first_agent_code, VARCHAR(128), true]\n  |  2472 <-> [2472: customer_account_code, VARCHAR(128), true]\n  |  2474 <-> [2474: sku_code, VARCHAR(128), true]\n  |  2504 <-> [2504: superior_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1252226\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.*****************, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 1243461.**********] ESTIMATE\n  |  * first_agent_code-->[-Infinity, Infinity, 0.0, 0.*****************, 23.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n  |  \n  216:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [2473: customer_warehouse_code, VARCHAR(128), true] = [2496: warehouse_code, VARCHAR, true]\n  |  output columns: 2464, 2466, 2470, 2472, 2474, 2504\n  |  can local shuffle: false\n  |  cardinality: 1252226\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.*****************, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 1243461.**********] ESTIMATE\n  |  * first_agent_code-->[-Infinity, Infinity, 0.0, 0.*****************, 23.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 1.****************, 18343.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 586.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 4.029215843879362E-6, 2.0, 2.0] ESTIMATE\n  |  * warehouse_code-->[-Infinity, Infinity, 8.058415453384509E-6, 11.***************, 220610.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n  |  * status-->[3.0, 6.0, 4.029215843879362E-6, 1.0, 2.0] ESTIMATE\n  |  * deleted_flag-->[0.0, 0.0, 4.029215843879362E-6, 1.0, 2.0] ESTIMATE\n  |  \n  |----215:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 220611\n  |    \n  200:UNION\n  |  output exprs:\n  |      [2464, VARCHAR(128), true] | [2466, VARCHAR(127), true] | [2470, VARCHAR(128), true] | [2472, VARCHAR(128), true] | [2473, VARCHAR(128), true] | [2474, VARCHAR(128), true]\n  |  child exprs:\n  |      [2312: vivo_source, VARCHAR, false] | [2311: imei1, VARCHAR, false] | [2319: first_agent_code, VARCHAR, true] | [2321: customer_account_code, VARCHAR, true] | [2322: customer_warehouse_code, VARCHAR, true] | [2323: sku_code, VARCHAR, true]\n  |      [2361: vivo_source, VARCHAR, true] | [2360: imei1, VARCHAR, true] | [2369: first_agent_code, VARCHAR, true] | [2371: customer_account_code, VARCHAR, true] | [2372: customer_warehouse_code, VARCHAR, true] | [2373: sku_code, VARCHAR, true]\n  |  pass-through-operands: all\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1252226\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.*****************, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 1243461.**********] ESTIMATE\n  |  * first_agent_code-->[-Infinity, Infinity, 0.0, 0.*****************, 23.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 1.****************, 18343.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.*****************, 586.0] ESTIMATE\n  |  \n  |----212:EXCHANGE\n  |       partition exprs: [2360: imei1, VARCHAR, false], [2361: vivo_source, VARCHAR, false]\n  |       cardinality: 8764\n  |       probe runtime filters:\n  |       - filter_id = 16, probe_expr = (2369: first_agent_code)\n  |    \n  203:EXCHANGE\n     cardinality: 1243462\n     probe runtime filters:\n     - filter_id = 16, probe_expr = (2319: first_agent_code)\n\nPLAN FRAGMENT 42(F176)\n\n  Input Partition: HASH_PARTITIONED: 3013: branch_id, 3029: status\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 379\n\n  378:AGGREGATE (merge finalize)\n  |  group by: [3013: branch_id, LARGEINT, true], [3029: status, TINYINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 170946.***********] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  \n  377:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [3013: branch_id, LARGEINT, true], [3029: status, TINYINT, true]\n     cardinality: 170946\n\nPLAN FRAGMENT 43(F123)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3013: branch_id, 3029: status\n  OutPut Exchange Id: 377\n\n  376:AGGREGATE (update serialize)\n  |  STREAMING\n  |  group by: [3013: branch_id, LARGEINT, true], [3029: status, TINYINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 170946.***********] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  \n  375:Project\n  |  output columns:\n  |  3013 <-> [3013: branch_id, LARGEINT, true]\n  |  3029 <-> [3029: status, TINYINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 170946.***********] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  \n  374:HASH JOIN\n  |  join op: LEFT SEMI JOIN (BROADCAST)\n  |  equal join conjunct: [3032: path, VARCHAR, true] = [3760: sales_area_path, VARCHAR(200), true]\n  |  build runtime filters:\n  |  - filter_id = 33, build_expr = (3760: sales_area_path), remote = false\n  |  output columns: 3013, 3029\n  |  can local shuffle: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 170946.***********] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  * path-->[-Infinity, Infinity, 0.0, 17.***************, 170946.***********] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163323, 170946.***********] ESTIMATE\n  |  \n  |----373:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 170946\n  |    \n  270:Project\n  |  output columns:\n  |  3013 <-> [3013: branch_id, LARGEINT, true]\n  |  3029 <-> [3029: status, TINYINT, true]\n  |  3032 <-> [3032: path, VARCHAR, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  * path-->[-Infinity, Infinity, 0.0, 17.***************, 185512.5] ESTIMATE\n  |  \n  269:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 3013: branch_id IS NOT NULL, 3029: status IN (3, 6), [3030: deleted_flag, TINYINT, true] = 0, 3012: op IN ('+U', '+I')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=37.510532\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 33, probe_expr = (3032: path)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * path-->[-Infinity, Infinity, 0.0, 17.***************, 185512.5] ESTIMATE\n\nPLAN FRAGMENT 44(F174)\n\n  Input Partition: HASH_PARTITIONED: 3760: sales_area_path\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 373\n\n  372:AGGREGATE (merge finalize)\n  |  group by: [3760: sales_area_path, VARCHAR(200), true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163323, 170946.***********] ESTIMATE\n  |  \n  371:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [3760: sales_area_path, VARCHAR(200), true]\n     cardinality: 170946\n\nPLAN FRAGMENT 45(F124)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3760: sales_area_path\n  OutPut Exchange Id: 371\n\n  370:AGGREGATE (update serialize)\n  |  STREAMING\n  |  group by: [3760: sales_area_path, VARCHAR(200), true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163323, 170946.***********] ESTIMATE\n  |  \n  271:UNION\n  |  output exprs:\n  |      [3760, VARCHAR(200), true]\n  |  child exprs:\n  |      [3109: sales_area_path, VARCHAR, true]\n  |      [3210: sales_area_path, VARCHAR, true]\n  |      [3311: sales_area_path, VARCHAR, true]\n  |      [3412: sales_area_path, VARCHAR, true]\n  |      [3513: sales_area_path, VARCHAR, true]\n  |      [3614: sales_area_path, VARCHAR, true]\n  |      [3715: sales_area_path, VARCHAR, true]\n  |  pass-through-operands: all\n  |  hasNullableGenerateChild: true\n  |  cardinality: 179581\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163323, 170946.***********] ESTIMATE\n  |  \n  |----299:EXCHANGE\n  |       partition exprs: [3165: branch_id, LARGEINT, true]\n  |       cardinality: 6391\n  |    \n  |----313:EXCHANGE\n  |       partition exprs: [3266: branch_id, LARGEINT, true]\n  |       cardinality: 518\n  |    \n  |----327:EXCHANGE\n  |       partition exprs: [3367: branch_id, LARGEINT, true]\n  |       cardinality: 97\n  |    \n  |----341:EXCHANGE\n  |       partition exprs: [3468: branch_id, LARGEINT, true]\n  |       cardinality: 25\n  |    \n  |----355:EXCHANGE\n  |       partition exprs: [3569: branch_id, LARGEINT, true]\n  |       cardinality: 2\n  |    \n  |----369:EXCHANGE\n  |       partition exprs: [3670: branch_id, LARGEINT, true]\n  |       cardinality: 1\n  |    \n  285:EXCHANGE\n     partition exprs: [3064: branch_id, LARGEINT, true]\n     cardinality: 172546\n\nPLAN FRAGMENT 46(F171)\n\n  Input Partition: HASH_PARTITIONED: 3670: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 369\n\n  368:Project\n  |  output columns:\n  |  3715 <-> [3715: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 1.002808214364868] ESTIMATE\n  |  \n  367:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [3663: sales_area_id, LARGEINT, true] = [5058: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 32, build_expr = (5058: cast), remote = true\n  |  output columns: 3715\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.02101150000015584E17, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 1.002808214364868] ESTIMATE\n  |  * cast-->[2.02101150000015584E17, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----366:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5058: cast, LARGEINT, true]\n  |       cardinality: 88714\n  |    \n  363:Project\n  |  output columns:\n  |  3663 <-> [3663: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  362:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [3670: branch_id, LARGEINT, true] = [3663: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 31, build_expr = (3663: sales_area_id), remote = true\n  |  output columns: 3663\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----361:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [3663: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 32, probe_expr = (3663: sales_area_id)\n  |    \n  358:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [3670: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 47(F172)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5058: cast\n  OutPut Exchange Id: 366\n\n  365:Project\n  |  output columns:\n  |  3715 <-> [3715: sales_area_path, VARCHAR, true]\n  |  5058 <-> cast([3753: sales_unit_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 88714\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 88713.99999999999] ESTIMATE\n  |  * cast-->[2.02101150000015584E17, 2.0240422000047728E17, 0.0, 16.0, 88713.99999999999] ESTIMATE\n  |  \n  364:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 3715: sales_area_path IS NOT NULL, CAST(3753: sales_unit_id AS LARGEINT) IS NOT NULL, 3721: op IN ('+I', '+U'), [3720: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 88714\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 88713.99999999999] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * sales_unit_id-->[2.02101150000015584E17, 2.0240422000047728E17, 0.0, 8.0, 88713.99999999999] ESTIMATE\n     * cast-->[2.02101150000015584E17, 2.0240422000047728E17, 0.0, 16.0, 88713.99999999999] ESTIMATE\n\nPLAN FRAGMENT 48(F169)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3663: sales_area_id\n  OutPut Exchange Id: 361\n\n  360:Project\n  |  output columns:\n  |  3663 <-> [3663: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  359:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [3661: master_id, LARGEINT, true] = 202108100063214891, [3665: type, TINYINT, true] = 0, [3667: delete_flag, TINYINT, false] = 0, 3660: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     probe runtime filters:\n     - filter_id = 32, probe_expr = (3663: sales_area_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 49(F167)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3670: branch_id\n  OutPut Exchange Id: 358\n\n  357:Project\n  |  output columns:\n  |  3670 <-> [3670: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  356:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 3670: branch_id IS NOT NULL, [3687: deleted_flag, TINYINT, true] = 0, 3686: status IN (3, 6), 3669: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 31, probe_expr = (3670: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 50(F164)\n\n  Input Partition: HASH_PARTITIONED: 3569: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 355\n\n  354:Project\n  |  output columns:\n  |  3614 <-> [3614: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 2\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 2.1275695378384345] ESTIMATE\n  |  \n  353:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [3562: sales_area_id, LARGEINT, true] = [5059: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 30, build_expr = (5059: cast), remote = false\n  |  output columns: 3614\n  |  can local shuffle: false\n  |  cardinality: 2\n  |  column statistics: \n  |  * sales_area_id-->[2.02101150000001408E17, 2.02404220000477088E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 2.1275695378384345] ESTIMATE\n  |  * cast-->[2.02101150000001408E17, 2.02404220000477088E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----352:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5059: cast, LARGEINT, true]\n  |       cardinality: 163031\n  |    \n  349:Project\n  |  output columns:\n  |  3562 <-> [3562: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  348:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [3569: branch_id, LARGEINT, true] = [3562: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 29, build_expr = (3562: sales_area_id), remote = true\n  |  output columns: 3562\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----347:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [3562: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 30, probe_expr = (3562: sales_area_id)\n  |    \n  344:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [3569: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 51(F165)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5059: cast\n  OutPut Exchange Id: 352\n\n  351:Project\n  |  output columns:\n  |  3614 <-> [3614: sales_area_path, VARCHAR, true]\n  |  5059 <-> cast([3648: town_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 163031\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 163031.0] ESTIMATE\n  |  * cast-->[2.02101150000001408E17, 2.02404220000477088E17, 0.0, 16.0, 76843.0] ESTIMATE\n  |  \n  350:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 3614: sales_area_path IS NOT NULL, CAST(3648: town_id AS LARGEINT) IS NOT NULL, 3620: op IN ('+I', '+U'), [3619: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 163031\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 163031.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * town_id-->[2.02101150000001408E17, 2.02404220000477088E17, 0.0, 8.0, 76843.0] ESTIMATE\n     * cast-->[2.02101150000001408E17, 2.02404220000477088E17, 0.0, 16.0, 76843.0] ESTIMATE\n\nPLAN FRAGMENT 52(F162)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3562: sales_area_id\n  OutPut Exchange Id: 347\n\n  346:Project\n  |  output columns:\n  |  3562 <-> [3562: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  345:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [3560: master_id, LARGEINT, true] = 202108100063214891, [3564: type, TINYINT, true] = 0, [3566: delete_flag, TINYINT, false] = 0, 3559: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 53(F160)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3569: branch_id\n  OutPut Exchange Id: 344\n\n  343:Project\n  |  output columns:\n  |  3569 <-> [3569: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  342:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 3569: branch_id IS NOT NULL, [3586: deleted_flag, TINYINT, true] = 0, 3585: status IN (3, 6), 3568: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 29, probe_expr = (3569: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 54(F157)\n\n  Input Partition: HASH_PARTITIONED: 3468: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 341\n\n  340:Project\n  |  output columns:\n  |  3513 <-> [3513: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 25\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 25.326926763547505] ESTIMATE\n  |  \n  339:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [3461: sales_area_id, LARGEINT, true] = [5060: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 28, build_expr = (5060: cast), remote = true\n  |  output columns: 3513\n  |  can local shuffle: false\n  |  cardinality: 25\n  |  column statistics: \n  |  * sales_area_id-->[2.02101150000001376E17, 2.02404130000135936E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 25.326926763547505] ESTIMATE\n  |  * cast-->[2.02101150000001376E17, 2.02404130000135936E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----338:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5060: cast, LARGEINT, true]\n  |       cardinality: 80996\n  |    \n  335:Project\n  |  output columns:\n  |  3461 <-> [3461: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  334:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [3468: branch_id, LARGEINT, true] = [3461: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 27, build_expr = (3461: sales_area_id), remote = true\n  |  output columns: 3461\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----333:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [3461: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 28, probe_expr = (3461: sales_area_id)\n  |    \n  330:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [3468: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 55(F158)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5060: cast\n  OutPut Exchange Id: 338\n\n  337:Project\n  |  output columns:\n  |  3513 <-> [3513: sales_area_path, VARCHAR, true]\n  |  5060 <-> cast([3543: second_theater_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 80996\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 80996.0] ESTIMATE\n  |  * cast-->[2.02101150000001376E17, 2.02404130000135936E17, 0.0, 16.0, 3207.0] ESTIMATE\n  |  \n  336:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 3513: sales_area_path IS NOT NULL, CAST(3543: second_theater_id AS LARGEINT) IS NOT NULL, 3519: op IN ('+I', '+U'), [3518: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 80996\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 80996.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * second_theater_id-->[2.02101150000001376E17, 2.02404130000135936E17, 0.0, 8.0, 3207.0] ESTIMATE\n     * cast-->[2.02101150000001376E17, 2.02404130000135936E17, 0.0, 16.0, 3207.0] ESTIMATE\n\nPLAN FRAGMENT 56(F155)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3461: sales_area_id\n  OutPut Exchange Id: 333\n\n  332:Project\n  |  output columns:\n  |  3461 <-> [3461: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  331:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [3459: master_id, LARGEINT, true] = 202108100063214891, [3463: type, TINYINT, true] = 0, [3465: delete_flag, TINYINT, false] = 0, 3458: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     probe runtime filters:\n     - filter_id = 28, probe_expr = (3461: sales_area_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 57(F153)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3468: branch_id\n  OutPut Exchange Id: 330\n\n  329:Project\n  |  output columns:\n  |  3468 <-> [3468: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  328:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 3468: branch_id IS NOT NULL, [3485: deleted_flag, TINYINT, true] = 0, 3484: status IN (3, 6), 3467: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 27, probe_expr = (3468: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 58(F150)\n\n  Input Partition: HASH_PARTITIONED: 3367: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 327\n\n  326:Project\n  |  output columns:\n  |  3412 <-> [3412: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 97\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 97.**************] ESTIMATE\n  |  \n  325:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [3360: sales_area_id, LARGEINT, true] = [5061: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 26, build_expr = (5061: cast), remote = false\n  |  output columns: 3412\n  |  can local shuffle: false\n  |  cardinality: 97\n  |  column statistics: \n  |  * sales_area_id-->[2.02101150000001152E17, 2.02404220000359744E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 97.**************] ESTIMATE\n  |  * cast-->[2.02101150000001152E17, 2.02404220000359744E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----324:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5061: cast, LARGEINT, true]\n  |       cardinality: 161920\n  |    \n  321:Project\n  |  output columns:\n  |  3360 <-> [3360: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  320:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [3367: branch_id, LARGEINT, true] = [3360: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 25, build_expr = (3360: sales_area_id), remote = true\n  |  output columns: 3360\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----319:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [3360: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 26, probe_expr = (3360: sales_area_id)\n  |    \n  316:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [3367: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 59(F151)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5061: cast\n  OutPut Exchange Id: 324\n\n  323:Project\n  |  output columns:\n  |  3412 <-> [3412: sales_area_path, VARCHAR, true]\n  |  5061 <-> cast([3438: second_area_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 161920\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 161920.0] ESTIMATE\n  |  * cast-->[2.02101150000001152E17, 2.02404220000359744E17, 0.0, 16.0, 1667.0] ESTIMATE\n  |  \n  322:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 3412: sales_area_path IS NOT NULL, CAST(3438: second_area_id AS LARGEINT) IS NOT NULL, 3418: op IN ('+I', '+U'), [3417: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 161920\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 161920.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * second_area_id-->[2.02101150000001152E17, 2.02404220000359744E17, 0.0, 8.0, 1667.0] ESTIMATE\n     * cast-->[2.02101150000001152E17, 2.02404220000359744E17, 0.0, 16.0, 1667.0] ESTIMATE\n\nPLAN FRAGMENT 60(F148)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3360: sales_area_id\n  OutPut Exchange Id: 319\n\n  318:Project\n  |  output columns:\n  |  3360 <-> [3360: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  317:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [3358: master_id, LARGEINT, true] = 202108100063214891, [3362: type, TINYINT, true] = 0, [3364: delete_flag, TINYINT, false] = 0, 3357: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 61(F146)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3367: branch_id\n  OutPut Exchange Id: 316\n\n  315:Project\n  |  output columns:\n  |  3367 <-> [3367: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  314:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 3367: branch_id IS NOT NULL, [3384: deleted_flag, TINYINT, true] = 0, 3383: status IN (3, 6), 3366: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 25, probe_expr = (3367: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 62(F143)\n\n  Input Partition: HASH_PARTITIONED: 3266: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 313\n\n  312:Project\n  |  output columns:\n  |  3311 <-> [3311: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 518\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 518.*************] ESTIMATE\n  |  \n  311:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [3259: sales_area_id, LARGEINT, true] = [5062: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 24, build_expr = (5062: cast), remote = false\n  |  output columns: 3311\n  |  can local shuffle: false\n  |  cardinality: 518\n  |  column statistics: \n  |  * sales_area_id-->[2.0210115000000112E17, 2.02402240000502304E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 518.*************] ESTIMATE\n  |  * cast-->[2.0210115000000112E17, 2.02402240000502304E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----310:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5062: cast, LARGEINT, true]\n  |       cardinality: 166993\n  |    \n  307:Project\n  |  output columns:\n  |  3259 <-> [3259: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  306:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [3266: branch_id, LARGEINT, true] = [3259: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 23, build_expr = (3259: sales_area_id), remote = true\n  |  output columns: 3259\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----305:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [3259: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 24, probe_expr = (3259: sales_area_id)\n  |    \n  302:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [3266: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 63(F144)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5062: cast\n  OutPut Exchange Id: 310\n\n  309:Project\n  |  output columns:\n  |  3311 <-> [3311: sales_area_path, VARCHAR, true]\n  |  5062 <-> cast([3333: first_theater_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 166993\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 166993.0] ESTIMATE\n  |  * cast-->[2.0210115000000112E17, 2.02402240000502304E17, 0.0, 16.0, 323.0] ESTIMATE\n  |  \n  308:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 3311: sales_area_path IS NOT NULL, CAST(3333: first_theater_id AS LARGEINT) IS NOT NULL, 3317: op IN ('+I', '+U'), [3316: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 166993\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 166993.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * first_theater_id-->[2.0210115000000112E17, 2.02402240000502304E17, 0.0, 8.0, 323.0] ESTIMATE\n     * cast-->[2.0210115000000112E17, 2.02402240000502304E17, 0.0, 16.0, 323.0] ESTIMATE\n\nPLAN FRAGMENT 64(F141)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3259: sales_area_id\n  OutPut Exchange Id: 305\n\n  304:Project\n  |  output columns:\n  |  3259 <-> [3259: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  303:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [3257: master_id, LARGEINT, true] = 202108100063214891, [3261: type, TINYINT, true] = 0, [3263: delete_flag, TINYINT, false] = 0, 3256: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 65(F139)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3266: branch_id\n  OutPut Exchange Id: 302\n\n  301:Project\n  |  output columns:\n  |  3266 <-> [3266: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  300:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 3266: branch_id IS NOT NULL, [3283: deleted_flag, TINYINT, true] = 0, 3282: status IN (3, 6), 3265: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 23, probe_expr = (3266: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 66(F136)\n\n  Input Partition: HASH_PARTITIONED: 3165: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 299\n\n  298:Project\n  |  output columns:\n  |  3210 <-> [3210: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 6391\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 6390.562480742515] ESTIMATE\n  |  \n  297:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [3158: sales_area_id, LARGEINT, true] = [5063: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 22, build_expr = (5063: cast), remote = false\n  |  output columns: 3210\n  |  can local shuffle: false\n  |  cardinality: 6391\n  |  column statistics: \n  |  * sales_area_id-->[2.02101150000000032E17, 2.02403190000280864E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 6390.562480742515] ESTIMATE\n  |  * cast-->[2.02101150000000032E17, 2.02403190000280864E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----296:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5063: cast, LARGEINT, true]\n  |       cardinality: 172062\n  |    \n  293:Project\n  |  output columns:\n  |  3158 <-> [3158: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  292:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [3165: branch_id, LARGEINT, true] = [3158: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 21, build_expr = (3158: sales_area_id), remote = true\n  |  output columns: 3158\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----291:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [3158: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 22, probe_expr = (3158: sales_area_id)\n  |    \n  288:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [3165: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 67(F137)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5063: cast\n  OutPut Exchange Id: 296\n\n  295:Project\n  |  output columns:\n  |  3210 <-> [3210: sales_area_path, VARCHAR, true]\n  |  5063 <-> cast([3228: first_area_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 172062\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n  |  * cast-->[2.02101150000000032E17, 2.02403190000280864E17, 0.0, 16.0, 27.0] ESTIMATE\n  |  \n  294:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 3210: sales_area_path IS NOT NULL, CAST(3228: first_area_id AS LARGEINT) IS NOT NULL, 3216: op IN ('+I', '+U'), [3215: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 172062\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * first_area_id-->[2.02101150000000032E17, 2.02403190000280864E17, 0.0, 8.0, 27.0] ESTIMATE\n     * cast-->[2.02101150000000032E17, 2.02403190000280864E17, 0.0, 16.0, 27.0] ESTIMATE\n\nPLAN FRAGMENT 68(F134)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3158: sales_area_id\n  OutPut Exchange Id: 291\n\n  290:Project\n  |  output columns:\n  |  3158 <-> [3158: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  289:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [3156: master_id, LARGEINT, true] = 202108100063214891, [3160: type, TINYINT, true] = 0, [3162: delete_flag, TINYINT, false] = 0, 3155: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 69(F132)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3165: branch_id\n  OutPut Exchange Id: 288\n\n  287:Project\n  |  output columns:\n  |  3165 <-> [3165: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  286:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 3165: branch_id IS NOT NULL, [3182: deleted_flag, TINYINT, true] = 0, 3181: status IN (3, 6), 3164: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 21, probe_expr = (3165: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 70(F129)\n\n  Input Partition: HASH_PARTITIONED: 3064: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 285\n\n  284:Project\n  |  output columns:\n  |  3109 <-> [3109: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 172546\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n  |  \n  283:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [3057: sales_area_id, LARGEINT, true] = [5064: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 20, build_expr = (5064: cast), remote = false\n  |  output columns: 3109\n  |  can local shuffle: false\n  |  cardinality: 172546\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n  |  * cast-->[2.01910110000001E14, 2.01910110000001E14, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----282:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5064: cast, LARGEINT, true]\n  |       cardinality: 172063\n  |    \n  279:Project\n  |  output columns:\n  |  3057 <-> [3057: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  278:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [3064: branch_id, LARGEINT, true] = [3057: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 19, build_expr = (3057: sales_area_id), remote = true\n  |  output columns: 3057\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----277:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [3057: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 20, probe_expr = (3057: sales_area_id)\n  |    \n  274:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [3064: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 71(F130)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5064: cast\n  OutPut Exchange Id: 282\n\n  281:Project\n  |  output columns:\n  |  3109 <-> [3109: sales_area_path, VARCHAR, true]\n  |  5064 <-> cast([3116: sales_market_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 172063\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n  |  * cast-->[2.01910110000001E14, 2.01910110000001E14, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  280:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 3109: sales_area_path IS NOT NULL, CAST(3116: sales_market_id AS LARGEINT) IS NOT NULL, 3115: op IN ('+I', '+U'), [3114: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 172063\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * sales_market_id-->[2.01910110000001E14, 2.01910110000001E14, 0.0, 8.0, 1.0] ESTIMATE\n     * cast-->[2.01910110000001E14, 2.01910110000001E14, 0.0, 16.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 72(F127)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3057: sales_area_id\n  OutPut Exchange Id: 277\n\n  276:Project\n  |  output columns:\n  |  3057 <-> [3057: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  275:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [3055: master_id, LARGEINT, true] = 202108100063214891, [3059: type, TINYINT, true] = 0, [3061: delete_flag, TINYINT, false] = 0, 3054: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 73(F125)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 3064: branch_id\n  OutPut Exchange Id: 274\n\n  273:Project\n  |  output columns:\n  |  3064 <-> [3064: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  272:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 3064: branch_id IS NOT NULL, [3081: deleted_flag, TINYINT, true] = 0, 3080: status IN (3, 6), 3063: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 19, probe_expr = (3064: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 74(F113)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 266\n\n  265:Project\n  |  output columns:\n  |  2866 <-> [2866: customer_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  \n  264:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5065: cast, VARCHAR(50), true] = [5066: cast, VARCHAR(50), true]\n  |  output columns: 2866\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.999995651984869, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.999995651984869, 1.0, 2.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  |----263:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  260:Project\n  |  output columns:\n  |  2866 <-> [2866: customer_code, VARCHAR, true]\n  |  5065 <-> cast([2910: district, BIGINT, true] as VARCHAR(50))\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  \n  259:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5067: cast, VARCHAR(50), true] = [5068: cast, VARCHAR(50), true]\n  |  output columns: 2866, 2910\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.999995651984869, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.999995651984869, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  |----258:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  255:Project\n  |  output columns:\n  |  2866 <-> [2866: customer_code, VARCHAR, true]\n  |  2910 <-> [2910: district, BIGINT, true]\n  |  5067 <-> cast([2909: city, BIGINT, true] as VARCHAR(50))\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  \n  254:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5069: cast, VARCHAR(50), true] = [5070: cast, VARCHAR(50), true]\n  |  output columns: 2866, 2909, 2910\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 1.0] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.999995651984869, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.999995651984869, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  |----253:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  250:Project\n  |  output columns:\n  |  2866 <-> [2866: customer_code, VARCHAR, true]\n  |  2909 <-> [2909: city, BIGINT, true]\n  |  2910 <-> [2910: district, BIGINT, true]\n  |  5069 <-> cast([2908: province, BIGINT, true] as VARCHAR(50))\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 173304.50986246436] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  \n  249:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5071: cast, LARGEINT, false] = [2927: master_branch_id, LARGEINT, true]\n  |  output columns: 2866, 2908, 2909, 2910\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 173304.50986246436] ESTIMATE\n  |  * province-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.2464693688314085, 2.0, 2.0] ESTIMATE\n  |  * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.37992224485821285, 16.0, 209577.4444444444] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.3798982750195003, 1.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.2464693688314085, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 173304.50986246436] ESTIMATE\n  |  \n  |----248:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 209577\n  |    \n  245:Project\n  |  output columns:\n  |  2866 <-> [2866: customer_code, VARCHAR, true]\n  |  2908 <-> [2908: province, BIGINT, true]\n  |  2909 <-> [2909: city, BIGINT, true]\n  |  2910 <-> [2910: district, BIGINT, true]\n  |  5071 <-> cast([2864: customer_id, BIGINT, false] as LARGEINT)\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 229990.0] ESTIMATE\n  |  * province-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 228870.0] ESTIMATE\n  |  \n  244:OlapScanNode\n     table: dim_customer_chain, rollup: dim_customer_chain\n     preAggregation: on\n     Predicates: 2913: op IN ('+I', '+U'), 2869: customer_status IN (3, 6), [2914: delete_flag, TINYINT, true] = 0\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=243024,243025,243026,243027,243028\n     actualRows=239125, avgRowSize=61.18988\n     cardinality: 229990\n     column statistics: \n     * customer_id-->[10001.0, 2.02404220000479872E17, 0.0, 8.0, 228870.0] ESTIMATE\n     * customer_code-->[-Infinity, Infinity, 0.0, 9.***************, 229990.0] ESTIMATE\n     * customer_status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * province-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n     * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n     * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 228870.0] ESTIMATE\n\nPLAN FRAGMENT 75(F120)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 263\n\n  262:Project\n  |  output columns:\n  |  5066 <-> cast([2985: id, LARGEINT, false] as VARCHAR(50))\n  |  cardinality: 1109\n  |  column statistics: \n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  261:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [2999: delete_flag, TINYINT, true] = 0, 2986: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=35.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n\nPLAN FRAGMENT 76(F118)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 258\n\n  257:Project\n  |  output columns:\n  |  5068 <-> cast([2959: id, LARGEINT, false] as VARCHAR(50))\n  |  cardinality: 1109\n  |  column statistics: \n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  256:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [2973: delete_flag, TINYINT, true] = 0, 2960: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=35.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n\nPLAN FRAGMENT 77(F116)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 253\n\n  252:Project\n  |  output columns:\n  |  5070 <-> cast([2933: id, LARGEINT, false] as VARCHAR(50))\n  |  cardinality: 1109\n  |  column statistics: \n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  251:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [2947: delete_flag, TINYINT, true] = 0, 2934: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=35.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n\nPLAN FRAGMENT 78(F114)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 248\n\n  247:Project\n  |  output columns:\n  |  2927 <-> [2927: master_branch_id, LARGEINT, true]\n  |  cardinality: 209577\n  |  column statistics: \n  |  * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.17710345207844144, 16.0, 209577.4444444444] ESTIMATE\n  |  \n  246:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [2932: delete_flag, TINYINT, false] = 0, 2931: status IN (3, 6), 2925: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=20.0\n     cardinality: 209577\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.17710345207844144, 16.0, 209577.4444444444] ESTIMATE\n     * status-->[3.0, 6.0, 0.17707164203951126, 1.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 79(F104)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 242\n\n  241:Project\n  |  output columns:\n  |  2808 <-> [2808: cast, VARCHAR(65533), true]\n  |  2817 <-> [2817: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 141547\n  |  column statistics: \n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  \n  240:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [2817: sales_area_id, LARGEINT, false] = [2824: branch_id, LARGEINT, true]\n  |  output columns: 2808, 2817\n  |  can local shuffle: false\n  |  cardinality: 141547\n  |  column statistics: \n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  \n  |----239:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 185513\n  |    \n  236:Project\n  |  output columns:\n  |  2808 <-> [2808: cast, VARCHAR(65533), true]\n  |  2817 <-> [2817: sales_area_id, LARGEINT, false]\n  |  cardinality: 141151\n  |  column statistics: \n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  \n  235:HASH JOIN\n  |  join op: INNER JOIN (BROADCAST)\n  |  equal join conjunct: [2807: cast, LARGEINT, true] = [2816: master_branch_id, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 17, build_expr = (2816: master_branch_id), remote = true\n  |  output columns: 2808, 2817\n  |  can local shuffle: false\n  |  cardinality: 141151\n  |  column statistics: \n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  \n  |----234:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 125746\n  |       probe runtime filters:\n  |       - filter_id = 34, probe_expr = (2817: sales_area_id)\n  |    \n  222:UNION\n  |  output exprs:\n  |      [2807, LARGEINT, true] | [2808, VARCHAR(65533), true]\n  |  child exprs:\n  |      [2624: cast, LARGEINT, true] | [2625: cast, VARCHAR, true]\n  |      [2703: cast, LARGEINT, true] | [2704: cast, VARCHAR, true]\n  |      [2709: branch_id, LARGEINT, false] | [2710: store_code, VARCHAR, true]\n  |  pass-through-operands: all\n  |  cardinality: 291333\n  |  column statistics: \n  |  * cast-->[10001.0, 2.0240513000048176E17, 0.0, 16.0, 213573.7711807576] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  \n  |----228:EXCHANGE\n  |       cardinality: 4034\n  |       probe runtime filters:\n  |       - filter_id = 17, probe_expr = (2703: cast)\n  |    \n  |----231:EXCHANGE\n  |       cardinality: 208006\n  |       probe runtime filters:\n  |       - filter_id = 17, probe_expr = (2709: branch_id)\n  |    \n  225:EXCHANGE\n     cardinality: 79293\n     probe runtime filters:\n     - filter_id = 17, probe_expr = (2624: cast)\n\nPLAN FRAGMENT 80(F110)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 239\n\n  238:Project\n  |  output columns:\n  |  2824 <-> [2824: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  237:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: [2841: deleted_flag, TINYINT, true] = 0, 2840: status IN (3, 6), 2823: op IN ('+I', '+U'), 2824: branch_id IS NOT NULL\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 81(F108)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 234\n\n  233:Project\n  |  output columns:\n  |  2816 <-> [2816: master_branch_id, LARGEINT, true]\n  |  2817 <-> [2817: sales_area_id, LARGEINT, false]\n  |  cardinality: 125746\n  |  column statistics: \n  |  * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.17710345207844144, 16.0, 125746.***********] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  \n  232:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [2821: delete_flag, TINYINT, false] = 0, 2820: status IN (3, 6), 2819: type IN (1, 2, 3), 2814: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=37.0\n     cardinality: 125746\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.17710345207844144, 16.0, 125746.***********] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n     * type-->[1.0, 3.0, 0.0, 1.0, 3.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.17707164203951126, 1.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 82(F107)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 231\n\n  230:Project\n  |  output columns:\n  |  2709 <-> [2709: branch_id, LARGEINT, false]\n  |  2710 <-> [2710: store_code, VARCHAR, true]\n  |  cardinality: 208006\n  |  column statistics: \n  |  * branch_id-->[2.02101160000110016E17, 2.0240513000048176E17, 0.0, 16.0, 130431.0] ESTIMATE\n  |  * store_code-->[-Infinity, Infinity, 6.623740554492552E-5, 9.180534322193697, 130558.0] ESTIMATE\n  |  \n  229:OlapScanNode\n     table: store, rollup: store\n     preAggregation: on\n     Predicates: [2741: delete_flag, TINYINT, false] = 0, 2736: status IN (3, 6), 2708: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=2/2\n     tabletList=16797,16798\n     actualRows=986567, avgRowSize=29.180534\n     cardinality: 208006\n     probe runtime filters:\n     - filter_id = 17, probe_expr = (2709: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.02101160000110016E17, 2.0240513000048176E17, 0.0, 16.0, 130431.0] ESTIMATE\n     * store_code-->[-Infinity, Infinity, 6.623740554492552E-5, 9.180534322193697, 130558.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 83(F106)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 228\n\n  227:Project\n  |  output columns:\n  |  2703 <-> cast([2630: branch_id, BIGINT, true] as LARGEINT)\n  |  2704 <-> [2631: number, VARCHAR, true]\n  |  cardinality: 4034\n  |  column statistics: \n  |  * cast-->[10001.0, 2.02405100000051104E17, 0.0, 16.0, 3981.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 4.957448566471123E-4, 8.***************, 4020.0] ESTIMATE\n  |  \n  226:OlapScanNode\n     table: agency, rollup: agency\n     preAggregation: on\n     Predicates: CAST(2630: branch_id AS LARGEINT) IS NOT NULL, [2666: delete_flag, TINYINT, true] = 0, 2662: status IN (3, 6), 2629: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=16/16\n     tabletList=16751,16752,16753,16754,16755,16756,16757,16758,16759,16760 ...\n     actualRows=12132, avgRowSize=44.27613\n     cardinality: 4034\n     probe runtime filters:\n     - filter_id = 17, probe_expr = (CAST(2630: branch_id AS LARGEINT))\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[10001.0, 2.02405100000051104E17, 0.0, 8.0, 3981.0] ESTIMATE\n     * number-->[-Infinity, Infinity, 4.957448566471123E-4, 8.***************, 4020.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[10001.0, 2.02405100000051104E17, 0.0, 16.0, 3981.0] ESTIMATE\n     * cast-->[-Infinity, Infinity, 4.957448566471123E-4, 8.***************, 4020.0] ESTIMATE\n\nPLAN FRAGMENT 84(F105)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 225\n\n  224:Project\n  |  output columns:\n  |  2624 <-> cast([2545: branch_id, BIGINT, true] as LARGEINT)\n  |  2625 <-> [2546: number, VARCHAR, true]\n  |  cardinality: 79293\n  |  column statistics: \n  |  * cast-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 16.0, 79292.8] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.7151620323661165E-4, 9.***************, 79292.8] ESTIMATE\n  |  \n  223:OlapScanNode\n     table: retailer, rollup: retailer\n     preAggregation: on\n     Predicates: CAST(2545: branch_id AS LARGEINT) IS NOT NULL, [2575: delete_flag, TINYINT, true] = 0, 2581: status IN (3, 6), 2544: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=16/16\n     tabletList=16770,16771,16772,16773,16774,16775,16776,16777,16778,16779 ...\n     actualRows=601020, avgRowSize=46.363583\n     cardinality: 79293\n     probe runtime filters:\n     - filter_id = 17, probe_expr = (CAST(2545: branch_id AS LARGEINT))\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 8.0, 79292.8] ESTIMATE\n     * number-->[-Infinity, Infinity, 1.7151620323661165E-4, 9.***************, 79292.8] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 16.0, 79292.8] ESTIMATE\n     * cast-->[-Infinity, Infinity, 1.7151620323661165E-4, 9.***************, 79292.8] ESTIMATE\n\nPLAN FRAGMENT 85(F102)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 219\n\n  218:OlapScanNode\n     table: dim_vwork_province_mapping2, rollup: dim_vwork_province_mapping2\n     preAggregation: on\n     Predicates: 2537: erp_code IS NOT NULL\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=55232\n     actualRows=24, avgRowSize=11.291667\n     cardinality: 24\n     column statistics: \n     * agent1_code-->[-Infinity, Infinity, 0.0, 5.***************, 24.0] ESTIMATE\n     * erp_code-->[-Infinity, Infinity, 0.0, 6.25, 24.0] ESTIMATE\n\nPLAN FRAGMENT 86(F100)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 215\n\n  214:Project\n  |  output columns:\n  |  2496 <-> [2496: warehouse_code, VARCHAR, true]\n  |  2504 <-> [2504: superior_id, LARGEINT, true]\n  |  cardinality: 220611\n  |  column statistics: \n  |  * warehouse_code-->[-Infinity, Infinity, 4.029215844085464E-6, 11.***************, 220610.***********] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n  |  \n  213:OlapScanNode\n     table: warehouse, rollup: warehouse\n     preAggregation: on\n     Predicates: 2494: op IN ('+I', '+U'), [2521: deleted_flag, TINYINT, true] = 0, 2519: status IN (3, 6)\n     partitionsRatio=1/1, tabletsRatio=2/2\n     tabletList=16316,16317\n     actualRows=993303, avgRowSize=31.118835\n     cardinality: 220611\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * warehouse_code-->[-Infinity, Infinity, 4.029215844085464E-6, 11.***************, 220610.***********] ESTIMATE\n     * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 87(F99)\n\n  Input Partition: HASH_PARTITIONED: 2360: imei1, 2361: vivo_source\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 212\n\n  211:Project\n  |  output columns:\n  |  2360 <-> [2360: imei1, VARCHAR, false]\n  |  2361 <-> [2361: vivo_source, VARCHAR, false]\n  |  2369 <-> [2369: first_agent_code, VARCHAR, true]\n  |  2371 <-> [2371: customer_account_code, VARCHAR, true]\n  |  2372 <-> [2372: customer_warehouse_code, VARCHAR, true]\n  |  2373 <-> [2373: sku_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 8764\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 8764.1***********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |  * first_agent_code-->[-Infinity, Infinity, 0.0, 6.****************, 23.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 12.0, 1.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 11.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 8.0, 586.0] ESTIMATE\n  |  \n  210:HASH JOIN\n  |  join op: LEFT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [2360: imei1, VARCHAR, false] = [2412: imei1, VARCHAR, true]\n  |  equal join conjunct: [2361: vivo_source, VARCHAR, false] = [2413: vivo_source, VARCHAR, true]\n  |  other predicates: 2412: imei1 IS NULL\n  |  output columns: 2360, 2361, 2369, 2371, 2372, 2373, 2412\n  |  can local shuffle: false\n  |  cardinality: 8764\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 8764.1***********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |  * first_agent_code-->[-Infinity, Infinity, 0.0, 6.****************, 23.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 12.0, 1.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 11.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 8.0, 586.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 1.0, 24.06498, 8764.1***********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.*****************, 1.8E-4, 2.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.*****************, 3.0E-5, 2.0] ESTIMATE\n  |  * del_flag-->[-Infinity, Infinity, 0.*****************, 1.0E-5, 2.0] ESTIMATE\n  |  \n  |----209:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [2412: imei1, VARCHAR, true], [2413: vivo_source, VARCHAR, true]\n  |       cardinality: 7460771\n  |    \n  206:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [2360: imei1, VARCHAR, false], [2361: vivo_source, VARCHAR, false]\n     cardinality: 87642\n\nPLAN FRAGMENT 88(F97)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 2412: imei1, 2413: vivo_source\n  OutPut Exchange Id: 209\n\n  208:Project\n  |  output columns:\n  |  2412 <-> [2412: imei1, VARCHAR, false]\n  |  2413 <-> [2413: vivo_source, VARCHAR, false]\n  |  cardinality: 7460771\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.06498, 7460771.444444444] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.8E-4, 2.0] ESTIMATE\n  |  \n  207:OlapScanNode\n     table: wide_serialcode_center, rollup: wide_serialcode_center\n     preAggregation: on\n     Predicates: 2413: vivo_source IN ('INGJ01', 'vwork_sale_ruichuang'), 2414: op IN ('+I', '+U'), [2436: del_flag, VARCHAR, true] = 'N'\n     partitionsRatio=1/1, tabletsRatio=40/40\n     tabletList=271039,271040,271041,271042,271043,271044,271045,271046,271047,271048 ...\n     actualRows=********, avgRowSize=24.0652\n     cardinality: 7460771\n     column statistics: \n     * imei1-->[-Infinity, Infinity, 0.0, 24.06498, 7460771.444444444] ESTIMATE\n     * vivo_source-->[-Infinity, Infinity, 0.0, 1.8E-4, 2.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 3.0E-5, 2.0] ESTIMATE\n     * del_flag-->[-Infinity, Infinity, 0.0, 1.0E-5, 2.0] ESTIMATE\n\nPLAN FRAGMENT 89(F95)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 2360: imei1, 2361: vivo_source\n  OutPut Exchange Id: 206\n\n  205:Project\n  |  output columns:\n  |  2360 <-> [2360: imei1, VARCHAR, false]\n  |  2361 <-> [2361: vivo_source, VARCHAR, false]\n  |  2369 <-> [2369: first_agent_code, VARCHAR, true]\n  |  2371 <-> [2371: customer_account_code, VARCHAR, true]\n  |  2372 <-> [2372: customer_warehouse_code, VARCHAR, true]\n  |  2373 <-> [2373: sku_code, VARCHAR, true]\n  |  cardinality: 87642\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 87641.***********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |  * first_agent_code-->[-Infinity, Infinity, 0.0, 6.****************, 23.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 12.0, 1.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 11.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 8.0, 586.0] ESTIMATE\n  |  \n  204:OlapScanNode\n     table: wide_return_serialcode_center, rollup: wide_return_serialcode_center\n     preAggregation: on\n     Predicates: 2373: sku_code IS NOT NULL, 2363: op IN ('+I', '+U'), 2361: vivo_source IN ('INGJ01', 'vwork_sale_ruichuang'), [2385: del_flag, VARCHAR, true] = 'N', [2377: business_status, VARCHAR, true] = 'RETURN_FACTORY'\n     partitionsRatio=1/1, tabletsRatio=10/10\n     tabletList=271121,271122,271123,271124,271125,271126,271127,271128,271129,271130\n     actualRows=1510607, avgRowSize=85.127335\n     cardinality: 87642\n     probe runtime filters:\n     - filter_id = 16, probe_expr = (2369: first_agent_code)\n     - filter_id = 36, probe_expr = (2373: sku_code)\n     column statistics: \n     * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 87641.***********] ESTIMATE\n     * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * first_agent_code-->[-Infinity, Infinity, 0.0, 6.****************, 23.0] ESTIMATE\n     * customer_account_code-->[-Infinity, Infinity, 0.0, 12.0, 1.0] ESTIMATE\n     * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 11.0, 1.0] ESTIMATE\n     * sku_code-->[-Infinity, Infinity, 0.0, 8.0, 586.0] ESTIMATE\n     * business_status-->[-Infinity, Infinity, 0.0, 14.0, 1.0] ESTIMATE\n     * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 90(F94)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 203\n\n  202:Project\n  |  output columns:\n  |  2311 <-> [2311: imei1, VARCHAR, false]\n  |  2312 <-> [2312: vivo_source, VARCHAR, false]\n  |  2319 <-> [2319: first_agent_code, VARCHAR, true]\n  |  2321 <-> [2321: customer_account_code, VARCHAR, true]\n  |  2322 <-> [2322: customer_warehouse_code, VARCHAR, true]\n  |  2323 <-> [2323: sku_code, VARCHAR, true]\n  |  cardinality: 1243462\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.06498, 1243461.**********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.8E-4, 2.0] ESTIMATE\n  |  * first_agent_code-->[-Infinity, Infinity, 0.0, 1.8E-4, 6.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.656275, 11059.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 0.985805, 18343.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.01892, 473.0] ESTIMATE\n  |  \n  201:OlapScanNode\n     table: wide_serialcode_center, rollup: wide_serialcode_center\n     preAggregation: on\n     Predicates: 2323: sku_code IS NOT NULL, 2313: op IN ('+I', '+U'), 2312: vivo_source IN ('INGJ01', 'vwork_sale_ruichuang'), [2335: del_flag, VARCHAR, true] = 'N', [2327: business_status, VARCHAR, true] = 'RETURN_FACTORY'\n     partitionsRatio=1/1, tabletsRatio=40/40\n     tabletList=271039,271040,271041,271042,271043,271044,271045,271046,271047,271048 ...\n     actualRows=********, avgRowSize=25.726645\n     cardinality: 1243462\n     probe runtime filters:\n     - filter_id = 16, probe_expr = (2319: first_agent_code)\n     - filter_id = 36, probe_expr = (2323: sku_code)\n     column statistics: \n     * imei1-->[-Infinity, Infinity, 0.0, 24.06498, 1243461.**********] ESTIMATE\n     * vivo_source-->[-Infinity, Infinity, 0.0, 1.8E-4, 2.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 3.0E-5, 2.0] ESTIMATE\n     * first_agent_code-->[-Infinity, Infinity, 0.0, 1.8E-4, 6.0] ESTIMATE\n     * customer_account_code-->[-Infinity, Infinity, 0.0, 0.656275, 11059.0] ESTIMATE\n     * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 0.985805, 18343.0] ESTIMATE\n     * sku_code-->[-Infinity, Infinity, 0.0, 0.01892, 473.0] ESTIMATE\n     * business_status-->[-Infinity, Infinity, 0.0, 2.65E-4, 6.0] ESTIMATE\n     * del_flag-->[-Infinity, Infinity, 0.0, 1.0E-5, 2.0] ESTIMATE\n\nPLAN FRAGMENT 91(F01)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 199\n\n  198:Project\n  |  output columns:\n  |  906 <-> [906: vivo_source, VARCHAR(128), true]\n  |  908 <-> [908: imei1, VARCHAR(127), true]\n  |  914 <-> [914: customer_account_code, VARCHAR(128), true]\n  |  916 <-> [916: sku_code, VARCHAR(128), true]\n  |  946 <-> [946: superior_id, LARGEINT, true]\n  |  1240 <-> [1240: cast, VARCHAR(65533), true]\n  |  1249 <-> [1249: sales_area_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 6221692\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.004800120942044749, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 6217309.*********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.024541260905342026, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.****************, 16.0, 123837.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  \n  197:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [1249: sales_area_id, LARGEINT, true] = [1558: branch_id, LARGEINT, true]\n  |  other predicates: (1555: data_id IS NOT NULL) OR ((1558: branch_id IS NOT NULL) AND (1574: status IN (3, 6)))\n  |  output columns: 906, 908, 914, 916, 946, 1240, 1249, 1555, 1558, 1574\n  |  can local shuffle: false\n  |  cardinality: 6221692\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.004800120942044749, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 6217309.*********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.024541260905342026, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.****************, 16.0, 123837.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * data_id-->[-Infinity, Infinity, 0.0, 16.0, 8.***************] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  \n  |----196:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 170946\n  |    \n  85:Project\n  |  output columns:\n  |  906 <-> [906: vivo_source, VARCHAR(128), true]\n  |  908 <-> [908: imei1, VARCHAR(127), true]\n  |  914 <-> [914: customer_account_code, VARCHAR(128), true]\n  |  916 <-> [916: sku_code, VARCHAR(128), true]\n  |  946 <-> [946: superior_id, LARGEINT, true]\n  |  1240 <-> [1240: cast, VARCHAR(65533), true]\n  |  1249 <-> [1249: sales_area_id, LARGEINT, true]\n  |  1555 <-> [1555: data_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 6221692\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.004800120942044749, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 6217309.*********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.024541260905342026, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.****************, 16.0, 123837.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * data_id-->[-Infinity, Infinity, 0.0, 16.0, 8.***************] ESTIMATE\n  |  \n  84:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5079: coalesce, LARGEINT, true] = [1555: data_id, LARGEINT, true]\n  |  output columns: 906, 908, 914, 916, 946, 1240, 1249, 1555\n  |  can local shuffle: false\n  |  cardinality: 6221692\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.004800120942044749, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 6217309.*********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.024541260905342026, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.****************, 16.0, 123837.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * user_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 5.***************] ESTIMATE\n  |  * data_id-->[-Infinity, Infinity, 0.0, 16.0, 8.***************] ESTIMATE\n  |  * coalesce-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  |----83:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 7\n  |    \n  66:Project\n  |  output columns:\n  |  906 <-> [906: vivo_source, VARCHAR(128), true]\n  |  908 <-> [908: imei1, VARCHAR(127), true]\n  |  914 <-> [914: customer_account_code, VARCHAR(128), true]\n  |  916 <-> [916: sku_code, VARCHAR(128), true]\n  |  946 <-> [946: superior_id, LARGEINT, true]\n  |  1240 <-> [1240: cast, VARCHAR(65533), true]\n  |  1249 <-> [1249: sales_area_id, LARGEINT, true]\n  |  5079 <-> coalesce[([946: superior_id, LARGEINT, true], [941: customer_id, LARGEINT, true]); args: LARGEINT; result: LARGEINT; args nullable: true; result nullable: true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 6221692\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.004800120942044749, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 6217309.*********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.024541260905342026, 586.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.****************, 16.0, 123837.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * coalesce-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  65:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5080: coalesce, LARGEINT, true] = [5081: cast, LARGEINT, true]\n  |  output columns: 906, 908, 914, 916, 941, 946, 1240, 1249\n  |  can local shuffle: false\n  |  cardinality: 6221692\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.004800120942044749, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 6217309.*********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.024541260905342026, 586.0] ESTIMATE\n  |  * customer_id-->[10001.0, 2.02404220000479136E17, 4.029215844044579E-6, 16.0, 106760.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.****************, 16.0, 123837.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * coalesce-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * coalesce-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----64:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 229990\n  |    \n  41:Project\n  |  output columns:\n  |  906 <-> [906: vivo_source, VARCHAR(128), true]\n  |  908 <-> [908: imei1, VARCHAR(127), true]\n  |  914 <-> [914: customer_account_code, VARCHAR(128), true]\n  |  916 <-> [916: sku_code, VARCHAR(128), true]\n  |  941 <-> [941: customer_id, LARGEINT, true]\n  |  946 <-> [946: superior_id, LARGEINT, true]\n  |  1240 <-> [1240: cast, VARCHAR(65533), true]\n  |  1249 <-> [1249: sales_area_id, LARGEINT, true]\n  |  5080 <-> coalesce[([946: superior_id, LARGEINT, true], [941: customer_id, LARGEINT, true]); args: LARGEINT; result: LARGEINT; args nullable: true; result nullable: true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 6221692\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.004800120942044749, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 6217309.*********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.024541260905342026, 586.0] ESTIMATE\n  |  * customer_id-->[10001.0, 2.02404220000479136E17, 4.029215844044579E-6, 16.0, 106760.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.****************, 16.0, 123837.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * coalesce-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  40:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5089: coalesce, LARGEINT, true] = [1239: cast, LARGEINT, true]\n  |  output columns: 906, 908, 914, 916, 941, 946, 1240, 1249\n  |  can local shuffle: false\n  |  cardinality: 6221692\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.004800120942044749, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 6217309.*********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.024541260905342026, 586.0] ESTIMATE\n  |  * customer_id-->[10001.0, 2.02404220000479136E17, 4.029215844044579E-6, 16.0, 106760.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.****************, 16.0, 123837.0] ESTIMATE\n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * coalesce-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * coalesce-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  |----39:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 291333\n  |    \n  18:Project\n  |  output columns:\n  |  906 <-> [906: vivo_source, VARCHAR(128), true]\n  |  908 <-> [908: imei1, VARCHAR(127), true]\n  |  914 <-> [914: customer_account_code, VARCHAR(128), true]\n  |  916 <-> [916: sku_code, VARCHAR(128), true]\n  |  941 <-> [941: customer_id, LARGEINT, true]\n  |  946 <-> [946: superior_id, LARGEINT, true]\n  |  5089 <-> coalesce[([946: superior_id, LARGEINT, true], [941: customer_id, LARGEINT, true]); args: LARGEINT; result: LARGEINT; args nullable: true; result nullable: true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 6221692\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.004800120942044749, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 6217309.*********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.024541260905342026, 586.0] ESTIMATE\n  |  * customer_id-->[10001.0, 2.02404220000479136E17, 4.029215844044579E-6, 16.0, 106760.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.****************, 16.0, 123837.0] ESTIMATE\n  |  * coalesce-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  17:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [915: customer_warehouse_code, VARCHAR(128), true] = [938: warehouse_code, VARCHAR, true]\n  |  output columns: 906, 908, 914, 916, 941, 946\n  |  can local shuffle: false\n  |  cardinality: 6221692\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.004800120942044749, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 6217309.*********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 0.****************, 18343.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.024541260905342026, 586.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 4.029215844044579E-6, 2.0, 2.0] ESTIMATE\n  |  * warehouse_code-->[-Infinity, Infinity, 8.058415453549726E-6, 11.***************, 220610.***********] ESTIMATE\n  |  * customer_id-->[10001.0, 2.02404220000479136E17, 4.029215844044579E-6, 16.0, 106760.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.****************, 16.0, 123837.0] ESTIMATE\n  |  * status-->[3.0, 6.0, 4.029215844044579E-6, 1.0, 2.0] ESTIMATE\n  |  * deleted_flag-->[0.0, 0.0, 4.029215844044579E-6, 1.0, 2.0] ESTIMATE\n  |  * coalesce-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  |----16:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 220611\n  |    \n  1:UNION\n  |  output exprs:\n  |      [906, VARCHAR(128), true] | [908, VARCHAR(127), true] | [914, VARCHAR(128), true] | [915, VARCHAR(128), true] | [916, VARCHAR(128), true]\n  |  child exprs:\n  |      [754: vivo_source, VARCHAR, false] | [753: imei1, VARCHAR, false] | [763: customer_account_code, VARCHAR, true] | [764: customer_warehouse_code, VARCHAR, true] | [765: sku_code, VARCHAR, true]\n  |      [803: vivo_source, VARCHAR, true] | [802: imei1, VARCHAR, true] | [813: customer_account_code, VARCHAR, true] | [814: customer_warehouse_code, VARCHAR, true] | [815: sku_code, VARCHAR, true]\n  |  pass-through-operands: all\n  |  hasNullableGenerateChild: true\n  |  cardinality: 6221692\n  |  column statistics: \n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 0.004800120942044749, 2.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.***************, 6217309.*********] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.****************, 11059.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 0.****************, 18343.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.024541260905342026, 586.0] ESTIMATE\n  |  \n  |----13:EXCHANGE\n  |       partition exprs: [802: imei1, VARCHAR, false], [803: vivo_source, VARCHAR, false]\n  |       cardinality: 4382\n  |    \n  4:EXCHANGE\n     cardinality: 6217310\n\nPLAN FRAGMENT 92(F91)\n\n  Input Partition: HASH_PARTITIONED: 1558: branch_id, 1574: status\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 196\n\n  195:AGGREGATE (merge finalize)\n  |  group by: [1558: branch_id, LARGEINT, true], [1574: status, TINYINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 170946.***********] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  \n  194:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [1558: branch_id, LARGEINT, true], [1574: status, TINYINT, true]\n     cardinality: 170946\n\nPLAN FRAGMENT 93(F38)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1558: branch_id, 1574: status\n  OutPut Exchange Id: 194\n\n  193:AGGREGATE (update serialize)\n  |  STREAMING\n  |  group by: [1558: branch_id, LARGEINT, true], [1574: status, TINYINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 170946.***********] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  \n  192:Project\n  |  output columns:\n  |  1558 <-> [1558: branch_id, LARGEINT, true]\n  |  1574 <-> [1574: status, TINYINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 170946.***********] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  \n  191:HASH JOIN\n  |  join op: LEFT SEMI JOIN (BROADCAST)\n  |  equal join conjunct: [1577: path, VARCHAR, true] = [2305: sales_area_path, VARCHAR(200), true]\n  |  build runtime filters:\n  |  - filter_id = 15, build_expr = (2305: sales_area_path), remote = false\n  |  output columns: 1558, 1574\n  |  can local shuffle: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 170946.***********] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  * path-->[-Infinity, Infinity, 0.0, 17.***************, 170946.***********] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163323, 170946.***********] ESTIMATE\n  |  \n  |----190:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 170946\n  |    \n  87:Project\n  |  output columns:\n  |  1558 <-> [1558: branch_id, LARGEINT, true]\n  |  1574 <-> [1574: status, TINYINT, true]\n  |  1577 <-> [1577: path, VARCHAR, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  * path-->[-Infinity, Infinity, 0.0, 17.***************, 185512.5] ESTIMATE\n  |  \n  86:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: [1575: deleted_flag, TINYINT, true] = 0, 1574: status IN (3, 6), 1557: op IN ('+U', '+I')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=37.510532\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 15, probe_expr = (1577: path)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * path-->[-Infinity, Infinity, 0.0, 17.***************, 185512.5] ESTIMATE\n\nPLAN FRAGMENT 94(F89)\n\n  Input Partition: HASH_PARTITIONED: 2305: sales_area_path\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 190\n\n  189:AGGREGATE (merge finalize)\n  |  group by: [2305: sales_area_path, VARCHAR(200), true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163323, 170946.***********] ESTIMATE\n  |  \n  188:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [2305: sales_area_path, VARCHAR(200), true]\n     cardinality: 170946\n\nPLAN FRAGMENT 95(F39)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 2305: sales_area_path\n  OutPut Exchange Id: 188\n\n  187:AGGREGATE (update serialize)\n  |  STREAMING\n  |  group by: [2305: sales_area_path, VARCHAR(200), true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 170946\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163323, 170946.***********] ESTIMATE\n  |  \n  88:UNION\n  |  output exprs:\n  |      [2305, VARCHAR(200), true]\n  |  child exprs:\n  |      [1654: sales_area_path, VARCHAR, true]\n  |      [1755: sales_area_path, VARCHAR, true]\n  |      [1856: sales_area_path, VARCHAR, true]\n  |      [1957: sales_area_path, VARCHAR, true]\n  |      [2058: sales_area_path, VARCHAR, true]\n  |      [2159: sales_area_path, VARCHAR, true]\n  |      [2260: sales_area_path, VARCHAR, true]\n  |  pass-through-operands: all\n  |  hasNullableGenerateChild: true\n  |  cardinality: 179581\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163323, 170946.***********] ESTIMATE\n  |  \n  |----116:EXCHANGE\n  |       partition exprs: [1710: branch_id, LARGEINT, true]\n  |       cardinality: 6391\n  |    \n  |----130:EXCHANGE\n  |       partition exprs: [1811: branch_id, LARGEINT, true]\n  |       cardinality: 518\n  |    \n  |----144:EXCHANGE\n  |       partition exprs: [1912: branch_id, LARGEINT, true]\n  |       cardinality: 97\n  |    \n  |----158:EXCHANGE\n  |       partition exprs: [2013: branch_id, LARGEINT, true]\n  |       cardinality: 25\n  |    \n  |----172:EXCHANGE\n  |       partition exprs: [2114: branch_id, LARGEINT, true]\n  |       cardinality: 2\n  |    \n  |----186:EXCHANGE\n  |       partition exprs: [2215: branch_id, LARGEINT, true]\n  |       cardinality: 1\n  |    \n  102:EXCHANGE\n     partition exprs: [1609: branch_id, LARGEINT, true]\n     cardinality: 172546\n\nPLAN FRAGMENT 96(F86)\n\n  Input Partition: HASH_PARTITIONED: 2215: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 186\n\n  185:Project\n  |  output columns:\n  |  2260 <-> [2260: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 1.002808214364868] ESTIMATE\n  |  \n  184:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [2208: sales_area_id, LARGEINT, true] = [5072: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 14, build_expr = (5072: cast), remote = true\n  |  output columns: 2260\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.02101150000015584E17, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 1.002808214364868] ESTIMATE\n  |  * cast-->[2.02101150000015584E17, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----183:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5072: cast, LARGEINT, true]\n  |       cardinality: 88714\n  |    \n  180:Project\n  |  output columns:\n  |  2208 <-> [2208: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  179:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [2215: branch_id, LARGEINT, true] = [2208: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 13, build_expr = (2208: sales_area_id), remote = true\n  |  output columns: 2208\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----178:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [2208: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 14, probe_expr = (2208: sales_area_id)\n  |    \n  175:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [2215: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 97(F87)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5072: cast\n  OutPut Exchange Id: 183\n\n  182:Project\n  |  output columns:\n  |  2260 <-> [2260: sales_area_path, VARCHAR, true]\n  |  5072 <-> cast([2298: sales_unit_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 88714\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 88713.99999999999] ESTIMATE\n  |  * cast-->[2.02101150000015584E17, 2.0240422000047728E17, 0.0, 16.0, 88713.99999999999] ESTIMATE\n  |  \n  181:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 2260: sales_area_path IS NOT NULL, CAST(2298: sales_unit_id AS LARGEINT) IS NOT NULL, 2266: op IN ('+I', '+U'), [2265: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 88714\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 88713.99999999999] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * sales_unit_id-->[2.02101150000015584E17, 2.0240422000047728E17, 0.0, 8.0, 88713.99999999999] ESTIMATE\n     * cast-->[2.02101150000015584E17, 2.0240422000047728E17, 0.0, 16.0, 88713.99999999999] ESTIMATE\n\nPLAN FRAGMENT 98(F84)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 2208: sales_area_id\n  OutPut Exchange Id: 178\n\n  177:Project\n  |  output columns:\n  |  2208 <-> [2208: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  176:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [2206: master_id, LARGEINT, true] = 202108100063214891, [2210: type, TINYINT, true] = 0, [2212: delete_flag, TINYINT, false] = 0, 2205: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     probe runtime filters:\n     - filter_id = 14, probe_expr = (2208: sales_area_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 99(F82)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 2215: branch_id\n  OutPut Exchange Id: 175\n\n  174:Project\n  |  output columns:\n  |  2215 <-> [2215: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  173:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 2215: branch_id IS NOT NULL, [2232: deleted_flag, TINYINT, true] = 0, 2231: status IN (3, 6), 2214: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 13, probe_expr = (2215: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 100(F79)\n\n  Input Partition: HASH_PARTITIONED: 2114: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 172\n\n  171:Project\n  |  output columns:\n  |  2159 <-> [2159: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 2\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 2.1275695378384345] ESTIMATE\n  |  \n  170:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [2107: sales_area_id, LARGEINT, true] = [5073: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 12, build_expr = (5073: cast), remote = false\n  |  output columns: 2159\n  |  can local shuffle: false\n  |  cardinality: 2\n  |  column statistics: \n  |  * sales_area_id-->[2.02101150000001408E17, 2.02404220000477088E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 2.1275695378384345] ESTIMATE\n  |  * cast-->[2.02101150000001408E17, 2.02404220000477088E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----169:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5073: cast, LARGEINT, true]\n  |       cardinality: 163031\n  |    \n  166:Project\n  |  output columns:\n  |  2107 <-> [2107: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  165:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [2114: branch_id, LARGEINT, true] = [2107: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 11, build_expr = (2107: sales_area_id), remote = true\n  |  output columns: 2107\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----164:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [2107: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 12, probe_expr = (2107: sales_area_id)\n  |    \n  161:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [2114: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 101(F80)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5073: cast\n  OutPut Exchange Id: 169\n\n  168:Project\n  |  output columns:\n  |  2159 <-> [2159: sales_area_path, VARCHAR, true]\n  |  5073 <-> cast([2193: town_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 163031\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 163031.0] ESTIMATE\n  |  * cast-->[2.02101150000001408E17, 2.02404220000477088E17, 0.0, 16.0, 76843.0] ESTIMATE\n  |  \n  167:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 2159: sales_area_path IS NOT NULL, CAST(2193: town_id AS LARGEINT) IS NOT NULL, 2165: op IN ('+I', '+U'), [2164: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 163031\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 163031.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * town_id-->[2.02101150000001408E17, 2.02404220000477088E17, 0.0, 8.0, 76843.0] ESTIMATE\n     * cast-->[2.02101150000001408E17, 2.02404220000477088E17, 0.0, 16.0, 76843.0] ESTIMATE\n\nPLAN FRAGMENT 102(F77)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 2107: sales_area_id\n  OutPut Exchange Id: 164\n\n  163:Project\n  |  output columns:\n  |  2107 <-> [2107: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  162:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [2105: master_id, LARGEINT, true] = 202108100063214891, [2109: type, TINYINT, true] = 0, [2111: delete_flag, TINYINT, false] = 0, 2104: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 103(F75)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 2114: branch_id\n  OutPut Exchange Id: 161\n\n  160:Project\n  |  output columns:\n  |  2114 <-> [2114: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  159:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 2114: branch_id IS NOT NULL, [2131: deleted_flag, TINYINT, true] = 0, 2130: status IN (3, 6), 2113: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 11, probe_expr = (2114: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 104(F72)\n\n  Input Partition: HASH_PARTITIONED: 2013: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 158\n\n  157:Project\n  |  output columns:\n  |  2058 <-> [2058: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 25\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 25.326926763547505] ESTIMATE\n  |  \n  156:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [2006: sales_area_id, LARGEINT, true] = [5074: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 10, build_expr = (5074: cast), remote = true\n  |  output columns: 2058\n  |  can local shuffle: false\n  |  cardinality: 25\n  |  column statistics: \n  |  * sales_area_id-->[2.02101150000001376E17, 2.02404130000135936E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 25.326926763547505] ESTIMATE\n  |  * cast-->[2.02101150000001376E17, 2.02404130000135936E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----155:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5074: cast, LARGEINT, true]\n  |       cardinality: 80996\n  |    \n  152:Project\n  |  output columns:\n  |  2006 <-> [2006: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  151:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [2013: branch_id, LARGEINT, true] = [2006: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 9, build_expr = (2006: sales_area_id), remote = true\n  |  output columns: 2006\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----150:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [2006: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 10, probe_expr = (2006: sales_area_id)\n  |    \n  147:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [2013: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 105(F73)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5074: cast\n  OutPut Exchange Id: 155\n\n  154:Project\n  |  output columns:\n  |  2058 <-> [2058: sales_area_path, VARCHAR, true]\n  |  5074 <-> cast([2088: second_theater_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 80996\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 80996.0] ESTIMATE\n  |  * cast-->[2.02101150000001376E17, 2.02404130000135936E17, 0.0, 16.0, 3207.0] ESTIMATE\n  |  \n  153:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 2058: sales_area_path IS NOT NULL, CAST(2088: second_theater_id AS LARGEINT) IS NOT NULL, 2064: op IN ('+I', '+U'), [2063: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 80996\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 80996.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * second_theater_id-->[2.02101150000001376E17, 2.02404130000135936E17, 0.0, 8.0, 3207.0] ESTIMATE\n     * cast-->[2.02101150000001376E17, 2.02404130000135936E17, 0.0, 16.0, 3207.0] ESTIMATE\n\nPLAN FRAGMENT 106(F70)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 2006: sales_area_id\n  OutPut Exchange Id: 150\n\n  149:Project\n  |  output columns:\n  |  2006 <-> [2006: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  148:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [2004: master_id, LARGEINT, true] = 202108100063214891, [2008: type, TINYINT, true] = 0, [2010: delete_flag, TINYINT, false] = 0, 2003: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     probe runtime filters:\n     - filter_id = 10, probe_expr = (2006: sales_area_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 107(F68)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 2013: branch_id\n  OutPut Exchange Id: 147\n\n  146:Project\n  |  output columns:\n  |  2013 <-> [2013: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  145:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 2013: branch_id IS NOT NULL, [2030: deleted_flag, TINYINT, true] = 0, 2029: status IN (3, 6), 2012: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 9, probe_expr = (2013: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 108(F65)\n\n  Input Partition: HASH_PARTITIONED: 1912: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 144\n\n  143:Project\n  |  output columns:\n  |  1957 <-> [1957: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 97\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 97.**************] ESTIMATE\n  |  \n  142:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [1905: sales_area_id, LARGEINT, true] = [5075: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 8, build_expr = (5075: cast), remote = false\n  |  output columns: 1957\n  |  can local shuffle: false\n  |  cardinality: 97\n  |  column statistics: \n  |  * sales_area_id-->[2.02101150000001152E17, 2.02404220000359744E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 97.**************] ESTIMATE\n  |  * cast-->[2.02101150000001152E17, 2.02404220000359744E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----141:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5075: cast, LARGEINT, true]\n  |       cardinality: 161920\n  |    \n  138:Project\n  |  output columns:\n  |  1905 <-> [1905: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  137:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [1912: branch_id, LARGEINT, true] = [1905: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 7, build_expr = (1905: sales_area_id), remote = true\n  |  output columns: 1905\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----136:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [1905: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 8, probe_expr = (1905: sales_area_id)\n  |    \n  133:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [1912: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 109(F66)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5075: cast\n  OutPut Exchange Id: 141\n\n  140:Project\n  |  output columns:\n  |  1957 <-> [1957: sales_area_path, VARCHAR, true]\n  |  5075 <-> cast([1983: second_area_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 161920\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 161920.0] ESTIMATE\n  |  * cast-->[2.02101150000001152E17, 2.02404220000359744E17, 0.0, 16.0, 1667.0] ESTIMATE\n  |  \n  139:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 1957: sales_area_path IS NOT NULL, CAST(1983: second_area_id AS LARGEINT) IS NOT NULL, 1963: op IN ('+I', '+U'), [1962: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 161920\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 161920.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * second_area_id-->[2.02101150000001152E17, 2.02404220000359744E17, 0.0, 8.0, 1667.0] ESTIMATE\n     * cast-->[2.02101150000001152E17, 2.02404220000359744E17, 0.0, 16.0, 1667.0] ESTIMATE\n\nPLAN FRAGMENT 110(F63)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1905: sales_area_id\n  OutPut Exchange Id: 136\n\n  135:Project\n  |  output columns:\n  |  1905 <-> [1905: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  134:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [1903: master_id, LARGEINT, true] = 202108100063214891, [1907: type, TINYINT, true] = 0, [1909: delete_flag, TINYINT, false] = 0, 1902: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 111(F61)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1912: branch_id\n  OutPut Exchange Id: 133\n\n  132:Project\n  |  output columns:\n  |  1912 <-> [1912: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  131:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 1912: branch_id IS NOT NULL, [1929: deleted_flag, TINYINT, true] = 0, 1928: status IN (3, 6), 1911: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 7, probe_expr = (1912: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 112(F58)\n\n  Input Partition: HASH_PARTITIONED: 1811: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 130\n\n  129:Project\n  |  output columns:\n  |  1856 <-> [1856: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 518\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 518.*************] ESTIMATE\n  |  \n  128:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [1804: sales_area_id, LARGEINT, true] = [5076: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 6, build_expr = (5076: cast), remote = false\n  |  output columns: 1856\n  |  can local shuffle: false\n  |  cardinality: 518\n  |  column statistics: \n  |  * sales_area_id-->[2.0210115000000112E17, 2.02402240000502304E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 518.*************] ESTIMATE\n  |  * cast-->[2.0210115000000112E17, 2.02402240000502304E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----127:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5076: cast, LARGEINT, true]\n  |       cardinality: 166993\n  |    \n  124:Project\n  |  output columns:\n  |  1804 <-> [1804: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  123:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [1811: branch_id, LARGEINT, true] = [1804: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 5, build_expr = (1804: sales_area_id), remote = true\n  |  output columns: 1804\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----122:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [1804: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 6, probe_expr = (1804: sales_area_id)\n  |    \n  119:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [1811: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 113(F59)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5076: cast\n  OutPut Exchange Id: 127\n\n  126:Project\n  |  output columns:\n  |  1856 <-> [1856: sales_area_path, VARCHAR, true]\n  |  5076 <-> cast([1878: first_theater_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 166993\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 166993.0] ESTIMATE\n  |  * cast-->[2.0210115000000112E17, 2.02402240000502304E17, 0.0, 16.0, 323.0] ESTIMATE\n  |  \n  125:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 1856: sales_area_path IS NOT NULL, CAST(1878: first_theater_id AS LARGEINT) IS NOT NULL, 1862: op IN ('+I', '+U'), [1861: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 166993\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 166993.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * first_theater_id-->[2.0210115000000112E17, 2.02402240000502304E17, 0.0, 8.0, 323.0] ESTIMATE\n     * cast-->[2.0210115000000112E17, 2.02402240000502304E17, 0.0, 16.0, 323.0] ESTIMATE\n\nPLAN FRAGMENT 114(F56)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1804: sales_area_id\n  OutPut Exchange Id: 122\n\n  121:Project\n  |  output columns:\n  |  1804 <-> [1804: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  120:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [1802: master_id, LARGEINT, true] = 202108100063214891, [1806: type, TINYINT, true] = 0, [1808: delete_flag, TINYINT, false] = 0, 1801: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 115(F54)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1811: branch_id\n  OutPut Exchange Id: 119\n\n  118:Project\n  |  output columns:\n  |  1811 <-> [1811: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  117:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 1811: branch_id IS NOT NULL, [1828: deleted_flag, TINYINT, true] = 0, 1827: status IN (3, 6), 1810: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 5, probe_expr = (1811: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 116(F51)\n\n  Input Partition: HASH_PARTITIONED: 1710: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 116\n\n  115:Project\n  |  output columns:\n  |  1755 <-> [1755: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 6391\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 6390.562480742515] ESTIMATE\n  |  \n  114:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [1703: sales_area_id, LARGEINT, true] = [5077: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 4, build_expr = (5077: cast), remote = false\n  |  output columns: 1755\n  |  can local shuffle: false\n  |  cardinality: 6391\n  |  column statistics: \n  |  * sales_area_id-->[2.02101150000000032E17, 2.02403190000280864E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 6390.562480742515] ESTIMATE\n  |  * cast-->[2.02101150000000032E17, 2.02403190000280864E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----113:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5077: cast, LARGEINT, true]\n  |       cardinality: 172062\n  |    \n  110:Project\n  |  output columns:\n  |  1703 <-> [1703: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  109:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [1710: branch_id, LARGEINT, true] = [1703: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 3, build_expr = (1703: sales_area_id), remote = true\n  |  output columns: 1703\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----108:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [1703: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 4, probe_expr = (1703: sales_area_id)\n  |    \n  105:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [1710: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 117(F52)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5077: cast\n  OutPut Exchange Id: 113\n\n  112:Project\n  |  output columns:\n  |  1755 <-> [1755: sales_area_path, VARCHAR, true]\n  |  5077 <-> cast([1773: first_area_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 172062\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n  |  * cast-->[2.02101150000000032E17, 2.02403190000280864E17, 0.0, 16.0, 27.0] ESTIMATE\n  |  \n  111:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 1755: sales_area_path IS NOT NULL, CAST(1773: first_area_id AS LARGEINT) IS NOT NULL, 1761: op IN ('+I', '+U'), [1760: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 172062\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * first_area_id-->[2.02101150000000032E17, 2.02403190000280864E17, 0.0, 8.0, 27.0] ESTIMATE\n     * cast-->[2.02101150000000032E17, 2.02403190000280864E17, 0.0, 16.0, 27.0] ESTIMATE\n\nPLAN FRAGMENT 118(F49)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1703: sales_area_id\n  OutPut Exchange Id: 108\n\n  107:Project\n  |  output columns:\n  |  1703 <-> [1703: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  106:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [1701: master_id, LARGEINT, true] = 202108100063214891, [1705: type, TINYINT, true] = 0, [1707: delete_flag, TINYINT, false] = 0, 1700: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 119(F47)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1710: branch_id\n  OutPut Exchange Id: 105\n\n  104:Project\n  |  output columns:\n  |  1710 <-> [1710: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  103:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 1710: branch_id IS NOT NULL, [1727: deleted_flag, TINYINT, true] = 0, 1726: status IN (3, 6), 1709: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 3, probe_expr = (1710: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 120(F44)\n\n  Input Partition: HASH_PARTITIONED: 1609: branch_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 102\n\n  101:Project\n  |  output columns:\n  |  1654 <-> [1654: sales_area_path, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 172546\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n  |  \n  100:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE(S))\n  |  equal join conjunct: [1602: sales_area_id, LARGEINT, true] = [5078: cast, LARGEINT, true]\n  |  build runtime filters:\n  |  - filter_id = 2, build_expr = (5078: cast), remote = false\n  |  output columns: 1654\n  |  can local shuffle: false\n  |  cardinality: 172546\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n  |  * cast-->[2.01910110000001E14, 2.01910110000001E14, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----99:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [5078: cast, LARGEINT, true]\n  |       cardinality: 172063\n  |    \n  96:Project\n  |  output columns:\n  |  1602 <-> [1602: sales_area_id, LARGEINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  95:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [1609: branch_id, LARGEINT, true] = [1602: sales_area_id, LARGEINT, false]\n  |  build runtime filters:\n  |  - filter_id = 1, build_expr = (1602: sales_area_id), remote = true\n  |  output columns: 1602\n  |  can local shuffle: false\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  |----94:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [1602: sales_area_id, LARGEINT, false]\n  |       cardinality: 1\n  |       probe runtime filters:\n  |       - filter_id = 2, probe_expr = (1602: sales_area_id)\n  |    \n  91:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [1609: branch_id, LARGEINT, true]\n     cardinality: 185513\n\nPLAN FRAGMENT 121(F45)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 5078: cast\n  OutPut Exchange Id: 99\n\n  98:Project\n  |  output columns:\n  |  1654 <-> [1654: sales_area_path, VARCHAR, true]\n  |  5078 <-> cast([1661: sales_market_id, BIGINT, true] as LARGEINT)\n  |  cardinality: 172063\n  |  column statistics: \n  |  * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n  |  * cast-->[2.01910110000001E14, 2.01910110000001E14, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  97:OlapScanNode\n     table: dim_sales_area_chain, rollup: dim_sales_area_chain\n     preAggregation: on\n     Predicates: 1654: sales_area_path IS NOT NULL, CAST(1661: sales_market_id AS LARGEINT) IS NOT NULL, 1660: op IN ('+I', '+U'), [1659: deleted_flag, INT, false] = 0\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15914\n     actualRows=172070, avgRowSize=47.63239\n     cardinality: 172063\n     column statistics: \n     * sales_area_path-->[-Infinity, Infinity, 0.0, 17.63239046163324, 170946.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 4.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * sales_market_id-->[2.01910110000001E14, 2.01910110000001E14, 0.0, 8.0, 1.0] ESTIMATE\n     * cast-->[2.01910110000001E14, 2.01910110000001E14, 0.0, 16.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 122(F42)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1602: sales_area_id\n  OutPut Exchange Id: 94\n\n  93:Project\n  |  output columns:\n  |  1602 <-> [1602: sales_area_id, LARGEINT, false]\n  |  cardinality: 1\n  |  column statistics: \n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  92:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [1600: master_id, LARGEINT, true] = 202108100063214891, [1604: type, TINYINT, true] = 0, [1606: delete_flag, TINYINT, false] = 0, 1599: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=36.0\n     cardinality: 1\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * master_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 1.0] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 1.0] ESTIMATE\n     * type-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 123(F40)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1609: branch_id\n  OutPut Exchange Id: 91\n\n  90:Project\n  |  output columns:\n  |  1609 <-> [1609: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  89:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: 1609: branch_id IS NOT NULL, [1626: deleted_flag, TINYINT, true] = 0, 1625: status IN (3, 6), 1608: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     probe runtime filters:\n     - filter_id = 1, probe_expr = (1609: branch_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 124(F36)\n\n  Input Partition: HASH_PARTITIONED: 1554: user_id, 1555: data_id\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 83\n\n  82:AGGREGATE (merge finalize)\n  |  group by: [1554: user_id, LARGEINT, true], [1555: data_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 7\n  |  column statistics: \n  |  * user_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 5.***************] ESTIMATE\n  |  * data_id-->[-Infinity, Infinity, 0.0, 16.0, 8.***************] ESTIMATE\n  |  \n  81:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [1554: user_id, LARGEINT, true], [1555: data_id, LARGEINT, true]\n     cardinality: 7\n\nPLAN FRAGMENT 125(F29)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1554: user_id, 1555: data_id\n  OutPut Exchange Id: 81\n\n  80:AGGREGATE (update serialize)\n  |  STREAMING\n  |  group by: [1554: user_id, LARGEINT, true], [1555: data_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 7\n  |  column statistics: \n  |  * user_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 5.***************] ESTIMATE\n  |  * data_id-->[-Infinity, Infinity, 0.0, 16.0, 8.***************] ESTIMATE\n  |  \n  67:UNION\n  |  output exprs:\n  |      [1554, LARGEINT, true] | [1555, LARGEINT, true]\n  |  child exprs:\n  |      [1445: user_id, LARGEINT, true] | [1446: data_id, LARGEINT, true]\n  |      [1451: user_id, LARGEINT, true] | [1553: if, LARGEINT, true]\n  |  pass-through-operands: all\n  |  hasNullableGenerateChild: true\n  |  cardinality: 7\n  |  column statistics: \n  |  * user_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 5.***************] ESTIMATE\n  |  * data_id-->[-Infinity, Infinity, 0.0, 16.0, 8.***************] ESTIMATE\n  |  \n  |----79:EXCHANGE\n  |       partition exprs: [1464: superior_id, LARGEINT, true]\n  |       cardinality: 5\n  |    \n  70:EXCHANGE\n     cardinality: 2\n\nPLAN FRAGMENT 126(F35)\n\n  Input Partition: HASH_PARTITIONED: 1464: superior_id\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 79\n\n  78:Project\n  |  output columns:\n  |  1451 <-> [1451: user_id, LARGEINT, true]\n  |  1553 <-> if[([1453: data_type, TINYINT, true] = 2, [1452: data_id, LARGEINT, true], [1457: branch_id, LARGEINT, true]); args: BOOLEAN,LARGEINT,LARGEINT; result: LARGEINT; args nullable: true; result nullable: true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 5\n  |  column statistics: \n  |  * user_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 4.***************] ESTIMATE\n  |  * if-->[-Infinity, Infinity, 0.0, 16.0, 8.***************] ESTIMATE\n  |  \n  77:HASH JOIN\n  |  join op: RIGHT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [1464: superior_id, LARGEINT, true] = [1452: data_id, LARGEINT, true]\n  |  other join predicates: [1453: data_type, TINYINT, true] = 1\n  |  build runtime filters:\n  |  - filter_id = 0, build_expr = (1452: data_id), remote = true\n  |  output columns: 1451, 1452, 1453, 1457\n  |  can local shuffle: false\n  |  cardinality: 5\n  |  column statistics: \n  |  * user_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 4.***************] ESTIMATE\n  |  * data_id-->[2.02101160000004032E17, 2.0240422000036496E17, 0.0, 16.0, 4.***************] ESTIMATE\n  |  * data_type-->[1.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  * branch_id-->[2.02101160000110016E17, 2.0240513000048176E17, 0.0, 16.0, 4.***************] ESTIMATE\n  |  * superior_id-->[2.02101160000004032E17, 2.0240422000036496E17, 0.0, 16.0, 4.***************] ESTIMATE\n  |  * if-->[-Infinity, Infinity, 0.0, 16.0, 8.***************] ESTIMATE\n  |  \n  |----76:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [1452: data_id, LARGEINT, true]\n  |       cardinality: 5\n  |    \n  73:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [1464: superior_id, LARGEINT, true]\n     cardinality: 208006\n\nPLAN FRAGMENT 127(F33)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1452: data_id\n  OutPut Exchange Id: 76\n\n  75:Project\n  |  output columns:\n  |  1451 <-> [1451: user_id, LARGEINT, true]\n  |  1452 <-> [1452: data_id, LARGEINT, true]\n  |  1453 <-> [1453: data_type, TINYINT, true]\n  |  cardinality: 5\n  |  column statistics: \n  |  * user_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 4.586576824822755] ESTIMATE\n  |  * data_id-->[0.0, 2.0240422000036496E17, 0.0, 16.0, 4.586576824822755] ESTIMATE\n  |  * data_type-->[1.0, 2.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  \n  74:OlapScanNode\n     table: data_permission, rollup: data_permission\n     preAggregation: on\n     Predicates: [1451: user_id, LARGEINT, true] = 202108100063214891, 1450: op IN ('+I', '+U'), 1453: data_type IN (1, 2)\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16806\n     actualRows=2573390, avgRowSize=35.0\n     cardinality: 5\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * user_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 4.586576824822755] ESTIMATE\n     * data_id-->[0.0, 2.0240422000036496E17, 0.0, 16.0, 4.586576824822755] ESTIMATE\n     * data_type-->[1.0, 2.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 128(F31)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 1464: superior_id\n  OutPut Exchange Id: 73\n\n  72:Project\n  |  output columns:\n  |  1457 <-> [1457: branch_id, LARGEINT, false]\n  |  1464 <-> [1464: superior_id, LARGEINT, false]\n  |  cardinality: 208006\n  |  column statistics: \n  |  * branch_id-->[2.02101160000110016E17, 2.0240513000048176E17, 0.0, 16.0, 130431.0] ESTIMATE\n  |  * superior_id-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 16.0, 106980.0] ESTIMATE\n  |  \n  71:OlapScanNode\n     table: store, rollup: store\n     preAggregation: on\n     Predicates: [1489: delete_flag, TINYINT, false] = 0, 1484: status IN (3, 6), 1456: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=2/2\n     tabletList=16797,16798\n     actualRows=986567, avgRowSize=36.0\n     cardinality: 208006\n     probe runtime filters:\n     - filter_id = 0, probe_expr = (1464: superior_id)\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.02101160000110016E17, 2.0240513000048176E17, 0.0, 16.0, 130431.0] ESTIMATE\n     * superior_id-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 16.0, 106980.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 129(F30)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 70\n\n  69:Project\n  |  output columns:\n  |  1445 <-> [1445: user_id, LARGEINT, true]\n  |  1446 <-> [1446: data_id, LARGEINT, true]\n  |  cardinality: 2\n  |  column statistics: \n  |  * user_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 2.2932884124113775] ESTIMATE\n  |  * data_id-->[0.0, 2.0240422000036496E17, 0.0, 16.0, 2.2932884124113775] ESTIMATE\n  |  \n  68:OlapScanNode\n     table: data_permission, rollup: data_permission\n     preAggregation: on\n     Predicates: [1445: user_id, LARGEINT, true] = 202108100063214891, 1444: op IN ('+I', '+U'), [1447: data_type, TINYINT, true] = 1\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16806\n     actualRows=2573390, avgRowSize=35.0\n     cardinality: 2\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * user_id-->[2.0210810006321488E17, 2.0210810006321488E17, 0.0, 16.0, 2.2932884124113775] ESTIMATE\n     * data_id-->[0.0, 2.0240422000036496E17, 0.0, 16.0, 2.2932884124113775] ESTIMATE\n     * data_type-->[1.0, 1.0, 0.0, 1.0, 2.2932884124113775] ESTIMATE\n\nPLAN FRAGMENT 130(F19)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 64\n\n  63:Project\n  |  output columns:\n  |  5081 <-> cast([1296: customer_id, BIGINT, true] as LARGEINT)\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  \n  62:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5082: cast, VARCHAR(50), true] = [5083: cast, VARCHAR(50), true]\n  |  output columns: 1296\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_id-->[10001.0, 2.02404220000479872E17, 0.0, 8.0, 1.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.999995651984869, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.999995651984869, 1.0, 2.0] ESTIMATE\n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  |----61:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  58:Project\n  |  output columns:\n  |  1296 <-> [1296: customer_id, BIGINT, true]\n  |  5082 <-> cast([1342: district, BIGINT, true] as VARCHAR(50))\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_id-->[10001.0, 2.02404220000479872E17, 0.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  \n  57:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5084: cast, VARCHAR(50), true] = [5085: cast, VARCHAR(50), true]\n  |  output columns: 1296, 1342\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_id-->[10001.0, 2.02404220000479872E17, 0.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.999995651984869, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.999995651984869, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  |----56:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  53:Project\n  |  output columns:\n  |  1296 <-> [1296: customer_id, BIGINT, true]\n  |  1342 <-> [1342: district, BIGINT, true]\n  |  5084 <-> cast([1341: city, BIGINT, true] as VARCHAR(50))\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_id-->[10001.0, 2.02404220000479872E17, 0.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  \n  52:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5086: cast, VARCHAR(50), true] = [5087: cast, VARCHAR(50), true]\n  |  output columns: 1296, 1341, 1342\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_id-->[10001.0, 2.02404220000479872E17, 0.0, 8.0, 1.0] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * id-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.999995651984869, 2.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.999995651984869, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.999995651984869, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  |----51:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1109\n  |    \n  48:Project\n  |  output columns:\n  |  1296 <-> [1296: customer_id, BIGINT, false]\n  |  1341 <-> [1341: city, BIGINT, true]\n  |  1342 <-> [1342: district, BIGINT, true]\n  |  5086 <-> cast([1340: province, BIGINT, true] as VARCHAR(50))\n  |  hasNullableGenerateChild: true\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_id-->[10001.0, 2.02404220000479872E17, 0.0, 8.0, 173304.50986246436] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  \n  47:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [5088: cast, LARGEINT, false] = [1359: master_branch_id, LARGEINT, true]\n  |  output columns: 1296, 1340, 1341, 1342\n  |  can local shuffle: false\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_id-->[10001.0, 2.02404220000479872E17, 0.0, 8.0, 173304.50986246436] ESTIMATE\n  |  * province-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.2464693688314085, 2.0, 2.0] ESTIMATE\n  |  * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.37992224485821285, 16.0, 209577.4444444444] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.3798982750195003, 1.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.2464693688314085, 1.0, 2.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 173304.50986246436] ESTIMATE\n  |  \n  |----46:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 209577\n  |    \n  43:Project\n  |  output columns:\n  |  1296 <-> [1296: customer_id, BIGINT, false]\n  |  1340 <-> [1340: province, BIGINT, true]\n  |  1341 <-> [1341: city, BIGINT, true]\n  |  1342 <-> [1342: district, BIGINT, true]\n  |  5088 <-> cast([1296: customer_id, BIGINT, false] as LARGEINT)\n  |  cardinality: 229990\n  |  column statistics: \n  |  * customer_id-->[10001.0, 2.02404220000479872E17, 0.0, 8.0, 228870.0] ESTIMATE\n  |  * province-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 228870.0] ESTIMATE\n  |  \n  42:OlapScanNode\n     table: dim_customer_chain, rollup: dim_customer_chain\n     preAggregation: on\n     Predicates: 1345: op IN ('+I', '+U'), 1301: customer_status IN (3, 6), [1346: delete_flag, TINYINT, true] = 0\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=243024,243025,243026,243027,243028\n     actualRows=239125, avgRowSize=52.0\n     cardinality: 229990\n     column statistics: \n     * customer_id-->[10001.0, 2.02404220000479872E17, 0.0, 8.0, 228870.0] ESTIMATE\n     * customer_status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * province-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n     * city-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n     * district-->[-Infinity, Infinity, 1.0, 8.0, 1.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n     * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 228870.0] ESTIMATE\n\nPLAN FRAGMENT 131(F26)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 61\n\n  60:Project\n  |  output columns:\n  |  5083 <-> cast([1417: id, LARGEINT, false] as VARCHAR(50))\n  |  cardinality: 1109\n  |  column statistics: \n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  59:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [1431: delete_flag, TINYINT, true] = 0, 1418: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=35.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n\nPLAN FRAGMENT 132(F24)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 56\n\n  55:Project\n  |  output columns:\n  |  5085 <-> cast([1391: id, LARGEINT, false] as VARCHAR(50))\n  |  cardinality: 1109\n  |  column statistics: \n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  54:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [1405: delete_flag, TINYINT, true] = 0, 1392: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=35.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n\nPLAN FRAGMENT 133(F22)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 51\n\n  50:Project\n  |  output columns:\n  |  5087 <-> cast([1365: id, LARGEINT, false] as VARCHAR(50))\n  |  cardinality: 1109\n  |  column statistics: \n  |  * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n  |  \n  49:OlapScanNode\n     table: data_dictionary_type, rollup: data_dictionary_type\n     preAggregation: on\n     Predicates: [1379: delete_flag, TINYINT, true] = 0, 1366: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=16155\n     actualRows=3326, avgRowSize=35.0\n     cardinality: 1109\n     column statistics: \n     * id-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.01912345E8, 1.20469697139566182E18, 0.0, 16.0, 1108.6666666666665] ESTIMATE\n\nPLAN FRAGMENT 134(F20)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 46\n\n  45:Project\n  |  output columns:\n  |  1359 <-> [1359: master_branch_id, LARGEINT, true]\n  |  cardinality: 209577\n  |  column statistics: \n  |  * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.17710345207844144, 16.0, 209577.4444444444] ESTIMATE\n  |  \n  44:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [1364: delete_flag, TINYINT, false] = 0, 1363: status IN (3, 6), 1357: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=20.0\n     cardinality: 209577\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.17710345207844144, 16.0, 209577.4444444444] ESTIMATE\n     * status-->[3.0, 6.0, 0.17707164203951126, 1.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 135(F10)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 39\n\n  38:Project\n  |  output columns:\n  |  1239 <-> [1239: cast, LARGEINT, true]\n  |  1240 <-> [1240: cast, VARCHAR(65533), true]\n  |  1249 <-> [1249: sales_area_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 291333\n  |  column statistics: \n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  \n  37:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [1249: sales_area_id, LARGEINT, true] = [1256: branch_id, LARGEINT, true]\n  |  output columns: 1239, 1240, 1249\n  |  can local shuffle: false\n  |  cardinality: 291333\n  |  column statistics: \n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.514140165782806, 2.0, 2.0] ESTIMATE\n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.514140165782806, 16.0, 184993.0] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.514140165782806, 1.0, 2.0] ESTIMATE\n  |  * deleted_flag-->[0.0, 0.0, 0.514140165782806, 1.0, 2.0] ESTIMATE\n  |  \n  |----36:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 185513\n  |    \n  33:Project\n  |  output columns:\n  |  1239 <-> [1239: cast, LARGEINT, true]\n  |  1240 <-> [1240: cast, VARCHAR(65533), true]\n  |  1249 <-> [1249: sales_area_id, LARGEINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 291333\n  |  column statistics: \n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.****************, 16.0, 125746.***********] ESTIMATE\n  |  \n  32:HASH JOIN\n  |  join op: LEFT OUTER JOIN (BROADCAST)\n  |  equal join conjunct: [1239: cast, LARGEINT, true] = [1248: master_branch_id, LARGEINT, true]\n  |  output columns: 1239, 1240, 1249\n  |  can local shuffle: false\n  |  cardinality: 291333\n  |  column statistics: \n  |  * cast-->[10001.0, 2.02404220000479872E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.****************, 2.0, 2.0] ESTIMATE\n  |  * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.6013072344004559, 16.0, 125746.***********] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.****************, 16.0, 125746.***********] ESTIMATE\n  |  * type-->[1.0, 3.0, 0.****************, 1.0, 3.0] ESTIMATE\n  |  * status-->[3.0, 6.0, 0.6012918224602466, 1.0, 2.0] ESTIMATE\n  |  * delete_flag-->[0.0, 0.0, 0.****************, 1.0, 2.0] ESTIMATE\n  |  \n  |----31:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 125746\n  |    \n  19:UNION\n  |  output exprs:\n  |      [1239, LARGEINT, true] | [1240, VARCHAR(65533), true]\n  |  child exprs:\n  |      [1056: cast, LARGEINT, true] | [1057: cast, VARCHAR, true]\n  |      [1135: cast, LARGEINT, true] | [1136: cast, VARCHAR, true]\n  |      [1141: branch_id, LARGEINT, false] | [1142: store_code, VARCHAR, true]\n  |  pass-through-operands: all\n  |  cardinality: 291333\n  |  column statistics: \n  |  * cast-->[10001.0, 2.0240513000048176E17, 0.0, 16.0, 213573.7711807576] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.0083912338307481E-4, 9.***************, 130558.0] ESTIMATE\n  |  \n  |----25:EXCHANGE\n  |       cardinality: 4034\n  |    \n  |----28:EXCHANGE\n  |       cardinality: 208006\n  |    \n  22:EXCHANGE\n     cardinality: 79293\n\nPLAN FRAGMENT 136(F16)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 36\n\n  35:Project\n  |  output columns:\n  |  1256 <-> [1256: branch_id, LARGEINT, true]\n  |  cardinality: 185513\n  |  column statistics: \n  |  * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n  |  \n  34:OlapScanNode\n     table: sales_area, rollup: sales_area\n     preAggregation: on\n     Predicates: [1273: deleted_flag, TINYINT, true] = 0, 1272: status IN (3, 6), 1255: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=5/5\n     tabletList=15922,15923,15924,15925,15926\n     actualRows=371030, avgRowSize=20.0\n     cardinality: 185513\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 184993.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 137(F14)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 31\n\n  30:Project\n  |  output columns:\n  |  1248 <-> [1248: master_branch_id, LARGEINT, true]\n  |  1249 <-> [1249: sales_area_id, LARGEINT, false]\n  |  cardinality: 125746\n  |  column statistics: \n  |  * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.17710345207844144, 16.0, 125746.***********] ESTIMATE\n  |  * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n  |  \n  29:OlapScanNode\n     table: sales_area_link, rollup: sales_area_link\n     preAggregation: on\n     Predicates: [1253: delete_flag, TINYINT, false] = 0, 1252: status IN (3, 6), 1251: type IN (1, 2, 3), 1246: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=1/1\n     tabletList=15934\n     actualRows=1889361, avgRowSize=37.0\n     cardinality: 125746\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * master_branch_id-->[10001.0, 2.02404220000479872E17, 0.17710345207844144, 16.0, 125746.***********] ESTIMATE\n     * sales_area_id-->[2.01910110000001E14, 2.0240422000047728E17, 0.0, 16.0, 125746.***********] ESTIMATE\n     * type-->[1.0, 3.0, 0.0, 1.0, 3.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.17707164203951126, 1.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 138(F13)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 28\n\n  27:Project\n  |  output columns:\n  |  1141 <-> [1141: branch_id, LARGEINT, false]\n  |  1142 <-> [1142: store_code, VARCHAR, true]\n  |  cardinality: 208006\n  |  column statistics: \n  |  * branch_id-->[2.02101160000110016E17, 2.0240513000048176E17, 0.0, 16.0, 130431.0] ESTIMATE\n  |  * store_code-->[-Infinity, Infinity, 6.623740554492552E-5, 9.180534322193697, 130558.0] ESTIMATE\n  |  \n  26:OlapScanNode\n     table: store, rollup: store\n     preAggregation: on\n     Predicates: [1173: delete_flag, TINYINT, false] = 0, 1168: status IN (3, 6), 1140: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=2/2\n     tabletList=16797,16798\n     actualRows=986567, avgRowSize=29.180534\n     cardinality: 208006\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.02101160000110016E17, 2.0240513000048176E17, 0.0, 16.0, 130431.0] ESTIMATE\n     * store_code-->[-Infinity, Infinity, 6.623740554492552E-5, 9.180534322193697, 130558.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 139(F12)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 25\n\n  24:Project\n  |  output columns:\n  |  1135 <-> cast([1062: branch_id, BIGINT, true] as LARGEINT)\n  |  1136 <-> [1063: number, VARCHAR, true]\n  |  cardinality: 4034\n  |  column statistics: \n  |  * cast-->[10001.0, 2.02405100000051104E17, 0.0, 16.0, 3981.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 4.957448566471123E-4, 8.***************, 4020.0] ESTIMATE\n  |  \n  23:OlapScanNode\n     table: agency, rollup: agency\n     preAggregation: on\n     Predicates: [1098: delete_flag, TINYINT, true] = 0, 1094: status IN (3, 6), 1061: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=16/16\n     tabletList=16751,16752,16753,16754,16755,16756,16757,16758,16759,16760 ...\n     actualRows=12132, avgRowSize=44.27613\n     cardinality: 4034\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[10001.0, 2.02405100000051104E17, 0.0, 8.0, 3981.0] ESTIMATE\n     * number-->[-Infinity, Infinity, 4.957448566471123E-4, 8.***************, 4020.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[10001.0, 2.02405100000051104E17, 0.0, 16.0, 3981.0] ESTIMATE\n     * cast-->[-Infinity, Infinity, 4.957448566471123E-4, 8.***************, 4020.0] ESTIMATE\n\nPLAN FRAGMENT 140(F11)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 22\n\n  21:Project\n  |  output columns:\n  |  1056 <-> cast([977: branch_id, BIGINT, true] as LARGEINT)\n  |  1057 <-> [978: number, VARCHAR, true]\n  |  cardinality: 79293\n  |  column statistics: \n  |  * cast-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 16.0, 79292.8] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 1.7151620323661165E-4, 9.***************, 79292.8] ESTIMATE\n  |  \n  20:OlapScanNode\n     table: retailer, rollup: retailer\n     preAggregation: on\n     Predicates: [1007: delete_flag, TINYINT, true] = 0, 1013: status IN (3, 6), 976: op IN ('+I', '+U')\n     partitionsRatio=1/1, tabletsRatio=16/16\n     tabletList=16770,16771,16772,16773,16774,16775,16776,16777,16778,16779 ...\n     actualRows=601020, avgRowSize=46.363583\n     cardinality: 79293\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * branch_id-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 8.0, 79292.8] ESTIMATE\n     * number-->[-Infinity, Infinity, 1.7151620323661165E-4, 9.***************, 79292.8] ESTIMATE\n     * delete_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * cast-->[2.02101160000004032E17, 2.02405130000480928E17, 0.0, 16.0, 79292.8] ESTIMATE\n     * cast-->[-Infinity, Infinity, 1.7151620323661165E-4, 9.***************, 79292.8] ESTIMATE\n\nPLAN FRAGMENT 141(F08)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 16\n\n  15:Project\n  |  output columns:\n  |  938 <-> [938: warehouse_code, VARCHAR, true]\n  |  941 <-> [941: customer_id, LARGEINT, false]\n  |  946 <-> [946: superior_id, LARGEINT, true]\n  |  cardinality: 220611\n  |  column statistics: \n  |  * warehouse_code-->[-Infinity, Infinity, 4.029215844085464E-6, 11.***************, 220610.***********] ESTIMATE\n  |  * customer_id-->[10001.0, 2.02404220000479136E17, 0.0, 16.0, 106760.0] ESTIMATE\n  |  * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n  |  \n  14:OlapScanNode\n     table: warehouse, rollup: warehouse\n     preAggregation: on\n     Predicates: 936: op IN ('+I', '+U'), [963: deleted_flag, TINYINT, true] = 0, 961: status IN (3, 6)\n     partitionsRatio=1/1, tabletsRatio=2/2\n     tabletList=16316,16317\n     actualRows=993303, avgRowSize=47.118835\n     cardinality: 220611\n     column statistics: \n     * op-->[-Infinity, Infinity, 0.0, 2.0, 2.0] ESTIMATE\n     * warehouse_code-->[-Infinity, Infinity, 4.029215844085464E-6, 11.***************, 220610.***********] ESTIMATE\n     * customer_id-->[10001.0, 2.02404220000479136E17, 0.0, 16.0, 106760.0] ESTIMATE\n     * superior_id-->[2.02101160000110016E17, 2.02404220000479872E17, 0.*****************, 16.0, 123837.0] ESTIMATE\n     * status-->[3.0, 6.0, 0.0, 1.0, 2.0] ESTIMATE\n     * deleted_flag-->[0.0, 0.0, 0.0, 1.0, 2.0] ESTIMATE\n\nPLAN FRAGMENT 142(F07)\n\n  Input Partition: HASH_PARTITIONED: 802: imei1, 803: vivo_source\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 13\n\n  12:Project\n  |  output columns:\n  |  802 <-> [802: imei1, VARCHAR, false]\n  |  803 <-> [803: vivo_source, VARCHAR, false]\n  |  813 <-> [813: customer_account_code, VARCHAR, true]\n  |  814 <-> [814: customer_warehouse_code, VARCHAR, true]\n  |  815 <-> [815: sku_code, VARCHAR, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 4382\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 4382.************] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 12.0, 1.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 11.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 8.0, 586.0] ESTIMATE\n  |  \n  11:HASH JOIN\n  |  join op: LEFT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [802: imei1, VARCHAR, false] = [854: imei1, VARCHAR, true]\n  |  equal join conjunct: [803: vivo_source, VARCHAR, false] = [855: vivo_source, VARCHAR, true]\n  |  other predicates: 854: imei1 IS NULL\n  |  output columns: 802, 803, 813, 814, 815, 854\n  |  can local shuffle: false\n  |  cardinality: 4382\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 4382.************] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 12.0, 1.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 11.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 8.0, 586.0] ESTIMATE\n  |  * imei1-->[-Infinity, Infinity, 1.0, 24.06498, 4382.************] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.*****************, 1.8E-4, 2.0] ESTIMATE\n  |  * op-->[-Infinity, Infinity, 0.*****************, 3.0E-5, 2.0] ESTIMATE\n  |  * del_flag-->[-Infinity, Infinity, 0.*****************, 1.0E-5, 2.0] ESTIMATE\n  |  \n  |----10:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [854: imei1, VARCHAR, true], [855: vivo_source, VARCHAR, true]\n  |       cardinality: 7460771\n  |    \n  7:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [802: imei1, VARCHAR, false], [803: vivo_source, VARCHAR, false]\n     cardinality: 43821\n\nPLAN FRAGMENT 143(F05)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 854: imei1, 855: vivo_source\n  OutPut Exchange Id: 10\n\n  9:Project\n  |  output columns:\n  |  854 <-> [854: imei1, VARCHAR, false]\n  |  855 <-> [855: vivo_source, VARCHAR, false]\n  |  cardinality: 7460771\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.06498, 7460771.444444444] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.8E-4, 2.0] ESTIMATE\n  |  \n  8:OlapScanNode\n     table: wide_serialcode_center, rollup: wide_serialcode_center\n     preAggregation: on\n     Predicates: 855: vivo_source IN ('INGJ01', 'vwork_sale_ruichuang'), 856: op IN ('+I', '+U'), [878: del_flag, VARCHAR, true] = 'N'\n     partitionsRatio=1/1, tabletsRatio=40/40\n     tabletList=271039,271040,271041,271042,271043,271044,271045,271046,271047,271048 ...\n     actualRows=********, avgRowSize=24.0652\n     cardinality: 7460771\n     column statistics: \n     * imei1-->[-Infinity, Infinity, 0.0, 24.06498, 7460771.444444444] ESTIMATE\n     * vivo_source-->[-Infinity, Infinity, 0.0, 1.8E-4, 2.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 3.0E-5, 2.0] ESTIMATE\n     * del_flag-->[-Infinity, Infinity, 0.0, 1.0E-5, 2.0] ESTIMATE\n\nPLAN FRAGMENT 144(F03)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 802: imei1, 803: vivo_source\n  OutPut Exchange Id: 07\n\n  6:Project\n  |  output columns:\n  |  802 <-> [802: imei1, VARCHAR, false]\n  |  803 <-> [803: vivo_source, VARCHAR, false]\n  |  813 <-> [813: customer_account_code, VARCHAR, true]\n  |  814 <-> [814: customer_warehouse_code, VARCHAR, true]\n  |  815 <-> [815: sku_code, VARCHAR, true]\n  |  cardinality: 43821\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 43820.***********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 12.0, 1.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 11.0, 1.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 8.0, 586.0] ESTIMATE\n  |  \n  5:OlapScanNode\n     table: wide_return_serialcode_center, rollup: wide_return_serialcode_center\n     preAggregation: on\n     Predicates: 815: sku_code IS NOT NULL, 805: op IN ('+I', '+U'), 803: vivo_source IN ('INGJ01', 'vwork_sale_ruichuang'), [827: del_flag, VARCHAR, true] = 'N', [819: business_status, VARCHAR, true] != 'RETURN_FACTORY'\n     partitionsRatio=1/1, tabletsRatio=10/10\n     tabletList=271121,271122,271123,271124,271125,271126,271127,271128,271129,271130\n     actualRows=1510607, avgRowSize=78.5675\n     cardinality: 43821\n     probe runtime filters:\n     - filter_id = 36, probe_expr = (815: sku_code)\n     column statistics: \n     * imei1-->[-Infinity, Infinity, 0.0, 24.**************, 43820.***********] ESTIMATE\n     * vivo_source-->[-Infinity, Infinity, 0.0, 6.****************, 2.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 2.0, 1.0] ESTIMATE\n     * customer_account_code-->[-Infinity, Infinity, 0.0, 12.0, 1.0] ESTIMATE\n     * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 11.0, 1.0] ESTIMATE\n     * sku_code-->[-Infinity, Infinity, 0.0, 8.0, 586.0] ESTIMATE\n     * business_status-->[-Infinity, Infinity, 0.0, 14.0, 1.0] ESTIMATE\n     * del_flag-->[-Infinity, Infinity, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 145(F02)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange Id: 04\n\n  3:Project\n  |  output columns:\n  |  753 <-> [753: imei1, VARCHAR, false]\n  |  754 <-> [754: vivo_source, VARCHAR, false]\n  |  763 <-> [763: customer_account_code, VARCHAR, true]\n  |  764 <-> [764: customer_warehouse_code, VARCHAR, true]\n  |  765 <-> [765: sku_code, VARCHAR, true]\n  |  cardinality: 6217310\n  |  column statistics: \n  |  * imei1-->[-Infinity, Infinity, 0.0, 24.06498, 6217309.*********] ESTIMATE\n  |  * vivo_source-->[-Infinity, Infinity, 0.0, 1.8E-4, 2.0] ESTIMATE\n  |  * customer_account_code-->[-Infinity, Infinity, 0.0, 0.656275, 11059.0] ESTIMATE\n  |  * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 0.985805, 18343.0] ESTIMATE\n  |  * sku_code-->[-Infinity, Infinity, 0.0, 0.01892, 473.0] ESTIMATE\n  |  \n  2:OlapScanNode\n     table: wide_serialcode_center, rollup: wide_serialcode_center\n     preAggregation: on\n     Predicates: 765: sku_code IS NOT NULL, 755: op IN ('+I', '+U'), 754: vivo_source IN ('INGJ01', 'vwork_sale_ruichuang'), [777: del_flag, VARCHAR, true] = 'N', [769: business_status, VARCHAR, true] != 'RETURN_FACTORY'\n     partitionsRatio=1/1, tabletsRatio=40/40\n     tabletList=271039,271040,271041,271042,271043,271044,271045,271046,271047,271048 ...\n     actualRows=********, avgRowSize=25.726465\n     cardinality: 6217310\n     probe runtime filters:\n     - filter_id = 36, probe_expr = (765: sku_code)\n     column statistics: \n     * imei1-->[-Infinity, Infinity, 0.0, 24.06498, 6217309.*********] ESTIMATE\n     * vivo_source-->[-Infinity, Infinity, 0.0, 1.8E-4, 2.0] ESTIMATE\n     * op-->[-Infinity, Infinity, 0.0, 3.0E-5, 2.0] ESTIMATE\n     * customer_account_code-->[-Infinity, Infinity, 0.0, 0.656275, 11059.0] ESTIMATE\n     * customer_warehouse_code-->[-Infinity, Infinity, 0.0, 0.985805, 18343.0] ESTIMATE\n     * sku_code-->[-Infinity, Infinity, 0.0, 0.01892, 473.0] ESTIMATE\n     * business_status-->[-Infinity, Infinity, 0.0, 2.65E-4, 6.0] ESTIMATE\n     * del_flag-->[-Infinity, Infinity, 0.0, 1.0E-5, 2.0] ESTIMATE\n", "session_variables": "{\"partial_update_mode\":\"auto\",\"cbo_cte_reuse\":true,\"character_set_connection\":\"utf8\",\"cbo_use_correlated_join_estimate\":true,\"enable_insert_strict\":true,\"enable_connector_adaptive_io_tasks\":true,\"tx_isolation\":\"REPEATABLE-READ\",\"enable_hive_metadata_cache_with_insert\":false,\"cbo_cte_reuse_rate_v2\":1.15,\"character_set_results\":\"utf8\",\"enable_iceberg_column_statistics\":true,\"materialized_view_union_rewrite_mode\":0,\"enable_count_star_optimization\":true,\"materialized_view_max_relation_mapping_size\":10,\"global_runtime_filter_build_min_size\":131072,\"enable_iceberg_identity_column_optimize\":true,\"query_excluding_mv_names\":\"\",\"enable_rewrite_simple_agg_to_meta_scan\":false,\"enable_adaptive_sink_dop\":true,\"consistent_hash_virtual_number\":128,\"enable_profile\":true,\"load_mem_limit\":0,\"cbo_eq_base_type\":\"varchar\",\"enable_materialized_view_for_insert\":false,\"large_decimal_underlying_type\":\"panic\",\"sql_safe_updates\":0,\"runtime_filter_early_return_selectivity\":0.05,\"enable_local_shuffle_agg\":true,\"disable_function_fold_constants\":false,\"enable_query_queue\":true,\"select_ratio_threshold\":0.15,\"query_delivery_timeout\":300,\"collation_database\":\"utf8_general_ci\",\"spill_mem_table_size\":104857600,\"follower_query_forward_mode\":\"\",\"orc_use_column_names\":false,\"cbo_use_lock_db\":false,\"new_planner_agg_stage\":0,\"enable_strict_order_by\":true,\"hash_join_interpolate_passthrough\":false,\"use_compute_nodes\":-1,\"collation_connection\":\"utf8_general_ci\",\"enable_rewrite_bitmap_union_to_bitamp_agg\":false,\"enable_force_rule_based_mv_rewrite\":false,\"enable_array_distinct_after_agg_opt\":true,\"resource_group\":\"\",\"enable_materialized_view_plan_cache\":true,\"spill_operator_max_bytes\":1048576000,\"cbo_max_reorder_node_use_dp\":10,\"enable_result_sink_accumulate\":true,\"enable_hive_column_stats\":true,\"enable_async_profile\":true,\"enable_groupby_use_output_alias\":false,\"global_runtime_filter_wait_timeout\":20,\"forward_to_leader\":false,\"count_distinct_column_buckets\":1024,\"cross_join_cost_penalty\":1000000,\"query_cache_agg_cardinality_limit\":5000000,\"enable_pipeline_query_statistic\":true,\"cboPushDownAggregateMode_v1\":-1,\"window_partition_mode\":1,\"enable_deliver_batch_fragments\":true,\"enable_tablet_internal_parallel_v2\":true,\"interpolate_passthrough\":true,\"enable_incremental_mv\":false,\"cbo_push_down_topn_limit\":0,\"SQL_AUTO_IS_NULL\":false,\"event_scheduler\":\"OFF\",\"max_pipeline_dop\":64,\"broadcast_right_table_scale_factor\":10,\"materialized_view_rewrite_mode\":\"DEFAULT\",\"enable_simplify_case_when\":true,\"runtime_join_filter_push_down_limit\":1024000,\"big_query_log_cpu_second_threshold\":480,\"div_precision_increment\":4,\"runtime_adaptive_dop_max_block_rows_per_driver_seq\":16384,\"log_rejected_record_num\":0,\"cbo_push_down_distinct_below_window\":true,\"sql_mode_v2\":32,\"prefer_cte_rewrite\":false,\"optimizer_materialized_view_timelimit\":1000,\"hdfs_backend_selector_scan_range_shuffle\":false,\"pipeline_profile_level\":1,\"parallel_fragment_exec_instance_num\":1,\"max_scan_key_num\":-1,\"net_read_timeout\":60,\"streaming_preaggregation_mode\":\"auto\",\"hive_partition_stats_sample_size\":3000,\"enable_mv_planner\":false,\"enable_collect_table_level_scan_stats\":true,\"query_debug_options\":\"\",\"profile_timeout\":2,\"cbo_push_down_aggregate\":\"global\",\"spill_encode_level\":7,\"enable_query_dump\":false,\"global_runtime_filter_build_max_size\":67108864,\"enable_rewrite_sum_by_associative_rule\":true,\"query_cache_hot_partition_num\":3,\"enable_prune_complex_types\":true,\"query_cache_type\":0,\"max_parallel_scan_instance_num\":-1,\"query_cache_entry_max_rows\":409600,\"enable_mv_optimizer_trace_log\":false,\"connector_io_tasks_per_scan_operator\":16,\"enable_materialized_view_union_rewrite\":true,\"sql_quote_show_create\":true,\"scan_or_to_union_threshold\":50000000,\"enable_materialized_view_rewrite_partition_compensate\":true,\"enable_exchange_pass_through\":true,\"runtime_profile_report_interval\":10,\"query_cache_entry_max_bytes\":4194304,\"enable_partition_column_value_only_optimization\":true,\"enable_iceberg_ndv\":true,\"enable_exchange_perf\":false,\"workgroup_id\":0,\"enable_rewrite_groupingsets_to_union_all\":false,\"transmission_compression_type\":\"NO_COMPRESSION\",\"interactive_timeout\":3600,\"use_page_cache\":true,\"big_query_log_scan_bytes_threshold\":10737418240,\"collation_server\":\"utf8_general_ci\",\"cbo_decimal_cast_string_strict\":true,\"tablet_internal_parallel_mode\":\"auto\",\"enable_pipeline\":true,\"spill_mode\":\"auto\",\"enable_query_debug_trace\":false,\"cbo_materialized_view_rewrite_related_mvs_limit\":64,\"enable_show_all_variables\":false,\"full_sort_max_buffered_bytes\":16777216,\"wait_timeout\":28800,\"enable_query_tablet_affinity\":false,\"transmission_encode_level\":7,\"query_including_mv_names\":\"\",\"transaction_isolation\":\"REPEATABLE-READ\",\"enable_global_runtime_filter\":true,\"enable_load_profile\":false,\"enable_plan_validation\":true,\"load_transmission_compression_type\":\"NO_COMPRESSION\",\"global_runtime_filter_rpc_http_min_size\":67108864,\"cbo_materialized_view_rewrite_rule_output_limit\":3,\"cbo_enable_low_cardinality_optimize\":true,\"scan_use_query_mem_ratio\":0.3,\"new_planner_optimize_timeout\":3000,\"enable_outer_join_reorder\":true,\"force_schedule_local\":false,\"hudi_mor_force_jni_reader\":false,\"full_sort_late_materialization\":false,\"cbo_enable_greedy_join_reorder\":true,\"range_pruner_max_predicate\":100,\"enable_rbo_table_prune\":false,\"spillable_operator_mask\":-1,\"rpc_http_min_size\":2147482624,\"cbo_debug_alive_backend_number\":0,\"global_runtime_filter_probe_min_size\":102400,\"scan_or_to_union_limit\":4,\"enable_cbo_table_prune\":false,\"enable_parallel_merge\":true,\"cbo_materialized_view_rewrite_candidate_limit\":12,\"nested_mv_rewrite_max_level\":3,\"big_query_profile_threshold\":\"0s\",\"net_write_timeout\":60,\"cbo_prune_shuffle_column_rate\":0.1,\"enable_persistent_index_by_default\":true,\"hash_join_push_down_right_table\":true,\"pipeline_sink_dop\":0,\"broadcast_row_limit\":15000000,\"enable_populate_block_cache\":true,\"exec_mem_limit\":2147483648,\"enable_sort_aggregate\":false,\"query_cache_force_populate\":false,\"runtime_filter_on_exchange_node\":false,\"disable_join_reorder\":false,\"enable_rule_based_materialized_view_rewrite\":true,\"global_runtime_filter_rpc_timeout\":400,\"connector_scan_use_query_mem_ratio\":0.3,\"net_buffer_length\":16384,\"cbo_prune_subfield\":true,\"full_sort_max_buffered_rows\":1024000,\"query_timeout\":60,\"connector_io_tasks_slow_io_latency_ms\":50,\"cbo_max_reorder_node\":50,\"enable_distinct_column_bucketization\":false,\"enable_big_query_log\":true,\"analyze_mv\":\"sample\",\"runtime_filter_scan_wait_time\":20,\"enable_sync_materialized_view_rewrite\":true,\"prefer_compute_node\":false,\"enable_strict_type\":false,\"enable_table_prune_on_update\":false,\"group_concat_max_len\":1024,\"parse_tokens_limit\":3500000,\"chunk_size\":4096,\"global_runtime_filter_probe_min_selectivity\":0.5,\"query_mem_limit\":0,\"enable_filter_unused_columns_in_scan_stage\":true,\"enable_scan_block_cache\":true,\"enable_materialized_view_single_table_view_delta_rewrite\":false,\"enable_prune_complex_types_in_unnest\":true,\"auto_increment_increment\":1,\"sql_dialect\":\"StarRocks\",\"big_query_log_scan_rows_threshold\":1000000000,\"character_set_client\":\"utf8\",\"autocommit\":true,\"enable_column_expr_predicate\":true,\"enable_runtime_adaptive_dop\":false,\"cbo_cte_max_limit\":10,\"storage_engine\":\"olap\",\"enable_optimizer_trace_log\":false,\"spill_operator_min_bytes\":10485760,\"cbo_enable_dp_join_reorder\":true,\"tx_visible_wait_timeout\":10,\"materialized_view_join_same_table_permutation_limit\":5,\"enable_materialized_view_view_delta_rewrite\":true,\"cbo_max_reorder_node_use_exhaustive\":4,\"enable_sql_digest\":false,\"spill_mem_table_num\":2,\"enable_spill\":false,\"enable_materialized_view_rewrite_greedy_mode\":false,\"pipeline_dop\":1,\"single_node_exec_plan\":false,\"join_implementation_mode_v2\":\"auto\",\"sql_select_limit\":9223372036854775807,\"enable_materialized_view_rewrite\":false,\"statistic_collect_parallel\":1,\"hdfs_backend_selector_hash_algorithm\":\"consistent\",\"enable_expr_prune_partition\":true,\"enable_topn_runtime_filter\":true,\"disable_colocate_join\":false,\"max_pushdown_conditions_per_column\":-1,\"default_table_compression\":\"lz4_frame\",\"runtime_adaptive_dop_max_output_amplification_factor\":0,\"skew_join_rand_range\":1000,\"choose_execute_instances_mode\":\"LOCALITY\",\"innodb_read_only\":true,\"spill_mem_limit_threshold\":0.8,\"cbo_reorder_threshold_use_exhaustive\":6,\"enable_predicate_reorder\":false,\"enable_query_cache\":true,\"transaction_read_only\":\"OFF\",\"max_allowed_packet\":33554432,\"time_zone\":\"Asia/Kolkata\",\"enable_multicolumn_global_runtime_filter\":false,\"character_set_server\":\"utf8\",\"cbo_use_nth_exec_plan\":0,\"io_tasks_per_scan_operator\":4,\"parallel_exchange_instance_num\":-1,\"enable_shared_scan\":true,\"cbo_derive_range_join_predicate\":false,\"allow_default_partition\":false,\"enable_pipeline_level_shuffle\":true}", "be_number": 0, "be_core_stat": {"numOfHardwareCoresPerBe": "{\"274208\":16,\"274209\":16,\"204005\":8,\"274205\":16,\"274206\":16,\"274207\":16,\"10732\":16}", "cachedAvgNumOfHardwareCores": 14}, "exception": [], "version": "3.1.11", "commit_version": "34f131b"}