{"statement": "SELECT sum(db_mock_000.tbl_mock_130.mock_125)\nFROM db_mock_000.tbl_mock_001 AS tbl_mock_129 INNER JOIN db_mock_000.tbl_mock_122 AS tbl_mock_130 ON db_mock_000.tbl_mock_130.mock_092 = db_mock_000.tbl_mock_129.mock_035\nWHERE ((db_mock_000.tbl_mock_130.mock_126 = (curdate())) AND (db_mock_000.tbl_mock_130.mock_127 < 17)) AND (db_mock_000.tbl_mock_129.mock_057 NOT IN (53)) LIMIT 2", "table_meta": {"db_mock_000.tbl_mock_001": "CREATE TABLE db_mock_000.tbl_mock_001 (\nmock_039 varchar(400) ,\nmock_047 int(11) ,\nmock_013 int(11) ,\nmock_035 int(11) NOT NULL ,\nmock_068 int(11) ,\nmock_025 int(11) ,\nmock_028 int(11) ,\nmock_031 varchar(100) ,\nmock_007 int(11) ,\nmock_010 varchar(100) ,\nmock_057 int(11) ,\nmock_060 varchar(50) ,\nmock_014 int(11) ,\nmock_018 varchar(50) ,\nmock_073 int(11) ,\nmock_076 varchar(100) ,\nmock_036 varchar(200) ,\nmock_069 int(11) ,\nmock_026 int(11) ,\nmock_029 int(11) ,\nmock_032 varchar(100) ,\nmock_009 int(11) ,\nmock_011 varchar(100) ,\nmock_058 int(11) ,\nmock_061 varchar(50) ,\nmock_015 int(11) ,\nmock_019 varchar(50) ,\nmock_074 int(11) ,\nmock_077 varchar(100) ,\nmock_037 varchar(200) ,\nmock_070 int(11) ,\nmock_027 int(11) ,\nmock_030 int(11) ,\nmock_033 varchar(100) ,\nmock_008 varchar(100) ,\nmock_012 varchar(100) ,\nmock_059 varchar(50) ,\nmock_016 varchar(100) ,\nmock_020 varchar(50) ,\nmock_075 varchar(100) ,\nmock_042 int(11) ,\nmock_021 int(11) ,\nmock_022 varchar(400) ,\nmock_038 varchar(1) ,\nmock_055 int(11) ,\nmock_056 varchar(255) ,\nmock_052 varchar(255) ,\nmock_065 varchar(255) ,\nmock_005 double NOT NULL ,\nmock_064 double NOT NULL ,\nmock_043 int(11) NOT NULL ,\nmock_044 varchar(100) NOT NULL ,\nmock_072 varchar(100) NOT NULL ,\nmock_071 double NOT NULL ,\nmock_050 varchar(100) NOT NULL ,\nmock_067 double NOT NULL ,\nmock_040 int(11) NOT NULL ,\nmock_002 varchar(255) NOT NULL ,\nmock_003 varchar(255) NOT NULL ,\nmock_034 int(11) ,\nmock_063 decimal(18, 4) ,\nmock_062 decimal(18, 4) ,\nmock_006 varchar(10) ,\nmock_053 int(11) ,\nmock_054 varchar(500) ,\nmock_017 varchar(100) ,\nmock_046 varchar(100) ,\nmock_045 varchar(1000) ,\nmock_048 varchar(200) ,\nmock_049 varchar(200) ,\nmock_041 varchar(10) ,\nmock_004 varchar(255) ,\nmock_051 varchar(65533) ,\nmock_023 int(11) ,\nmock_066 int(11) ,\nmock_024 varchar(50) ,\n  INDEX index_hotelbasicinfo_hotellevel (mock_038) USING BITMAP,\n  INDEX index_hotelbasicinfo_star_ctrip (mock_068) USING BITMAP,\n  INDEX index_hotelbasicinfo_goldstar_ctrip (mock_025) USING BITMAP,\n  INDEX index_hotelbasicinfo_star_elong (mock_069) USING BITMAP,\n  INDEX index_hotelbasicinfo_goldstar_elong (mock_026) USING BITMAP,\n  INDEX index_hotelbasicinfo_star_qunar (mock_070) USING BITMAP,\n  INDEX index_hotelbasicinfo_goldstar_qunar (mock_027) USING BITMAP,\n  INDEX index_hotelbasicinfo_hoteltype (mock_034) USING BITMAP\n) ENGINE= OLAP \nDUPLICATE KEY(mock_039)\nDISTRIBUTED BY HASH(mock_035) BUCKETS 10 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"bloom_filter_columns\" = \"mock_002, mock_052, mock_059, mock_055, mock_075, mock_050, mock_003, mock_014, mock_042, mock_074, mock_057, mock_058, mock_016, mock_034, mock_009, mock_043, mock_013, mock_015, mock_038, mock_073, mock_065\"\n);", "db_mock_000.tbl_mock_078": "CREATE TABLE db_mock_000.tbl_mock_078 (\nmock_095 bigint(20) NOT NULL ,\nmock_094 datetime NOT NULL ,\nmock_090 int(11) NOT NULL ,\nmock_107 varchar(50) ,\nmock_080 datetime ,\nmock_088 datetime ,\nmock_089 datetime ,\nmock_092 int(11) ,\nmock_102 int(11) ,\nmock_100 int(11) ,\nmock_079 decimal(18, 4) ,\nmock_096 varchar(5) ,\nmock_081 varchar(10) ,\nmock_084 varchar(1) ,\nmock_104 varchar(50) ,\nmock_087 datetime ,\nmock_086 datetime ,\nmock_085 varchar(45) ,\nmock_083 int(11) ,\nmock_099 int(11) ,\nmock_103 varchar(50) ,\nmock_091 varchar(5) ,\nmock_105 varchar(10) ,\nmock_108 int(11) ,\nmock_098 decimal(18, 4) ,\nmock_101 varchar(200) ,\nmock_106 varchar(20) ,\nmock_097 varchar(5) ,\nmock_093 decimal(18, 4) ,\nmock_082 datetime \n) ENGINE= OLAP \nPRIMARY KEY(mock_095, mock_094, mock_090)\nPARTITION BY RANGE(mock_094)\n(PARTITION p20240418 VALUES [(\"2024-04-18 00:00:00\"), (\"2024-04-19 00:00:00\")),\nPARTITION p20240419 VALUES [(\"2024-04-19 00:00:00\"), (\"2024-04-20 00:00:00\")),\nPARTITION p20240420 VALUES [(\"2024-04-20 00:00:00\"), (\"2024-04-21 00:00:00\")),\nPARTITION p20240421 VALUES [(\"2024-04-21 00:00:00\"), (\"2024-04-22 00:00:00\")),\nPARTITION p20240422 VALUES [(\"2024-04-22 00:00:00\"), (\"2024-04-23 00:00:00\")),\nPARTITION p20240423 VALUES [(\"2024-04-23 00:00:00\"), (\"2024-04-24 00:00:00\")),\nPARTITION p20240424 VALUES [(\"2024-04-24 00:00:00\"), (\"2024-04-25 00:00:00\")),\nPARTITION p20240425 VALUES [(\"2024-04-25 00:00:00\"), (\"2024-04-26 00:00:00\")),\nPARTITION p20240426 VALUES [(\"2024-04-26 00:00:00\"), (\"2024-04-27 00:00:00\")),\nPARTITION p20240427 VALUES [(\"2024-04-27 00:00:00\"), (\"2024-04-28 00:00:00\")),\nPARTITION p20240428 VALUES [(\"2024-04-28 00:00:00\"), (\"2024-04-29 00:00:00\")),\nPARTITION p20240429 VALUES [(\"2024-04-29 00:00:00\"), (\"2024-04-30 00:00:00\")),\nPARTITION p20240430 VALUES [(\"2024-04-30 00:00:00\"), (\"2024-05-01 00:00:00\")),\nPARTITION p20240501 VALUES [(\"2024-05-01 00:00:00\"), (\"2024-05-02 00:00:00\")),\nPARTITION p20240502 VALUES [(\"2024-05-02 00:00:00\"), (\"2024-05-03 00:00:00\")),\nPARTITION p20240503 VALUES [(\"2024-05-03 00:00:00\"), (\"2024-05-04 00:00:00\")),\nPARTITION p20240504 VALUES [(\"2024-05-04 00:00:00\"), (\"2024-05-05 00:00:00\")),\nPARTITION p20240505 VALUES [(\"2024-05-05 00:00:00\"), (\"2024-05-06 00:00:00\")),\nPARTITION p20240506 VALUES [(\"2024-05-06 00:00:00\"), (\"2024-05-07 00:00:00\")),\nPARTITION p20240507 VALUES [(\"2024-05-07 00:00:00\"), (\"2024-05-08 00:00:00\")),\nPARTITION p20240508 VALUES [(\"2024-05-08 00:00:00\"), (\"2024-05-09 00:00:00\")),\nPARTITION p20240509 VALUES [(\"2024-05-09 00:00:00\"), (\"2024-05-10 00:00:00\")),\nPARTITION p20240510 VALUES [(\"2024-05-10 00:00:00\"), (\"2024-05-11 00:00:00\")),\nPARTITION p20240511 VALUES [(\"2024-05-11 00:00:00\"), (\"2024-05-12 00:00:00\")),\nPARTITION p20240512 VALUES [(\"2024-05-12 00:00:00\"), (\"2024-05-13 00:00:00\")),\nPARTITION p20240513 VALUES [(\"2024-05-13 00:00:00\"), (\"2024-05-14 00:00:00\")),\nPARTITION p20240514 VALUES [(\"2024-05-14 00:00:00\"), (\"2024-05-15 00:00:00\")),\nPARTITION p20240515 VALUES [(\"2024-05-15 00:00:00\"), (\"2024-05-16 00:00:00\")),\nPARTITION p20240516 VALUES [(\"2024-05-16 00:00:00\"), (\"2024-05-17 00:00:00\")),\nPARTITION p20240517 VALUES [(\"2024-05-17 00:00:00\"), (\"2024-05-18 00:00:00\")),\nPARTITION p20240518 VALUES [(\"2024-05-18 00:00:00\"), (\"2024-05-19 00:00:00\")),\nPARTITION p20240519 VALUES [(\"2024-05-19 00:00:00\"), (\"2024-05-20 00:00:00\")),\nPARTITION p20240520 VALUES [(\"2024-05-20 00:00:00\"), (\"2024-05-21 00:00:00\")),\nPARTITION p20240521 VALUES [(\"2024-05-21 00:00:00\"), (\"2024-05-22 00:00:00\")),\nPARTITION p20240522 VALUES [(\"2024-05-22 00:00:00\"), (\"2024-05-23 00:00:00\")),\nPARTITION p20240523 VALUES [(\"2024-05-23 00:00:00\"), (\"2024-05-24 00:00:00\")),\nPARTITION p20240524 VALUES [(\"2024-05-24 00:00:00\"), (\"2024-05-25 00:00:00\")),\nPARTITION p20240525 VALUES [(\"2024-05-25 00:00:00\"), (\"2024-05-26 00:00:00\")),\nPARTITION p20240526 VALUES [(\"2024-05-26 00:00:00\"), (\"2024-05-27 00:00:00\")),\nPARTITION p20240527 VALUES [(\"2024-05-27 00:00:00\"), (\"2024-05-28 00:00:00\")),\nPARTITION p20240528 VALUES [(\"2024-05-28 00:00:00\"), (\"2024-05-29 00:00:00\")),\nPARTITION p20240529 VALUES [(\"2024-05-29 00:00:00\"), (\"2024-05-30 00:00:00\")),\nPARTITION p20240530 VALUES [(\"2024-05-30 00:00:00\"), (\"2024-05-31 00:00:00\")),\nPARTITION p20240531 VALUES [(\"2024-05-31 00:00:00\"), (\"2024-06-01 00:00:00\")),\nPARTITION p20240601 VALUES [(\"2024-06-01 00:00:00\"), (\"2024-06-02 00:00:00\")),\nPARTITION p20240602 VALUES [(\"2024-06-02 00:00:00\"), (\"2024-06-03 00:00:00\")),\nPARTITION p20240603 VALUES [(\"2024-06-03 00:00:00\"), (\"2024-06-04 00:00:00\")),\nPARTITION p20240604 VALUES [(\"2024-06-04 00:00:00\"), (\"2024-06-05 00:00:00\")),\nPARTITION p20240605 VALUES [(\"2024-06-05 00:00:00\"), (\"2024-06-06 00:00:00\")),\nPARTITION p20240606 VALUES [(\"2024-06-06 00:00:00\"), (\"2024-06-07 00:00:00\")),\nPARTITION p20240607 VALUES [(\"2024-06-07 00:00:00\"), (\"2024-06-08 00:00:00\")),\nPARTITION p20240608 VALUES [(\"2024-06-08 00:00:00\"), (\"2024-06-09 00:00:00\")),\nPARTITION p20240609 VALUES [(\"2024-06-09 00:00:00\"), (\"2024-06-10 00:00:00\")),\nPARTITION p20240610 VALUES [(\"2024-06-10 00:00:00\"), (\"2024-06-11 00:00:00\")),\nPARTITION p20240611 VALUES [(\"2024-06-11 00:00:00\"), (\"2024-06-12 00:00:00\")),\nPARTITION p20240612 VALUES [(\"2024-06-12 00:00:00\"), (\"2024-06-13 00:00:00\")),\nPARTITION p20240613 VALUES [(\"2024-06-13 00:00:00\"), (\"2024-06-14 00:00:00\")),\nPARTITION p20240614 VALUES [(\"2024-06-14 00:00:00\"), (\"2024-06-15 00:00:00\")),\nPARTITION p20240615 VALUES [(\"2024-06-15 00:00:00\"), (\"2024-06-16 00:00:00\")),\nPARTITION p20240616 VALUES [(\"2024-06-16 00:00:00\"), (\"2024-06-17 00:00:00\")),\nPARTITION p20240617 VALUES [(\"2024-06-17 00:00:00\"), (\"2024-06-18 00:00:00\")),\nPARTITION p20240618 VALUES [(\"2024-06-18 00:00:00\"), (\"2024-06-19 00:00:00\")),\nPARTITION p20240619 VALUES [(\"2024-06-19 00:00:00\"), (\"2024-06-20 00:00:00\")),\nPARTITION p20240620 VALUES [(\"2024-06-20 00:00:00\"), (\"2024-06-21 00:00:00\")))\nDISTRIBUTED BY HASH(mock_090) BUCKETS 10 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"dynamic_partition.enable\" = \"true\",\n\"dynamic_partition.time_unit\" = \"DAY\",\n\"dynamic_partition.time_zone\" = \"Asia/Shanghai\",\n\"dynamic_partition.start\" = \"-62\",\n\"dynamic_partition.end\" = \"1\",\n\"dynamic_partition.prefix\" = \"p\",\n\"dynamic_partition.buckets\" = \"10\",\n\"dynamic_partition.history_partition_num\" = \"0\"\n);", "db_mock_000.tbl_mock_109": "CREATE TABLE db_mock_000.tbl_mock_109 (\nmock_094 datetime NOT NULL ,\nmock_115 varchar(50) NOT NULL ,\nmock_114 varchar(50) NOT NULL ,\nmock_095 bigint(20) ,\nmock_092 int(11) NOT NULL ,\nmock_106 varchar(50) ,\nmock_080 datetime ,\nmock_088 datetime ,\nmock_102 varchar(50) ,\nmock_096 varchar(20) ,\nmock_081 varchar(20) ,\nmock_100 int(11) ,\nmock_107 varchar(50) ,\nmock_113 varchar(20) ,\nmock_098 double ,\nmock_079 double ,\nmock_110 datetime ,\nmock_116 varchar(20) ,\nmock_112 datetime ,\nmock_111 datetime \n) ENGINE= OLAP \nPRIMARY KEY(mock_094, mock_115, mock_114)\nPARTITION BY RANGE(mock_094)\n(PARTITION p20240610 VALUES [(\"2024-06-10 00:00:00\"), (\"2024-06-11 00:00:00\")),\nPARTITION p20240611 VALUES [(\"2024-06-11 00:00:00\"), (\"2024-06-12 00:00:00\")),\nPARTITION p20240612 VALUES [(\"2024-06-12 00:00:00\"), (\"2024-06-13 00:00:00\")),\nPARTITION p20240613 VALUES [(\"2024-06-13 00:00:00\"), (\"2024-06-14 00:00:00\")),\nPARTITION p20240614 VALUES [(\"2024-06-14 00:00:00\"), (\"2024-06-15 00:00:00\")),\nPARTITION p20240615 VALUES [(\"2024-06-15 00:00:00\"), (\"2024-06-16 00:00:00\")),\nPARTITION p20240616 VALUES [(\"2024-06-16 00:00:00\"), (\"2024-06-17 00:00:00\")),\nPARTITION p20240617 VALUES [(\"2024-06-17 00:00:00\"), (\"2024-06-18 00:00:00\")),\nPARTITION p20240618 VALUES [(\"2024-06-18 00:00:00\"), (\"2024-06-19 00:00:00\")),\nPARTITION p20240619 VALUES [(\"2024-06-19 00:00:00\"), (\"2024-06-20 00:00:00\")),\nPARTITION p20240620 VALUES [(\"2024-06-20 00:00:00\"), (\"2024-06-21 00:00:00\")))\nDISTRIBUTED BY HASH(mock_114) BUCKETS 10 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"dynamic_partition.enable\" = \"true\",\n\"dynamic_partition.time_unit\" = \"DAY\",\n\"dynamic_partition.time_zone\" = \"Asia/Shanghai\",\n\"dynamic_partition.start\" = \"-9\",\n\"dynamic_partition.end\" = \"1\",\n\"dynamic_partition.prefix\" = \"p\",\n\"dynamic_partition.buckets\" = \"10\",\n\"dynamic_partition.history_partition_num\" = \"0\"\n);", "db_mock_000.tbl_mock_117": "CREATE TABLE db_mock_000.tbl_mock_117 (\nmock_090 int(11) ,\nmock_118 int(11) \n) ENGINE= OLAP \nDUPLICATE KEY(mock_090)\nDISTRIBUTED BY HASH(mock_090) BUCKETS 10 \nPROPERTIES (\n\"replication_num\" = \"1\"\n);", "db_mock_000.tbl_mock_119": "CREATE TABLE db_mock_000.tbl_mock_119 (\nmock_095 bigint(20) NOT NULL ,\nmock_082 datetime NOT NULL ,\nmock_094 datetime ,\nmock_090 int(11) ,\nmock_092 int(11) ,\nmock_100 int(11) ,\nmock_096 varchar(5) ,\nmock_086 datetime ,\nmock_091 varchar(5) ,\nmock_105 varchar(10) ,\nmock_106 varchar(20) ,\nmock_097 varchar(5) ,\nmock_093 decimal(18, 4) \n) ENGINE= OLAP \nPRIMARY KEY(mock_095, mock_082)\nPARTITION BY RANGE(mock_082)\n(PARTITION p20240528 VALUES [(\"2024-05-28 00:00:00\"), (\"2024-05-29 00:00:00\")),\nPARTITION p20240529 VALUES [(\"2024-05-29 00:00:00\"), (\"2024-05-30 00:00:00\")),\nPARTITION p20240530 VALUES [(\"2024-05-30 00:00:00\"), (\"2024-05-31 00:00:00\")),\nPARTITION p20240531 VALUES [(\"2024-05-31 00:00:00\"), (\"2024-06-01 00:00:00\")),\nPARTITION p20240601 VALUES [(\"2024-06-01 00:00:00\"), (\"2024-06-02 00:00:00\")),\nPARTITION p20240602 VALUES [(\"2024-06-02 00:00:00\"), (\"2024-06-03 00:00:00\")),\nPARTITION p20240603 VALUES [(\"2024-06-03 00:00:00\"), (\"2024-06-04 00:00:00\")),\nPARTITION p20240604 VALUES [(\"2024-06-04 00:00:00\"), (\"2024-06-05 00:00:00\")),\nPARTITION p20240605 VALUES [(\"2024-06-05 00:00:00\"), (\"2024-06-06 00:00:00\")),\nPARTITION p20240606 VALUES [(\"2024-06-06 00:00:00\"), (\"2024-06-07 00:00:00\")),\nPARTITION p20240607 VALUES [(\"2024-06-07 00:00:00\"), (\"2024-06-08 00:00:00\")),\nPARTITION p20240608 VALUES [(\"2024-06-08 00:00:00\"), (\"2024-06-09 00:00:00\")),\nPARTITION p20240609 VALUES [(\"2024-06-09 00:00:00\"), (\"2024-06-10 00:00:00\")),\nPARTITION p20240610 VALUES [(\"2024-06-10 00:00:00\"), (\"2024-06-11 00:00:00\")),\nPARTITION p20240611 VALUES [(\"2024-06-11 00:00:00\"), (\"2024-06-12 00:00:00\")),\nPARTITION p20240612 VALUES [(\"2024-06-12 00:00:00\"), (\"2024-06-13 00:00:00\")),\nPARTITION p20240613 VALUES [(\"2024-06-13 00:00:00\"), (\"2024-06-14 00:00:00\")),\nPARTITION p20240614 VALUES [(\"2024-06-14 00:00:00\"), (\"2024-06-15 00:00:00\")),\nPARTITION p20240615 VALUES [(\"2024-06-15 00:00:00\"), (\"2024-06-16 00:00:00\")),\nPARTITION p20240616 VALUES [(\"2024-06-16 00:00:00\"), (\"2024-06-17 00:00:00\")),\nPARTITION p20240617 VALUES [(\"2024-06-17 00:00:00\"), (\"2024-06-18 00:00:00\")),\nPARTITION p20240618 VALUES [(\"2024-06-18 00:00:00\"), (\"2024-06-19 00:00:00\")),\nPARTITION p20240619 VALUES [(\"2024-06-19 00:00:00\"), (\"2024-06-20 00:00:00\")),\nPARTITION p20240620 VALUES [(\"2024-06-20 00:00:00\"), (\"2024-06-21 00:00:00\")))\nDISTRIBUTED BY HASH(mock_095) BUCKETS 10 \nPROPERTIES (\n\"replication_num\" = \"1\",\n\"dynamic_partition.enable\" = \"true\",\n\"dynamic_partition.time_unit\" = \"DAY\",\n\"dynamic_partition.time_zone\" = \"Asia/Shanghai\",\n\"dynamic_partition.start\" = \"-22\",\n\"dynamic_partition.end\" = \"1\",\n\"dynamic_partition.prefix\" = \"p\",\n\"dynamic_partition.buckets\" = \"10\",\n\"dynamic_partition.history_partition_num\" = \"0\"\n);"}, "table_row_count": {"db_mock_000.tbl_mock_001": {"tbl_mock_001": 1944369.0}, "db_mock_000.tbl_mock_117": {"tbl_mock_117": 28484657.0}, "db_mock_000.tbl_mock_109": {"tbl_mock_123": 108639.0}, "db_mock_000.tbl_mock_078": {"tbl_mock_123": 654320.0}}, "view_meta": {"db_mock_000.tbl_mock_120": "SELECT tbl_mock_078.mock_095, tbl_mock_078.mock_100, tbl_mock_078.mock_094, tbl_mock_078.mock_090, tbl_mock_078.mock_092, tbl_mock_078.mock_096, tbl_mock_078.mock_097, 0 AS mock_144\nFROM db_mock_000.tbl_mock_078 UNION ALL SELECT tbl_mock_109.mock_095, tbl_mock_109.mock_100, tbl_mock_109.mock_094, tbl_mock_109.mock_092 AS mock_090, tbl_mock_109.mock_092, CASE WHEN (tbl_mock_109.mock_096 IN ('CANCELLED', 'REJECTED')) THEN 'C' ELSE 'P' END AS mock_096, 'q' AS mock_097, 1 AS mock_144\nFROM db_mock_000.tbl_mock_109\nWHERE tbl_mock_109.mock_113 NOT IN ('C直采', 'C代理');", "db_mock_000.tbl_mock_121": "SELECT tbl_mock_119.mock_095, tbl_mock_119.mock_100, tbl_mock_119.mock_094, tbl_mock_119.mock_082, tbl_mock_119.mock_090, tbl_mock_119.mock_092, tbl_mock_119.mock_096, tbl_mock_119.mock_097, 0 AS mock_144\nFROM db_mock_000.tbl_mock_119 UNION ALL SELECT tbl_mock_109.mock_095, tbl_mock_109.mock_100, tbl_mock_109.mock_094, tbl_mock_109.mock_110 AS mock_082, tbl_mock_109.mock_092 AS mock_090, tbl_mock_109.mock_092, 'C' AS mock_096, 'q' AS mock_097, 1 AS mock_144\nFROM db_mock_000.tbl_mock_109\nWHERE (tbl_mock_109.mock_096 IN ('CANCELLED', 'REJECTED')) AND (tbl_mock_109.mock_113 NOT IN ('C直采', 'C代理'));", "db_mock_000.tbl_mock_122": "SELECT CAST(tbl_mock_130.mock_094 AS DATE) AS mock_126, tbl_mock_130.mock_092, tbl_mock_130.mock_097, date_trunc('month', CAST(tbl_mock_130.mock_094 AS DATE)) AS mock_132, CASE WHEN (tbl_mock_130.mock_144 = 1) THEN 3 WHEN (tbl_mock_129.mock_118 IS NULL) THEN 3 ELSE tbl_mock_129.mock_118 END AS mock_118, count(tbl_mock_130.mock_095) AS mock_125, 0 AS mock_133, 0 AS mock_134, sum(tbl_mock_130.mock_100) AS mock_100, 0 AS mock_135, 0 AS mock_136, 0 AS mock_137, 0 AS mock_138, 0 AS mock_139, CASE WHEN (tbl_mock_130.mock_096 = 'C') THEN 'C' ELSE 'NC' END AS mock_096, hour(tbl_mock_130.mock_094) AS mock_127, minute(tbl_mock_130.mock_094) AS mock_140, NULL AS mock_141, NULL AS mock_142, NULL AS mock_143\nFROM db_mock_000.tbl_mock_120 AS tbl_mock_130 LEFT OUTER JOIN db_mock_000.tbl_mock_117 AS tbl_mock_129 ON tbl_mock_130.mock_090 = tbl_mock_129.mock_090\nWHERE tbl_mock_130.mock_094 >= (date(date_sub(curdate(), INTERVAL 7 DAY)))\nGROUP BY tbl_mock_130.mock_092, tbl_mock_130.mock_097, mock_126, CASE WHEN (tbl_mock_130.mock_144 = 1) THEN 3 WHEN (tbl_mock_129.mock_118 IS NULL) THEN 3 ELSE tbl_mock_129.mock_118 END, tbl_mock_130.mock_096, mock_127, mock_140 UNION ALL SELECT NULL AS mock_126, tbl_mock_130.mock_092, tbl_mock_130.mock_097, NULL AS mock_132, CASE WHEN (tbl_mock_130.mock_144 = 1) THEN 3 WHEN (tbl_mock_129.mock_118 IS NULL) THEN 3 ELSE tbl_mock_129.mock_118 END AS mock_118, count(tbl_mock_130.mock_095) AS mock_125, 0 AS mock_133, 0 AS mock_134, sum(tbl_mock_130.mock_100) AS mock_100, 0 AS mock_135, 0 AS mock_136, 0 AS mock_137, 0 AS mock_138, 0 AS mock_139, 'C' AS mock_096, NULL AS mock_127, NULL AS mock_140, hour(tbl_mock_130.mock_082) AS mock_141, minute(tbl_mock_130.mock_082) AS mock_142, CAST(tbl_mock_130.mock_082 AS DATE) AS mock_143\nFROM db_mock_000.tbl_mock_121 AS tbl_mock_130 LEFT OUTER JOIN db_mock_000.tbl_mock_117 AS tbl_mock_129 ON tbl_mock_130.mock_090 = tbl_mock_129.mock_090\nWHERE tbl_mock_130.mock_082 >= (date(date_sub(curdate(), INTERVAL 7 DAY)))\nGROUP BY tbl_mock_130.mock_092, tbl_mock_130.mock_097, CASE WHEN (tbl_mock_130.mock_144 = 1) THEN 3 WHEN (tbl_mock_129.mock_118 IS NULL) THEN 3 ELSE tbl_mock_129.mock_118 END, mock_127, mock_140, mock_141, mock_142, mock_143;"}, "column_statistics": {"db_mock_000.tbl_mock_001": {"mock_057": "[1.0, 1.0, 0.0, 4.0, 1.0] ESTIMATE", "mock_035": "[215.0, 1.20754936E8, 0.0, 4.0, 54029.0] ESTIMATE"}, "db_mock_000.tbl_mock_117": {"mock_090": "[10.0, 6651124.0, 0.0, 4.0, 2.84E7] ESTIMATE", "mock_118": "[0.0, 3.0, 0.0, 4.0, 3.0] ESTIMATE"}, "db_mock_000.tbl_mock_109": {"mock_096": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "mock_095": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "mock_113": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "mock_092": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "mock_094": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN"}, "db_mock_000.tbl_mock_078": {"mock_096": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "mock_095": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "mock_090": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "mock_092": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "mock_094": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "mock_097": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN"}}, "explain_info": "PLAN FRAGMENT 0(F10)\n  Output Exprs:258: sum\n  Input Partition: UNPARTITIONED\n  RESULT SINK\n\n  23:AGGREGATE (merge finalize)\n  |  aggregate: sum[([258: sum, BIGINT, true]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: true]\n  |  hasNullableGenerateChild: true\n  |  limit: 2\n  |  cardinality: 1\n  |  column statistics: \n  |  * sum-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  22:EXCHANGE\n     distribution type: GATHER\n     cardinality: 1\n\nPLAN FRAGMENT 1(F00)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange id: 22\n\n  21:AGGREGATE (update serialize)\n  |  aggregate: sum[([243: count, BIGINT, true]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 1\n  |  column statistics: \n  |  * sum-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  20:Project\n  |  output columns:\n  |  243 <-> [243: count, BIGINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 12788588\n  |  column statistics: \n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  19:HASH JOIN\n  |  join op: INNER JOIN (BUCKET_SHUFFLE)\n  |  equal join conjunct: [4: mock_035, INT, false] = [239: mock_092, INT, true]\n  |  build runtime filters:\n  |  - filter_id = 0, build_expr = (239: mock_092), remote = false\n  |  output columns: 243\n  |  can local shuffle: true\n  |  cardinality: 12788588\n  |  column statistics: \n  |  * mock_035-->[215.0, 1.20754936E8, 0.0, 4.0, 54029.0] ESTIMATE\n  |  * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  |----18:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [239: mock_092, INT, true]\n  |       cardinality: 12788588\n  |    \n  1:Project\n  |  output columns:\n  |  4 <-> [4: mock_035, INT, false]\n  |  cardinality: 1944369\n  |  column statistics: \n  |  * mock_035-->[215.0, 1.20754936E8, 0.0, 4.0, 54029.0] ESTIMATE\n  |  \n  0:OlapScanNode\n     table: mock_001, rollup: mock_001\n     preAggregation: on\n     Predicates: [11: mock_057, INT, true] != 53\n     partitionsRatio=1/1, tabletsRatio=10/10\n     tabletList=144309593,144309597,144309601,144309605,144309609,144309613,144309617,144309621,144309625,144309629\n     actualRows=1944369, avgRowSize=8.0\n     cardinality: 1944369\n     probe runtime filters:\n     - filter_id = 0, probe_expr = (4: mock_035)\n     column statistics: \n     * mock_035-->[215.0, 1.20754936E8, 0.0, 4.0, 54029.0] ESTIMATE\n     * mock_057-->[1.0, 1.0, 0.0, 4.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 2(F08)\n\n  Input Partition: HASH_PARTITIONED: 137: mock_092, 139: mock_097, 143: cast, 144: case, 138: mock_096, 145: mock_091, 146: minute\n  OutPut Partition: BUCKET_SHUFFLE_HASH_PARTITIONED: 239: mock_092\n  OutPut Exchange id: 18\n\n  17:Project\n  |  output columns:\n  |  239 <-> [137: mock_092, INT, true]\n  |  243 <-> [147: count, BIGINT, false]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 12788588\n  |  column statistics: \n  |  * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  16:AGGREGATE (merge finalize)\n  |  aggregate: count[([147: count, BIGINT, false]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: false]\n  |  group by: [137: mock_092, INT, true], [139: mock_097, VARCHAR(5), true], [143: cast, DATE, true], [144: case, INT, true], [138: mock_096, VARCHAR(5), true], [145: mock_091, TINYINT, true], [146: minute, TINYINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 12788588\n  |  column statistics: \n  |  * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_096-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_097-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * cast-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * case-->[-Infinity, Infinity, 0.0, 4.0, 5.0] ESTIMATE\n  |  * mock_091-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * minute-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  15:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [137: mock_092, INT, true], [139: mock_097, VARCHAR(5), true], [143: cast, DATE, true], [144: case, INT, true], [138: mock_096, VARCHAR(5), true], [145: mock_091, TINYINT, true], [146: minute, TINYINT, true]\n     cardinality: 19086082\n\nPLAN FRAGMENT 3(F07)\n\n  Input Partition: HASH_PARTITIONED: 136: mock_090\n  OutPut Partition: HASH_PARTITIONED: 137: mock_092, 139: mock_097, 143: cast, 144: case, 138: mock_096, 145: mock_091, 146: minute\n  OutPut Exchange id: 15\n\n  14:AGGREGATE (update serialize)\n  |  STREAMING\n  |  aggregate: count[([133: mock_095, BIGINT, true]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: false]\n  |  group by: [137: mock_092, INT, true], [139: mock_097, VARCHAR(5), true], [143: cast, DATE, true], [144: case, INT, true], [138: mock_096, VARCHAR(5), true], [145: mock_091, TINYINT, true], [146: minute, TINYINT, true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 19086082\n  |  column statistics: \n  |  * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_096-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_097-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * cast-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * case-->[-Infinity, Infinity, 0.0, 4.0, 5.0] ESTIMATE\n  |  * mock_091-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * minute-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * count-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  13:Project\n  |  output columns:\n  |  133 <-> [133: mock_095, BIGINT, true]\n  |  137 <-> [137: mock_092, INT, true]\n  |  138 <-> [138: mock_096, VARCHAR(5), true]\n  |  139 <-> [139: mock_097, VARCHAR(5), true]\n  |  143 <-> cast([135: mock_094, DATETIME, false] as DATE)\n  |  144 <-> CASE WHEN 140: expr = 1 THEN 3 WHEN 142: mock_118 IS NULL THEN 3 ELSE 142: mock_118 END\n  |  145 <-> mock_091[([135: mock_094, DATETIME, false]); args: DATETIME; result: TINYINT; args nullable: false; result nullable: false]\n  |  146 <-> minute[([135: mock_094, DATETIME, false]); args: DATETIME; result: TINYINT; args nullable: false; result nullable: true]\n  |  hasNullableGenerateChild: true\n  |  cardinality: 28484657\n  |  column statistics: \n  |  * mock_095-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_096-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_097-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * cast-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * case-->[-Infinity, Infinity, 0.0, 4.0, 5.0] ESTIMATE\n  |  * mock_091-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * minute-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  12:HASH JOIN\n  |  join op: LEFT OUTER JOIN (PARTITIONED)\n  |  equal join conjunct: [136: mock_090, INT, false] = [141: mock_090, INT, true]\n  |  output columns: 133, 135, 137, 138, 139, 140, 142\n  |  can local shuffle: true\n  |  cardinality: 28484657\n  |  column statistics: \n  |  * mock_095-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_094-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_090-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_096-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_097-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * expr-->[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  * mock_090-->[10.0, 6651124.0, 0.0, 4.0, 2.84E7] ESTIMATE\n  |  * mock_118-->[0.0, 3.0, 0.0, 4.0, 3.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * case-->[-Infinity, Infinity, 0.0, 4.0, 5.0] ESTIMATE\n  |  * mock_091-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * minute-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  |----11:EXCHANGE\n  |       distribution type: SHUFFLE\n  |       partition exprs: [141: mock_090, INT, true]\n  |       cardinality: 28484657\n  |    \n  9:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [136: mock_090, INT, false]\n     cardinality: 354320\n\nPLAN FRAGMENT 4(F05)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 141: mock_090\n  OutPut Exchange id: 11\n\n  10:OlapScanNode\n     table: mock_117, rollup: mock_117\n     preAggregation: on\n     partitionsRatio=1/1, tabletsRatio=10/10\n     tabletList=144326315,144326319,144326323,144326327,144326331,144326335,144326339,144326343,144326347,144326351\n     actualRows=28484657, avgRowSize=8.0\n     cardinality: 28484657\n     column statistics: \n     * mock_090-->[10.0, 6651124.0, 0.0, 4.0, 2.84E7] ESTIMATE\n     * mock_118-->[0.0, 3.0, 0.0, 4.0, 3.0] ESTIMATE\n\nPLAN FRAGMENT 5(F01)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 136: mock_090\n  OutPut Exchange id: 09\n\n  2:UNION\n  |  output exprs:\n  |      [133, BIGINT, true] | [135, DATETIME, false] | [136, INT, false] | [137, INT, true] | [138, VARCHAR(5), true] | [139, VARCHAR(5), true] | [140, TINYINT, false]\n  |  child exprs:\n  |      [77: mock_095, BIGINT, false] | [78: mock_094, DATETIME, false] | [79: mock_090, INT, false] | [84: mock_092, INT, true] | [88: mock_096, VARCHAR, true] | [104: mock_097, VARCHAR, true] | [107: expr, TINYINT, false]\n  |      [111: mock_095, BIGINT, true] | [108: mock_094, DATETIME, false] | [112: mock_092, INT, false] | [112: mock_092, INT, false] | [131: cast, VARCHAR, true] | [132: cast, VARCHAR, false] | [130: expr, TINYINT, false]\n  |  pass-through-operands: all\n  |  cardinality: 354320\n  |  column statistics: \n  |  * mock_095-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_094-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_090-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_096-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_097-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * expr-->[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE\n  |  \n  |----8:EXCHANGE\n  |       cardinality: 27160\n  |    \n  5:EXCHANGE\n     cardinality: 327160\n\nPLAN FRAGMENT 6(F03)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange id: 08\n\n  7:Project\n  |  output columns:\n  |  108 <-> [108: mock_094, DATETIME, false]\n  |  111 <-> [111: mock_095, BIGINT, true]\n  |  112 <-> [112: mock_092, INT, false]\n  |  130 <-> 1\n  |  131 <-> if[(117: mock_096 IN ('CANCELLED', 'REJECTED'), 'C', 'P'); args: BOOLEAN,VARCHAR,VARCHAR; result: VARCHAR; args nullable: true; result nullable: true]\n  |  132 <-> 'q'\n  |  cardinality: 27160\n  |  column statistics: \n  |  * mock_094-->[1.7187264E9, 1.7188128E9, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_095-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * expr-->[1.0, 1.0, 0.0, 1.0, 1.0] ESTIMATE\n  |  * cast-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * cast-->[-Infinity, Infinity, 0.0, 1.0, 1.0] ESTIMATE\n  |  \n  6:OlapScanNode\n     table: mock_109, rollup: mock_109\n     preAggregation: on\n     Predicates: mock_091[([108: mock_094, DATETIME, false]); args: DATETIME; result: TINYINT; args nullable: false; result nullable: false] < 17, 121: mock_113 NOT IN ('C直采', 'C代理')\n     partitionsRatio=1/11, tabletsRatio=10/10\n     tabletList=143877276,143877280,143877284,143877288,143877292,143877296,143877300,143877304,143877308,143877312\n     actualRows=55017, avgRowSize=8.0\n     cardinality: 27160\n     column statistics: \n     * mock_094-->[1.7187264E9, 1.7188128E9, 0.0, 1.0, 1.0] UNKNOWN\n     * mock_095-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * mock_096-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * mock_113-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * expr-->[1.0, 1.0, 0.0, 1.0, 1.0] ESTIMATE\n     * cast-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * cast-->[-Infinity, Infinity, 0.0, 1.0, 1.0] ESTIMATE\n\nPLAN FRAGMENT 7(F02)\n\n  Input Partition: RANDOM\n  OutPut Partition: RANDOM\n  OutPut Exchange id: 05\n\n  4:Project\n  |  output columns:\n  |  77 <-> [77: mock_095, BIGINT, false]\n  |  78 <-> [78: mock_094, DATETIME, false]\n  |  79 <-> [79: mock_090, INT, false]\n  |  84 <-> [84: mock_092, INT, true]\n  |  88 <-> [88: mock_096, VARCHAR, true]\n  |  104 <-> [104: mock_097, VARCHAR, true]\n  |  107 <-> 0\n  |  cardinality: 327160\n  |  column statistics: \n  |  * mock_095-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_094-->[1.7187264E9, 1.7188128E9, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_090-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_096-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * mock_097-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * expr-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n  |  \n  3:OlapScanNode\n     table: mock_078, rollup: mock_078\n     preAggregation: on\n     Predicates: mock_091[([78: mock_094, DATETIME, false]); args: DATETIME; result: TINYINT; args nullable: false; result nullable: false] < 17\n     partitionsRatio=1/64, tabletsRatio=10/10\n     tabletList=143876822,143876826,143876830,143876834,143876838,143876842,143876846,143876850,143876854,143876858\n     actualRows=653172, avgRowSize=7.0\n     cardinality: 327160\n     column statistics: \n     * mock_095-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * mock_094-->[1.7187264E9, 1.7188128E9, 0.0, 1.0, 1.0] UNKNOWN\n     * mock_090-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * mock_092-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * mock_096-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * mock_097-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * expr-->[0.0, 0.0, 0.0, 1.0, 1.0] ESTIMATE\n", "session_variables": "{\"partial_update_mode\":\"auto\",\"cbo_cte_reuse\":true,\"character_set_connection\":\"utf8\",\"cbo_use_correlated_join_estimate\":true,\"enable_insert_strict\":true,\"enable_connector_adaptive_io_tasks\":true,\"tx_isolation\":\"REPEATABLE-READ\",\"enable_hive_metadata_cache_with_insert\":false,\"cbo_cte_reuse_rate_v2\":1.15,\"character_set_results\":\"utf8\",\"enable_iceberg_column_statistics\":true,\"enable_count_star_optimization\":true,\"global_runtime_filter_build_min_size\":131072,\"enable_iceberg_identity_column_optimize\":true,\"query_excluding_mv_names\":\"\",\"enable_rewrite_simple_agg_to_meta_scan\":false,\"enable_adaptive_sink_dop\":false,\"consistent_hash_virtual_number\":32,\"enable_profile\":false,\"load_mem_limit\":0,\"cbo_eq_base_type\":\"varchar\",\"enable_materialized_view_for_insert\":false,\"large_decimal_underlying_type\":\"panic\",\"sql_safe_updates\":0,\"runtime_filter_early_return_selectivity\":0.05,\"enable_local_shuffle_agg\":true,\"disable_function_fold_constants\":false,\"enable_query_queue\":true,\"select_ratio_threshold\":0.15,\"query_delivery_timeout\":300,\"collation_database\":\"utf8_general_ci\",\"spill_mem_table_size\":104857600,\"follower_query_forward_mode\":\"\",\"cbo_use_lock_db\":false,\"new_planner_agg_stage\":0,\"enable_strict_order_by\":true,\"hash_join_interpolate_passthrough\":false,\"use_compute_nodes\":-1,\"collation_connection\":\"utf8_general_ci\",\"enable_rewrite_bitmap_union_to_bitamp_agg\":false,\"enable_array_distinct_after_agg_opt\":true,\"resource_group\":\"\",\"enable_materialized_view_plan_cache\":true,\"spill_operator_max_bytes\":1048576000,\"cbo_max_reorder_node_use_dp\":10,\"enable_result_sink_accumulate\":true,\"enable_hive_column_stats\":true,\"enable_async_profile\":true,\"enable_groupby_use_output_alias\":false,\"global_runtime_filter_wait_timeout\":20,\"forward_to_leader\":false,\"count_distinct_column_buckets\":1024,\"cross_join_cost_penalty\":1000000,\"query_cache_agg_cardinality_limit\":5000000,\"enable_pipeline_query_statistic\":true,\"cboPushDownAggregateMode_v1\":-1,\"window_partition_mode\":1,\"enable_deliver_batch_fragments\":true,\"enable_tablet_internal_parallel_v2\":true,\"interpolate_passthrough\":true,\"enable_incremental_mv\":false,\"cbo_push_down_topn_limit\":0,\"SQL_AUTO_IS_NULL\":false,\"event_scheduler\":\"OFF\",\"max_pipeline_dop\":64,\"broadcast_right_table_scale_factor\":10,\"materialized_view_rewrite_mode\":\"DEFAULT\",\"enable_simplify_case_when\":true,\"runtime_join_filter_push_down_limit\":1024000,\"big_query_log_cpu_second_threshold\":480,\"div_precision_increment\":4,\"runtime_adaptive_dop_max_block_rows_per_driver_seq\":16384,\"log_rejected_record_num\":0,\"cbo_push_down_distinct_below_window\":true,\"sql_mode_v2\":32,\"prefer_cte_rewrite\":false,\"optimizer_materialized_view_timelimit\":1000,\"hdfs_backend_selector_scan_range_shuffle\":false,\"pipeline_profile_level\":1,\"parallel_fragment_exec_instance_num\":4,\"max_scan_key_num\":-1,\"net_read_timeout\":60,\"streaming_preaggregation_mode\":\"auto\",\"hive_partition_stats_sample_size\":5000,\"enable_mv_planner\":false,\"enable_collect_table_level_scan_stats\":true,\"query_debug_options\":\"\",\"profile_timeout\":2,\"cbo_push_down_aggregate\":\"global\",\"spill_encode_level\":7,\"enable_query_dump\":false,\"global_runtime_filter_build_max_size\":67108864,\"enable_rewrite_sum_by_associative_rule\":true,\"query_cache_hot_partition_num\":3,\"enable_prune_complex_types\":true,\"query_cache_type\":0,\"max_parallel_scan_instance_num\":-1,\"query_cache_entry_max_rows\":409600,\"enable_mv_optimizer_trace_log\":false,\"connector_io_tasks_per_scan_operator\":16,\"enable_materialized_view_union_rewrite\":true,\"sql_quote_show_create\":true,\"scan_or_to_union_threshold\":50000000,\"enable_materialized_view_rewrite_partition_compensate\":true,\"enable_exchange_pass_through\":true,\"runtime_profile_report_interval\":10,\"query_cache_entry_max_bytes\":4194304,\"enable_partition_column_value_only_optimization\":true,\"enable_iceberg_ndv\":true,\"enable_exchange_perf\":false,\"workgroup_id\":0,\"enable_rewrite_groupingsets_to_union_all\":false,\"transmission_compression_type\":\"LZ4\",\"interactive_timeout\":28800,\"use_page_cache\":true,\"big_query_log_scan_bytes_threshold\":10737418240,\"collation_server\":\"utf8_general_ci\",\"cbo_decimal_cast_string_strict\":true,\"tablet_internal_parallel_mode\":\"auto\",\"enable_pipeline\":true,\"spill_mode\":\"auto\",\"enable_query_debug_trace\":false,\"cbo_materialized_view_rewrite_related_mvs_limit\":64,\"enable_show_all_variables\":false,\"full_sort_max_buffered_bytes\":16777216,\"wait_timeout\":28800,\"enable_query_tablet_affinity\":false,\"transmission_encode_level\":7,\"query_including_mv_names\":\"\",\"transaction_isolation\":\"REPEATABLE-READ\",\"enable_global_runtime_filter\":false,\"enable_load_profile\":false,\"enable_plan_validation\":true,\"load_transmission_compression_type\":\"NO_COMPRESSION\",\"global_runtime_filter_rpc_http_min_size\":67108864,\"cbo_materialized_view_rewrite_rule_output_limit\":3,\"cbo_enable_low_cardinality_optimize\":true,\"scan_use_query_mem_ratio\":0.3,\"new_planner_optimize_timeout\":3000,\"enable_outer_join_reorder\":true,\"force_schedule_local\":false,\"hudi_mor_force_jni_reader\":false,\"full_sort_late_materialization\":false,\"cbo_enable_greedy_join_reorder\":true,\"range_pruner_max_predicate\":100,\"enable_rbo_table_prune\":false,\"spillable_operator_mask\":-1,\"rpc_http_min_size\":2147482624,\"cbo_debug_alive_backend_number\":0,\"global_runtime_filter_probe_min_size\":102400,\"scan_or_to_union_limit\":1,\"enable_cbo_table_prune\":false,\"enable_parallel_merge\":true,\"cbo_materialized_view_rewrite_candidate_limit\":12,\"nested_mv_rewrite_max_level\":3,\"big_query_profile_threshold\":\"0s\",\"net_write_timeout\":60,\"cbo_prune_shuffle_column_rate\":0.1,\"enable_persistent_index_by_default\":false,\"hash_join_push_down_right_table\":true,\"pipeline_sink_dop\":0,\"broadcast_row_limit\":15000000,\"enable_populate_block_cache\":true,\"exec_mem_limit\":6147483648,\"enable_sort_aggregate\":false,\"query_cache_force_populate\":false,\"runtime_filter_on_exchange_node\":false,\"disable_join_reorder\":true,\"enable_rule_based_materialized_view_rewrite\":true,\"global_runtime_filter_rpc_timeout\":400,\"connector_scan_use_query_mem_ratio\":0.3,\"net_buffer_length\":16384,\"cbo_prune_subfield\":true,\"full_sort_max_buffered_rows\":1024000,\"query_timeout\":300,\"connector_io_tasks_slow_io_latency_ms\":50,\"cbo_max_reorder_node\":50,\"enable_distinct_column_bucketization\":true,\"enable_big_query_log\":true,\"analyze_mv\":\"sample\",\"runtime_filter_scan_wait_time\":20,\"enable_sync_materialized_view_rewrite\":true,\"prefer_compute_node\":false,\"enable_strict_type\":false,\"enable_table_prune_on_update\":false,\"group_concat_max_len\":65535,\"parse_tokens_limit\":3500000,\"chunk_size\":4096,\"global_runtime_filter_probe_min_selectivity\":0.5,\"query_mem_limit\":0,\"enable_filter_unused_columns_in_scan_stage\":false,\"enable_scan_block_cache\":false,\"enable_materialized_view_single_table_view_delta_rewrite\":false,\"enable_prune_complex_types_in_unnest\":true,\"auto_increment_increment\":1,\"sql_dialect\":\"StarRocks\",\"big_query_log_scan_rows_threshold\":1000000000,\"character_set_client\":\"utf8\",\"autocommit\":true,\"enable_column_expr_predicate\":false,\"enable_runtime_adaptive_dop\":false,\"cbo_cte_max_limit\":10,\"storage_engine\":\"olap\",\"enable_optimizer_trace_log\":false,\"spill_operator_min_bytes\":10485760,\"cbo_enable_dp_join_reorder\":true,\"tx_visible_wait_timeout\":10,\"materialized_view_join_same_table_permutation_limit\":5,\"enable_materialized_view_view_delta_rewrite\":true,\"cbo_max_reorder_node_use_exhaustive\":4,\"enable_sql_digest\":false,\"spill_mem_table_num\":2,\"enable_spill\":false,\"enable_materialized_view_rewrite_greedy_mode\":false,\"pipeline_dop\":0,\"single_node_exec_plan\":false,\"join_implementation_mode_v2\":\"auto\",\"sql_select_limit\":9223372036854775807,\"enable_materialized_view_rewrite\":true,\"statistic_collect_parallel\":1,\"hdfs_backend_selector_hash_algorithm\":\"consistent\",\"enable_expr_prune_partition\":true,\"enable_topn_runtime_filter\":true,\"disable_colocate_join\":false,\"max_pushdown_conditions_per_column\":-1,\"default_table_compression\":\"lz4_frame\",\"runtime_adaptive_dop_max_output_amplification_factor\":0,\"choose_execute_instances_mode\":\"LOCALITY\",\"innodb_read_only\":true,\"spill_mem_limit_threshold\":0.8,\"cbo_reorder_threshold_use_exhaustive\":6,\"enable_predicate_reorder\":false,\"enable_query_cache\":true,\"transaction_read_only\":\"OFF\",\"max_allowed_packet\":1048576,\"time_zone\":\"Asia/Shanghai\",\"enable_multicolumn_global_runtime_filter\":false,\"character_set_server\":\"utf8\",\"cbo_use_nth_exec_plan\":0,\"io_tasks_per_scan_operator\":4,\"parallel_exchange_instance_num\":-1,\"enable_shared_scan\":false,\"cbo_derive_range_join_predicate\":false,\"allow_default_partition\":false,\"enable_pipeline_level_shuffle\":true}", "be_number": 4.0, "be_core_stat": {"numOfHardwareCoresPerBe": "{\"97668240\":112,\"97668266\":112,\"97668264\":112,\"97668430\":112}", "cachedAvgNumOfHardwareCores": 112.0}, "exception": [], "version": "3.1.9", "commit_version": "e1c6e4e"}