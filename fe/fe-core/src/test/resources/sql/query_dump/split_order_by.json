{"statement": "select a.k3||':'||b.k4 as col1,sum(a.k6+b.k6) as col2 from aggregate_tbl a inner join unique_par_tbl partition(p202008) b on a.k12 = b.k12 and a.k9 = b.k9 where a.k3 like(with tbl1 as  (select distinct k3 from duplicate_tbl),  tbl2 as  (select distinct k3 from aggregate_par_tbl partition(p202006) where k4 not in('futian','baiyun','luohu'))  select * from tbl1 where k3 <>(select * from tbl2 order by 1 nulls last limit 1)  order by 1 limit 1)  and a.k13 >= 0  and b.k5 is not null  group by 1 order by 2 desc,1;\n", "table_meta": {"test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.aggregate_tbl": "CREATE TABLE `aggregate_tbl` (\n  `k1` date NULL COMMENT \"\",\n  `k2` datetime NULL COMMENT \"\",\n  `k3` char(20) NULL COMMENT \"\",\n  `k4` varchar(20) NULL COMMENT \"\",\n  `k5` boolean NULL COMMENT \"\",\n  `k6` bigint(20) SUM NULL COMMENT \"\",\n  `k7` bigint(20) SUM NULL COMMENT \"\",\n  `k8` int(11) SUM NULL COMMENT \"\",\n  `k9` bigint(20) MAX NULL COMMENT \"\",\n  `k10` largeint(40) MAX NULL COMMENT \"\",\n  `k11` float MIN NULL COMMENT \"\",\n  `k12` double MIN NULL COMMENT \"\",\n  `k13` decimal(38, 9) SUM NULL COMMENT \"\"\n) ENGINE=OLAP \nAGGREGATE KEY(`k1`, `k2`, `k3`, `k4`, `k5`)\nDISTRIBUTED BY HASH(`k1`, `k2`, `k3`, `k4`, `k5`) BUCKETS 3 \nPROPERTIES (\n\"compression\" = \"LZ4\",\n\"fast_schema_evolution\" = \"true\",\n\"replicated_storage\" = \"true\",\n\"replication_num\" = \"1\"\n);", "test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.unique_par_tbl": "CREATE TABLE `unique_par_tbl` (\n  `k1` date NULL COMMENT \"\",\n  `k2` datetime NULL COMMENT \"\",\n  `k3` char(20) NULL COMMENT \"\",\n  `k4` varchar(20) NULL COMMENT \"\",\n  `k5` boolean NULL COMMENT \"\",\n  `k6` tinyint(4) NULL COMMENT \"\",\n  `k7` smallint(6) NULL COMMENT \"\",\n  `k8` int(11) NULL COMMENT \"\",\n  `k9` bigint(20) NULL COMMENT \"\",\n  `k10` largeint(40) NULL COMMENT \"\",\n  `k11` float NULL COMMENT \"\",\n  `k12` double NULL COMMENT \"\",\n  `k13` decimal(27, 9) NULL COMMENT \"\"\n) ENGINE=OLAP \nUNIQUE KEY(`k1`, `k2`, `k3`, `k4`, `k5`)\nCOMMENT \"OLAP\"\nPARTITION BY RANGE(`k1`)\n(PARTITION p202006 VALUES [(\"0000-01-01\"), (\"2020-07-01\")),\nPARTITION p202007 VALUES [(\"2020-07-01\"), (\"2020-08-01\")),\nPARTITION p202008 VALUES [(\"2020-08-01\"), (\"2020-09-01\")))\nDISTRIBUTED BY HASH(`k1`, `k2`, `k3`, `k4`, `k5`) BUCKETS 3 \nPROPERTIES (\n\"compression\" = \"LZ4\",\n\"fast_schema_evolution\" = \"true\",\n\"replicated_storage\" = \"true\",\n\"replication_num\" = \"1\"\n);", "test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.duplicate_tbl": "CREATE TABLE `duplicate_tbl` (\n  `k1` date NULL COMMENT \"\",\n  `k2` datetime NULL COMMENT \"\",\n  `k3` char(20) NULL COMMENT \"\",\n  `k4` varchar(20) NULL COMMENT \"\",\n  `k5` boolean NULL COMMENT \"\",\n  `k6` tinyint(4) NULL COMMENT \"\",\n  `k7` smallint(6) NULL COMMENT \"\",\n  `k8` int(11) NULL COMMENT \"\",\n  `k9` bigint(20) NULL COMMENT \"\",\n  `k10` largeint(40) NULL COMMENT \"\",\n  `k11` float NULL COMMENT \"\",\n  `k12` double NULL COMMENT \"\",\n  `k13` decimal(27, 9) NULL COMMENT \"\"\n) ENGINE=OLAP \nDUPLICATE KEY(`k1`, `k2`, `k3`, `k4`, `k5`)\nDISTRIBUTED BY HASH(`k1`, `k2`, `k3`) BUCKETS 3 \nPROPERTIES (\n\"compression\" = \"LZ4\",\n\"fast_schema_evolution\" = \"true\",\n\"replicated_storage\" = \"true\",\n\"replication_num\" = \"1\"\n);", "test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.aggregate_par_tbl": "CREATE TABLE `aggregate_par_tbl` (\n  `k1` date NULL COMMENT \"\",\n  `k2` datetime NULL COMMENT \"\",\n  `k3` char(20) NULL COMMENT \"\",\n  `k4` varchar(20) NULL COMMENT \"\",\n  `k5` boolean NULL COMMENT \"\",\n  `k6` bigint(20) SUM NULL COMMENT \"\",\n  `k7` bigint(20) SUM NULL COMMENT \"\",\n  `k8` int(11) SUM NULL COMMENT \"\",\n  `k9` bigint(20) MAX NULL COMMENT \"\",\n  `k10` largeint(40) MAX NULL COMMENT \"\",\n  `k11` float MIN NULL COMMENT \"\",\n  `k12` double MIN NULL COMMENT \"\",\n  `k13` decimal(38, 9) SUM NULL COMMENT \"\"\n) ENGINE=OLAP \nAGGREGATE KEY(`k1`, `k2`, `k3`, `k4`, `k5`)\nCOMMENT \"OLAP\"\nPARTITION BY RANGE(`k1`)\n(PARTITION p202006 VALUES [(\"0000-01-01\"), (\"2020-07-01\")),\nPARTITION p202007 VALUES [(\"2020-07-01\"), (\"2020-08-01\")),\nPARTITION p202008 VALUES [(\"2020-08-01\"), (\"2020-09-01\")))\nDISTRIBUTED BY HASH(`k1`, `k2`, `k3`, `k4`, `k5`) BUCKETS 3 \nPROPERTIES (\n\"compression\" = \"LZ4\",\n\"fast_schema_evolution\" = \"true\",\n\"replicated_storage\" = \"true\",\n\"replication_num\" = \"1\"\n);"}, "table_row_count": {"test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.aggregate_tbl": {"aggregate_tbl": 65536}, "test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.duplicate_tbl": {"duplicate_tbl": 65536}, "test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.unique_par_tbl": {"p202006": 8192, "p202007": 31744, "p202008": 25600}, "test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.aggregate_par_tbl": {"p202006": 8192, "p202007": 31744, "p202008": 25600}}, "column_statistics": {"test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.aggregate_tbl": {"k3": "[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE", "k6": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "k13": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "k12": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "k9": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN"}, "test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.duplicate_tbl": {"k3": "[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE"}, "test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.unique_par_tbl": {"k1": "[1.5928416E9, 1.5982848E9, 0.0, 4.0, 64.0] ESTIMATE", "k4": "[-Infinity, Infinity, 0.0, 6.25, 8.0] ESTIMATE", "k5": "[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE", "k6": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "k12": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN", "k9": "[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN"}, "test_multi_cte_82528cec_23cf_11ef_be03_00163e0e489a.aggregate_par_tbl": {"k1": "[1.5928416E9, 1.5982848E9, 0.0, 4.0, 64.0] ESTIMATE", "k3": "[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE", "k4": "[-Infinity, Infinity, 0.0, 6.25, 8.0] ESTIMATE"}}, "explain_info": "PLAN FRAGMENT 0(F12)\n  Output Exprs:55: concat | 57: sum\n  Input Partition: UNPARTITIONED\n  RESULT SINK\n\n  32:MERGING-EXCHANGE\n     distribution type: GATHER\n     cardinality: 0\n     column statistics: \n     * concat-->[-Infinity, Infinity, 0.0, 15.25, NaN] ESTIMATE\n     * sum-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n\nPLAN FRAGMENT 1(F11)\n\n  Input Partition: HASH_PARTITIONED: 55: concat\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 32\n\n  31:SORT\n  |  order by: [57, BIGINT, true] DESC, [55, VARCHAR, true] ASC\n  |  offset: 0\n  |  cardinality: 13\n  |  column statistics: \n  |  * concat-->[-Infinity, Infinity, 0.0, 15.25, 13.0] ESTIMATE\n  |  * sum-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  30:AGGREGATE (merge finalize)\n  |  aggregate: sum[([57: sum, BIGINT, true]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: true]\n  |  group by: [55: concat, VARCHAR, true]\n  |  cardinality: 13\n  |  column statistics: \n  |  * concat-->[-<PERSON>, <PERSON>, 0.0, 15.25, 13.0] ESTIMATE\n  |  * sum-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  29:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [55: concat, VARCHAR, true]\n     cardinality: 13\n\nPLAN FRAGMENT 2(F00)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 55: concat\n  OutPut Exchange Id: 29\n\n  28:AGGREGATE (update serialize)\n  |  STREAMING\n  |  aggregate: sum[([56: expr, BIGINT, true]); args: BIGINT; result: BIGINT; args nullable: true; result nullable: true]\n  |  group by: [55: concat, VARCHAR, true]\n  |  cardinality: 13\n  |  column statistics: \n  |  * concat-->[-Infinity, Infinity, 0.0, 15.25, 13.0] ESTIMATE\n  |  * sum-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  27:Project\n  |  output columns:\n  |  55 <-> concat[(concat[([3: k3, VARCHAR, true], ':'); args: VARCHAR; result: VARCHAR; args nullable: true; result nullable: true], DictDecode(59: k4, [<place-holder>])); args: VARCHAR; result: VARCHAR; args nullable: true; result nullable: true]\n  |  56 <-> [6: k6, BIGINT, true] + cast([19: k6, TINYINT, true] as BIGINT)\n  |  cardinality: 25600\n  |  column statistics: \n  |  * concat-->[-Infinity, Infinity, 0.0, 15.25, 13.0] ESTIMATE\n  |  * expr-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  26:HASH JOIN\n  |  join op: INNER JOIN (BROADCAST)\n  |  equal join conjunct: [12: k12, DOUBLE, true] = [25: k12, DOUBLE, true]\n  |  equal join conjunct: [9: k9, BIGINT, true] = [22: k9, BIGINT, true]\n  |  build runtime filters:\n  |  - filter_id = 3, build_expr = (25: k12), remote = false\n  |  - filter_id = 4, build_expr = (22: k9), remote = false\n  |  output columns: 3, 6, 19, 59\n  |  can local shuffle: true\n  |  cardinality: 25600\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n  |  * k6-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k9-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k12-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k4-->[-Infinity, Infinity, 0.0, 6.25, 8.0] ESTIMATE\n  |  * k6-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k9-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k12-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * concat-->[-Infinity, Infinity, 0.0, 15.25, 13.0] ESTIMATE\n  |  * expr-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  |----25:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 25600\n  |    \n  22:Project\n  |  output columns:\n  |  3 <-> [3: k3, VARCHAR, true]\n  |  6 <-> [6: k6, BIGINT, true]\n  |  9 <-> [9: k9, BIGINT, true]\n  |  12 <-> [12: k12, DOUBLE, true]\n  |  cardinality: 8192\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n  |  * k6-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k9-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k12-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  21:NESTLOOP JOIN\n  |  join op: INNER JOIN\n  |  other join predicates: 3: k3 LIKE 29: k3\n  |  build runtime filters:\n  |  - filter_id = 2, build_expr = (29: k3), remote = false\n  |  can local shuffle: false\n  |  cardinality: 8192\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n  |  * k6-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k9-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k12-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 3.0] ESTIMATE\n  |  \n  |----20:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1\n  |    \n  1:Project\n  |  output columns:\n  |  3 <-> [3: k3, CHAR, true]\n  |  6 <-> [6: k6, BIGINT, true]\n  |  9 <-> [9: k9, BIGINT, true]\n  |  12 <-> [12: k12, DOUBLE, true]\n  |  cardinality: 32768\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n  |  * k6-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k9-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k12-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  0:OlapScanNode\n     table: aggregate_tbl, rollup: aggregate_tbl\n     preAggregation: off. Reason: Has can not pre-aggregation Join\n     Predicates: [13: k13, DECIMAL128(38,9), true] >= 0\n     partitionsRatio=1/1, tabletsRatio=3/3\n     tabletList=10198,10200,10202\n     actualRows=65536, avgRowSize=12.0\n     cardinality: 32768\n     probe runtime filters:\n     - filter_id = 2, probe_expr = (3: k3)\n     - filter_id = 3, probe_expr = (12: k12)\n     - filter_id = 4, probe_expr = (9: k9)\n     column statistics: \n     * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n     * k6-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * k9-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * k12-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * k13-->[0.0, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n\nPLAN FRAGMENT 3(F09)\n\n  Input Partition: RANDOM\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 25\n\n  24:Project\n  |  output columns:\n  |  19 <-> [19: k6, TINYINT, true]\n  |  22 <-> [22: k9, BIGINT, true]\n  |  25 <-> [25: k12, DOUBLE, true]\n  |  59 <-> [59: k4, INT, true]\n  |  cardinality: 25600\n  |  column statistics: \n  |  * k6-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k9-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  * k12-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n  |  \n  23:OlapScanNode\n     table: unique_par_tbl, rollup: unique_par_tbl\n     preAggregation: off. Reason: Has can not pre-aggregation Join\n     Predicates: 18: k5 IS NOT NULL\n     dict_col=k4\n     partitionsRatio=1/3, tabletsRatio=3/3\n     tabletList=10166,10168,10170\n     actualRows=25600, avgRowSize=10.25\n     cardinality: 25600\n     column statistics: \n     * k4-->[-Infinity, Infinity, 0.0, 6.25, 8.0] ESTIMATE\n     * k5-->[0.0, 1.0, 0.0, 1.0, 2.0] ESTIMATE\n     * k6-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * k9-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n     * k12-->[-Infinity, Infinity, 0.0, 1.0, 1.0] UNKNOWN\n\nPLAN FRAGMENT 4(F07)\n\n  Input Partition: UNPARTITIONED\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 20\n\n  19:ASSERT NUMBER OF ROWS\n  |  assert number of rows: LE 1\n  |  cardinality: 1\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 3.0] ESTIMATE\n  |  \n  18:EXCHANGE\n     distribution type: GATHER\n     limit: 1\n     cardinality: 1\n\nPLAN FRAGMENT 5(F02)\n\n  Input Partition: HASH_PARTITIONED: 29: k3\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 18\n\n  17:TOP-N\n  |  order by: [29, VARCHAR, true] ASC\n  |  offset: 0\n  |  limit: 1\n  |  cardinality: 1\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 3.0] ESTIMATE\n  |  \n  16:Project\n  |  output columns:\n  |  29 <-> [29: k3, VARCHAR, true]\n  |  cardinality: 3\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 3.0] ESTIMATE\n  |  \n  15:NESTLOOP JOIN\n  |  join op: INNER JOIN\n  |  other join predicates: [29: k3, VARCHAR, true] != [42: k3, VARCHAR, true]\n  |  can local shuffle: false\n  |  cardinality: 3\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 3.0] ESTIMATE\n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 3.0] ESTIMATE\n  |  \n  |----14:EXCHANGE\n  |       distribution type: BROADCAST\n  |       cardinality: 1\n  |    \n  5:AGGREGATE (merge finalize)\n  |  group by: [29: k3, VARCHAR, true]\n  |  cardinality: 4\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n  |  \n  4:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [29: k3, VARCHAR, true]\n     cardinality: 4\n\nPLAN FRAGMENT 6(F05)\n\n  Input Partition: UNPARTITIONED\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 14\n\n  13:ASSERT NUMBER OF ROWS\n  |  assert number of rows: LE 1\n  |  cardinality: 1\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n  |  \n  12:EXCHANGE\n     distribution type: GATHER\n     limit: 1\n     cardinality: 1\n\nPLAN FRAGMENT 7(F04)\n\n  Input Partition: HASH_PARTITIONED: 42: k3\n  OutPut Partition: UNPARTITIONED\n  OutPut Exchange Id: 12\n\n  11:TOP-N\n  |  order by: [42, VARCHAR, true] ASC\n  |  offset: 0\n  |  limit: 1\n  |  cardinality: 1\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n  |  \n  10:AGGREGATE (merge finalize)\n  |  group by: [42: k3, VARCHAR, true]\n  |  cardinality: 4\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n  |  \n  9:EXCHANGE\n     distribution type: SHUFFLE\n     partition exprs: [42: k3, VARCHAR, true]\n     cardinality: 4\n\nPLAN FRAGMENT 8(F03)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 42: k3\n  OutPut Exchange Id: 09\n\n  8:AGGREGATE (update serialize)\n  |  STREAMING\n  |  group by: [42: k3, VARCHAR, true]\n  |  cardinality: 4\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n  |  \n  7:Project\n  |  output columns:\n  |  42 <-> [42: k3, CHAR, true]\n  |  cardinality: 5120\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n  |  \n  6:OlapScanNode\n     table: aggregate_par_tbl, rollup: aggregate_par_tbl\n     preAggregation: on\n     Predicates: DictDecode(58: k4, [<place-holder> NOT IN ('futian', 'baiyun', 'luohu')])\n     dict_col=k4\n     partitionsRatio=1/3, tabletsRatio=3/3\n     tabletList=10177,10179,10181\n     actualRows=8192, avgRowSize=14.25\n     cardinality: 5120\n     column statistics: \n     * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n     * k4-->[-Infinity, Infinity, 0.0, 6.25, 8.0] ESTIMATE\n\nPLAN FRAGMENT 9(F01)\n\n  Input Partition: RANDOM\n  OutPut Partition: HASH_PARTITIONED: 29: k3\n  OutPut Exchange Id: 04\n\n  3:AGGREGATE (update serialize)\n  |  STREAMING\n  |  group by: [29: k3, CHAR, true]\n  |  cardinality: 4\n  |  column statistics: \n  |  * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n  |  \n  2:OlapScanNode\n     table: duplicate_tbl, rollup: duplicate_tbl\n     preAggregation: on\n     partitionsRatio=1/1, tabletsRatio=3/3\n     tabletList=10207,10209,10211\n     actualRows=65536, avgRowSize=8.0\n     cardinality: 65536\n     column statistics: \n     * k3-->[-Infinity, Infinity, 0.0, 8.0, 4.0] ESTIMATE\n", "session_variables": "{\"partial_update_mode\":\"auto\",\"cbo_cte_reuse\":true,\"character_set_connection\":\"utf8\",\"cbo_use_correlated_join_estimate\":true,\"enable_insert_strict\":true,\"enable_connector_adaptive_io_tasks\":true,\"tx_isolation\":\"REPEATABLE-READ\",\"enable_hive_metadata_cache_with_insert\":false,\"hive_temp_staging_dir\":\"/tmp/starrocks\",\"cbo_cte_reuse_rate_v2\":1.15,\"spill_rand_ratio\":0.1,\"enable_datacache_io_adaptor\":true,\"character_set_results\":\"utf8\",\"materialized_view_union_rewrite_mode\":0,\"enable_count_star_optimization\":true,\"enable_iceberg_column_statistics\":false,\"thrift_plan_protocol\":\"binary\",\"enable_plan_serialize_concurrently\":true,\"materialized_view_max_relation_mapping_size\":10,\"global_runtime_filter_build_min_size\":131072,\"enable_iceberg_identity_column_optimize\":true,\"enable_view_based_mv_rewrite\":true,\"query_excluding_mv_names\":\"\",\"enable_rewrite_simple_agg_to_meta_scan\":false,\"enable_ukfk_opt\":false,\"enable_adaptive_sink_dop\":true,\"enable_ukfk_join_reorder\":false,\"consistent_hash_virtual_number\":256,\"warehouse\":\"default_warehouse\",\"enable_profile\":false,\"load_mem_limit\":0,\"spill_storage_volume\":\"\",\"cbo_eq_base_type\":\"decimal\",\"enable_materialized_view_for_insert\":false,\"large_decimal_underlying_type\":\"panic\",\"sql_safe_updates\":0,\"runtime_filter_early_return_selectivity\":0.05,\"enable_local_shuffle_agg\":true,\"disable_function_fold_constants\":false,\"enable_predicate_move_around\":true,\"enable_query_queue\":true,\"enable_cbo_view_based_mv_rewrite\":false,\"select_ratio_threshold\":0.15,\"enable_connector_sink_global_shuffle\":true,\"query_delivery_timeout\":300,\"collation_database\":\"utf8_general_ci\",\"spill_mem_table_size\":104857600,\"enable_gin_filter\":true,\"follower_query_forward_mode\":\"\",\"orc_use_column_names\":false,\"spill_enable_compaction\":true,\"cbo_use_lock_db\":false,\"new_planner_agg_stage\":0,\"enable_strict_order_by\":true,\"hash_join_interpolate_passthrough\":false,\"use_compute_nodes\":-1,\"enable_metadata_profile\":false,\"collation_connection\":\"utf8_general_ci\",\"enable_rewrite_bitmap_union_to_bitamp_agg\":true,\"enable_force_rule_based_mv_rewrite\":true,\"enable_array_distinct_after_agg_opt\":true,\"resource_group\":\"\",\"group_execution_max_groups\":128,\"scan_hive_partition_num_limit\":0,\"enable_materialized_view_plan_cache\":true,\"spill_operator_max_bytes\":1048576000,\"enable_materialized_view_agg_pushdown_rewrite\":false,\"cbo_max_reorder_node_use_dp\":10,\"cbo_prepare_metadata_thread_pool_size\":16,\"enable_result_sink_accumulate\":true,\"enable_hive_column_stats\":true,\"enable_async_profile\":true,\"enable_groupby_use_output_alias\":false,\"global_runtime_filter_wait_timeout\":20,\"forward_to_leader\":false,\"enable_prune_column_after_index_filter\":true,\"count_distinct_column_buckets\":1024,\"cross_join_cost_penalty\":1000000,\"query_cache_agg_cardinality_limit\":5000000,\"cboPushDownAggregateMode_v1\":-1,\"window_partition_mode\":1,\"enable_tablet_internal_parallel_v2\":true,\"interpolate_passthrough\":true,\"enable_incremental_mv\":false,\"cbo_push_down_topn_limit\":1000,\"SQL_AUTO_IS_NULL\":false,\"event_scheduler\":\"OFF\",\"max_pipeline_dop\":64,\"broadcast_right_table_scale_factor\":10,\"materialized_view_rewrite_mode\":\"DEFAULT\",\"enable_read_iceberg_puffin_ndv\":true,\"enable_write_hive_external_table\":false,\"enable_simplify_case_when\":true,\"enable_aggregation_pipeline_share_limit\":true,\"runtime_join_filter_push_down_limit\":1024000,\"trace_log_mode\":\"command\",\"enable_scan_datacache\":true,\"big_query_log_cpu_second_threshold\":480,\"div_precision_increment\":4,\"runtime_adaptive_dop_max_block_rows_per_driver_seq\":16384,\"log_rejected_record_num\":0,\"enable_short_circuit\":false,\"cbo_push_down_distinct_below_window\":true,\"sql_mode_v2\":34,\"prefer_cte_rewrite\":false,\"optimizer_materialized_view_timelimit\":1000,\"hdfs_backend_selector_scan_range_shuffle\":false,\"enable_cross_join\":true,\"pipeline_profile_level\":1,\"parallel_fragment_exec_instance_num\":1,\"max_scan_key_num\":-1,\"net_read_timeout\":60,\"streaming_preaggregation_mode\":\"auto\",\"hive_partition_stats_sample_size\":3000,\"enable_mv_planner\":false,\"enable_collect_table_level_scan_stats\":true,\"enable_fine_grained_range_predicate\":false,\"enable_populate_datacache\":true,\"enable_connector_sink_writer_scaling\":true,\"query_debug_options\":\"\",\"profile_timeout\":2,\"cbo_push_down_aggregate\":\"global\",\"spill_encode_level\":7,\"enable_query_dump\":false,\"max_spill_read_buffer_bytes_per_driver\":16777216,\"global_runtime_filter_build_max_size\":67108864,\"enable_pushdown_or_predicate\":true,\"enable_rewrite_sum_by_associative_rule\":true,\"query_cache_hot_partition_num\":3,\"enable_prune_complex_types\":true,\"metadata_collect_query_timeout\":60,\"query_cache_type\":0,\"max_parallel_scan_instance_num\":-1,\"enable_dynamic_prune_scan_range\":true,\"query_cache_entry_max_rows\":409600,\"connector_io_tasks_per_scan_operator\":16,\"disable_spill_to_local_disk\":false,\"enable_materialized_view_union_rewrite\":true,\"sql_quote_show_create\":true,\"enable_constant_execute_in_fe\":true,\"scan_or_to_union_threshold\":50000000,\"enable_materialized_view_rewrite_partition_compensate\":true,\"enable_exchange_pass_through\":true,\"runtime_profile_report_interval\":10,\"query_cache_entry_max_bytes\":4194304,\"enable_partition_column_value_only_optimization\":true,\"connector_max_split_size\":67108864,\"array_low_cardinality_optimize\":true,\"interleaving_group_size\":10,\"enable_exchange_perf\":false,\"enable_prepare_stmt\":true,\"workgroup_id\":0,\"enable_pipeline_level_multi_partitioned_rf\":false,\"enable_rewrite_groupingsets_to_union_all\":false,\"spill_enable_direct_io\":false,\"transmission_compression_type\":\"NO_COMPRESSION\",\"interactive_timeout\":3600,\"use_page_cache\":true,\"big_query_log_scan_bytes_threshold\":10737418240,\"enable_execution_only\":false,\"collation_server\":\"utf8_general_ci\",\"cbo_decimal_cast_string_strict\":true,\"enable_hyperscan_vec\":true,\"enable_datacache_async_populate_mode\":true,\"enable_nested_loop_join\":true,\"cbo_enable_predicate_subfield_path\":true,\"tablet_internal_parallel_mode\":\"auto\",\"enable_pipeline\":true,\"spill_mode\":\"auto\",\"allow_hive_without_partition_filter\":true,\"enable_query_debug_trace\":false,\"cbo_materialized_view_rewrite_related_mvs_limit\":64,\"enable_lake_tablet_internal_parallel\":false,\"enable_show_all_variables\":false,\"full_sort_max_buffered_bytes\":16777216,\"catalog\":\"default_catalog\",\"wait_timeout\":28800,\"max_buckets_per_be_to_use_balancer_assignment\":6,\"enable_query_tablet_affinity\":false,\"transmission_encode_level\":7,\"query_including_mv_names\":\"\",\"transaction_isolation\":\"REPEATABLE-READ\",\"enable_global_runtime_filter\":true,\"enable_load_profile\":false,\"enable_rewrite_simple_agg_to_hdfs_scan\":false,\"enable_plan_validation\":true,\"load_transmission_compression_type\":\"NO_COMPRESSION\",\"global_runtime_filter_rpc_http_min_size\":67108864,\"cbo_materialized_view_rewrite_rule_output_limit\":3,\"cbo_enable_low_cardinality_optimize\":true,\"scan_use_query_mem_ratio\":0.3,\"connector_huge_file_size\":536870912,\"new_planner_optimize_timeout\":3000,\"enable_outer_join_reorder\":true,\"force_schedule_local\":false,\"hudi_mor_force_jni_reader\":false,\"enable_agg_spill_preaggregation\":true,\"cbo_enable_greedy_join_reorder\":true,\"range_pruner_max_predicate\":100,\"enable_rbo_table_prune\":false,\"spillable_operator_mask\":-1,\"enable_wait_dependent_event\":false,\"rpc_http_min_size\":2147482624,\"low_cardinality_optimize_v2\":true,\"enable_file_metacache\":true,\"cbo_debug_alive_backend_number\":0,\"global_runtime_filter_probe_min_size\":102400,\"scan_or_to_union_limit\":4,\"enable_cbo_table_prune\":false,\"enable_parallel_merge\":true,\"cbo_materialized_view_rewrite_candidate_limit\":12,\"skew_join_use_mcv_count\":5,\"cbo_derive_join_is_null_predicate\":true,\"nested_mv_rewrite_max_level\":3,\"enable_materialized_view_text_match_rewrite\":true,\"big_query_profile_threshold\":\"0s\",\"net_write_timeout\":60,\"cbo_prune_shuffle_column_rate\":0.1,\"spill_revocable_max_bytes\":0,\"hash_join_push_down_right_table\":true,\"connector_sink_target_max_file_size\":1073741824,\"max_ukfk_join_reorder_scale_ratio\":100,\"pipeline_sink_dop\":0,\"broadcast_row_limit\":15000000,\"enable_prune_iceberg_manifest\":true,\"exec_mem_limit\":2147483648,\"enable_sort_aggregate\":false,\"query_cache_force_populate\":false,\"computation_fragment_scheduling_policy\":\"COMPUTE_NODES_ONLY\",\"runtime_filter_on_exchange_node\":false,\"disable_join_reorder\":false,\"global_runtime_filter_rpc_timeout\":400,\"connector_scan_use_query_mem_ratio\":0.3,\"net_buffer_length\":16384,\"cbo_prune_subfield\":true,\"full_sort_max_buffered_rows\":1024000,\"query_timeout\":300,\"connector_io_tasks_slow_io_latency_ms\":50,\"cbo_max_reorder_node\":50,\"enable_distinct_column_bucketization\":false,\"enable_big_query_log\":true,\"analyze_mv\":\"sample\",\"enable_spill_buffer_read\":true,\"group_execution_min_scan_rows\":5000000,\"runtime_filter_scan_wait_time\":20,\"enable_sync_materialized_view_rewrite\":true,\"prefer_compute_node\":true,\"enable_strict_type\":false,\"enable_table_prune_on_update\":false,\"group_concat_max_len\":1024,\"enable_stats_to_optimize_skew_join\":true,\"parse_tokens_limit\":3500000,\"chunk_size\":4096,\"global_runtime_filter_probe_min_selectivity\":0.5,\"query_mem_limit\":0,\"enable_filter_unused_columns_in_scan_stage\":true,\"enable_materialized_view_single_table_view_delta_rewrite\":false,\"cbo_prune_json_subfield\":true,\"enable_spill_to_remote_storage\":false,\"enable_materialized_view_transparent_union_rewrite\":true,\"enable_prune_complex_types_in_unnest\":true,\"auto_increment_increment\":1,\"sql_dialect\":\"StarRocks\",\"enable_per_bucket_optimize\":true,\"enable_group_execution\":true,\"big_query_log_scan_rows_threshold\":1000000000,\"character_set_client\":\"utf8\",\"autocommit\":true,\"enable_column_expr_predicate\":true,\"enable_partition_bucket_optimize\":false,\"max_ukfk_join_reorder_fk_rows\":100000000,\"enable_show_predicate_tree_in_profile\":false,\"enable_subfield_no_copy\":true,\"jit_level\":1,\"enable_runtime_adaptive_dop\":false,\"cbo_cte_max_limit\":10,\"connector_sink_compression_codec\":\"uncompressed\",\"storage_engine\":\"olap\",\"spill_operator_min_bytes\":52428800,\"cbo_enable_dp_join_reorder\":true,\"tx_visible_wait_timeout\":10,\"materialized_view_join_same_table_permutation_limit\":5,\"enable_materialized_view_view_delta_rewrite\":true,\"cbo_max_reorder_node_use_exhaustive\":4,\"enable_sql_digest\":false,\"spill_mem_table_num\":2,\"enable_spill\":false,\"enable_materialized_view_rewrite_greedy_mode\":false,\"pipeline_dop\":0,\"single_node_exec_plan\":false,\"full_sort_late_materialization_v2\":true,\"join_implementation_mode_v2\":\"auto\",\"enable_connector_split_io_tasks\":true,\"sql_select_limit\":9223372036854775807,\"enable_materialized_view_rewrite\":true,\"materialized_view_subuqery_text_match_max_count\":4,\"statistic_collect_parallel\":1,\"hdfs_backend_selector_hash_algorithm\":\"consistent\",\"enable_expr_prune_partition\":true,\"plan_mode\":\"auto\",\"enable_topn_runtime_filter\":true,\"disable_colocate_join\":false,\"max_pushdown_conditions_per_column\":-1,\"default_table_compression\":\"lz4_frame\",\"runtime_adaptive_dop_max_output_amplification_factor\":0,\"skew_join_rand_range\":1000,\"choose_execute_instances_mode\":\"LOCALITY\",\"skew_join_data_skew_threshold\":0.2,\"innodb_read_only\":true,\"spill_mem_limit_threshold\":0.8,\"cbo_reorder_threshold_use_exhaustive\":6,\"enable_predicate_reorder\":false,\"enable_query_cache\":false,\"group_execution_group_scale\":64,\"transaction_read_only\":\"OFF\",\"max_allowed_packet\":33554432,\"enable_parallel_prepare_metadata\":false,\"time_zone\":\"Asia/Shanghai\",\"enable_multicolumn_global_runtime_filter\":false,\"character_set_server\":\"utf8\",\"cbo_use_nth_exec_plan\":0,\"io_tasks_per_scan_operator\":4,\"parallel_exchange_instance_num\":-1,\"enable_shared_scan\":false,\"join_late_materialization\":false,\"audit_execute_stmt\":false,\"cbo_derive_range_join_predicate\":false,\"allow_default_partition\":false,\"paimon_force_jni_reader\":false,\"enable_pipeline_level_shuffle\":true}", "be_number": 1, "be_core_stat": {"numOfHardwareCoresPerBe": "{\"10002\":4}", "cachedAvgNumOfHardwareCores": 4}, "exception": [], "version": "main", "commit_version": "5a8de81"}