<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.starrocks</groupId>
    <artifactId>starrocks-fe</artifactId>
    <version>3.4.0</version>
    <packaging>pom</packaging>

    <modules>
        <module>plugin-common</module>
        <module>fe-common</module>
        <module>spark-dpp</module>
        <module>fe-core</module>
        <module>hive-udf</module>
    </modules>

    <name>starrocks-fe</name>

    <properties>
        <starrocks.home>${basedir}/../</starrocks.home>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <jprotobuf.version>2.4.23</jprotobuf.version>
        <log4j.version>2.19.0</log4j.version>
        <jackson.version>2.15.2</jackson.version>
        <spark.version>3.4.3</spark.version>
        <tomcat.version>8.5.70</tomcat.version>
        <parquet.version>1.12.3</parquet.version>
        <hadoop.version>3.4.0</hadoop.version>
        <gcs.connector.version>hadoop3-2.2.11</gcs.connector.version>
        <skip.plugin>false</skip.plugin>
        <hudi.version>0.14.1</hudi.version>
        <hive-apache.version>3.1.2-13</hive-apache.version>
        <dlf-metastore-client.version>0.2.14</dlf-metastore-client.version>
        <sonar.organization>starrocks</sonar.organization>
        <sonar.host.url>https://sonarcloud.io</sonar.host.url>
        <odps.version>0.48.7-public</odps.version>
        <hikaricp.version>3.4.5</hikaricp.version>
        <kafka-clients.version>3.4.0</kafka-clients.version>
    </properties>

    <profiles>
        <profile>
            <id>spark2</id>
            <properties>
                <spark.version>2.4.6</spark.version>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>spark3</id>
            <properties>
                <spark.version>3.3.1</spark.version>
            </properties>
        </profile>
        <!-- for custom internal repository -->
        <profile>
            <id>custom-env</id>
            <activation>
                <property>
                    <name>env.CUSTOM_MAVEN_REPO</name>
                </property>
            </activation>

            <repositories>
                <repository>
                    <id>custom-nexus</id>
                    <url>${env.CUSTOM_MAVEN_REPO}</url>
                </repository>
            </repositories>

            <pluginRepositories>
                <pluginRepository>
                    <id>custom-nexus</id>
                    <url>${env.CUSTOM_MAVEN_REPO}</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>

        <!-- for general repository -->
        <profile>
            <id>general-env</id>
            <activation>
                <property>
                    <name>!env.CUSTOM_MAVEN_REPO</name>
                </property>
            </activation>

            <repositories>
                <!-- for leveldbjni-all-1.8.jar,commons-crypto-1.0.0.jar on aarch64-->
                <repository>
                    <id>kunpeng</id>
                    <url>https://mirror.iscas.ac.cn/kunpeng/maven/</url>
                </repository>

                <repository>
                    <id>central</id>
                    <name>central maven repo https</name>
                    <url>https://repo.maven.apache.org/maven2</url>
                </repository>

                <!-- for java-cup -->
                <repository>
                    <id>cloudera-public</id>
                    <url>https://repository.cloudera.com/artifactory/public/</url>
                </repository>
                <repository>
                    <id>cloudera</id>
                    <url>https://repository.cloudera.com/artifactory/cloudera-repos/</url>
                </repository>
                <!-- for bdb je -->
                <repository>
                    <id>oracleReleases</id>
                    <url>https://download.oracle.com/maven</url>
                </repository>
            </repositories>

            <pluginRepositories>
                <pluginRepository>
                    <id>spring-plugins</id>
                    <url>https://repo.spring.io/plugins-release/</url>
                </pluginRepository>
                <!-- for cup-maven-plugin -->
                <pluginRepository>
                    <id>cloudera-public</id>
                    <url>https://repository.cloudera.com/artifactory/public/</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.starrocks</groupId>
                <artifactId>plugin-common</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.starrocks</groupId>
                <artifactId>hive-udf</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.starrocks</groupId>
                <artifactId>fe-common</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.starrocks</groupId>
                <artifactId>spark-dpp</artifactId>
                <version>1.0.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-cli/commons-cli -->
            <dependency>
                <groupId>commons-cli</groupId>
                <artifactId>commons-cli</artifactId>
                <version>1.4</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-codec/commons-codec -->
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.13</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-lang/commons-lang -->
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.4</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.9</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-pool2 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.3</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-net/commons-net -->
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.9.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-validator/commons-validator -->
            <dependency>
                <groupId>commons-validator</groupId>
                <artifactId>commons-validator</artifactId>
                <version>1.7</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.9</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.0.1-jre</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-core -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-annotations -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.dataformat/jackson-dataformat-yaml -->
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.module/jackson-module-jaxb-annotations -->
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-jaxb-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/net.sourceforge.czt.dev/java-cup -->
            <dependency>
                <groupId>net.sourceforge.czt.dev</groupId>
                <artifactId>java-cup</artifactId>
                <version>0.11-a-czt02-cdh</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.starrocks/starrocks-bdb-je -->
            <dependency>
                <groupId>com.starrocks</groupId>
                <artifactId>starrocks-bdb-je</artifactId>
                <version>18.3.16</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/de.jflex/jflex -->
            <dependency>
                <groupId>de.jflex</groupId>
                <artifactId>jflex</artifactId>
                <version>1.4.3</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.jmockit/jmockit -->
            <!-- https://mvnrepository.com/artifact/com.github.hazendaz.jmockit/jmockit -->
            <dependency>
                <groupId>com.github.hazendaz.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>1.49.4</version>
                <scope>test</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.baidu/jprotobuf -->
            <dependency>
                <groupId>com.baidu</groupId>
                <artifactId>jprotobuf</artifactId>
                <version>${jprotobuf.version}</version>
                <classifier>jar-with-dependencies</classifier>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.baidu/jprotobuf-rpc-common -->
            <dependency>
                <groupId>com.baidu</groupId>
                <artifactId>jprotobuf-rpc-common</artifactId>
                <version>1.9</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.7</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.baidu/jprotobuf-rpc-core -->
            <dependency>
                <groupId>com.baidu</groupId>
                <artifactId>jprotobuf-rpc-core</artifactId>
                <version>4.2.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.baidu</groupId>
                        <artifactId>jprotobuf</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.json/json -->
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>20231013</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/junit/junit -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.1</version>
                <scope>test</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.thrift/libthrift -->
            <dependency>
                <groupId>org.apache.thrift</groupId>
                <artifactId>libthrift</artifactId>
                <version>0.13.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-api -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-core -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-slf4j-impl -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-layout-template-json</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-1.2-api -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-1.2-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/io.netty/netty-all -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.61.Final</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.google.protobuf/protobuf-java -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.16.3</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.10.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.squareup.okio/okio -->
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>3.4.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/javax.validation/validation-api -->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>1.1.0.Final</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.slf4j/slf4j-api -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.30</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.github.oshi/oshi-core -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>6.2.1</version>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka-clients.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.jboss.xnio/xnio-nio -->
            <dependency>
                <groupId>org.jboss.xnio</groupId>
                <artifactId>xnio-nio</artifactId>
                <version>3.8.10.Final</version>
            </dependency>

            <!-- support jdk9 -->
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>1.3.2</version>
            </dependency>

            <!-- support jdk9 -->
            <dependency>
                <groupId>com.sun.activation</groupId>
                <artifactId>javax.activation</artifactId>
                <version>1.2.0</version>
            </dependency>

            <!-- support jdk11 -->
            <!-- https://mvnrepository.com/artifact/javax.xml.ws/jaxws-api -->
            <dependency>
                <groupId>javax.xml.ws</groupId>
                <artifactId>jaxws-api</artifactId>
                <version>2.3.0</version>
            </dependency>

            <dependency>
                <groupId>org.roaringbitmap</groupId>
                <artifactId>RoaringBitmap</artifactId>
                <version>0.8.13</version>
            </dependency>

            <!-- spark -->
            <!-- https://mvnrepository.com/artifact/org.apache.spark/spark-core_2.12 -->
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-core_2.12</artifactId>
                <version>${spark.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.zookeeper</groupId>
                        <artifactId>zookeeper</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j2-impl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.spark/spark-launcher_2.12 -->
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-launcher_2.12</artifactId>
                <version>${spark.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.spark/spark-sql_2.12 -->
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-sql_2.12</artifactId>
                <version>${spark.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.spark/spark-catalyst -->
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-catalyst_2.12</artifactId>
                <version>${spark.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/io.trino.hive/hive-apache -->
            <dependency>
                <groupId>io.trino.hive</groupId>
                <artifactId>hive-apache</artifactId>
                <version>${hive-apache.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.aliyun.datalake/metastore-client-hive3 -->
            <dependency>
                <groupId>com.aliyun.datalake</groupId>
                <artifactId>metastore-client-hive3</artifactId>
                <version>${dlf-metastore-client.version}</version>
            </dependency>


            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-common -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-common</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>zookeeper</artifactId>
                        <groupId>org.apache.zookeeper</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-reload4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.parquet/parquet-column -->
            <dependency>
                <groupId>org.apache.parquet</groupId>
                <artifactId>parquet-column</artifactId>
                <version>${parquet.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.parquet/parquet-hadoop -->
            <dependency>
                <groupId>org.apache.parquet</groupId>
                <artifactId>parquet-hadoop</artifactId>
                <version>${parquet.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.parquet/parquet-common -->
            <dependency>
                <groupId>org.apache.parquet</groupId>
                <artifactId>parquet-common</artifactId>
                <version>${parquet.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-collections/commons-collections -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>

            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>2.12.10</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.scala-lang/scala-library -->
            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo-shaded</artifactId>
                <version>4.0.2</version>
            </dependency>


            <!-- https://mvnrepository.com/artifact/com.github.ben-manes.caffeine/caffeine -->
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>2.9.3</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.opencsv/opencsv -->
            <dependency>
                <groupId>com.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>5.7.1</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-client -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-client</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-reload4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-client-api</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-client-runtime</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-aws -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-aws</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-azure-datalake -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-azure-datalake</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-azure -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-azure</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.google.cloud.bigdataoss/gcs-connector -->
            <dependency>
                <groupId>com.google.cloud.bigdataoss</groupId>
                <artifactId>gcs-connector</artifactId>
                <version>${gcs.connector.version}</version>
                <classifier>shaded</classifier>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.fasterxml.uuid/java-uuid-generator -->
            <dependency>
                <groupId>com.fasterxml.uuid</groupId>
                <artifactId>java-uuid-generator</artifactId>
                <version>4.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hudi/hudi-common -->
            <dependency>
                <groupId>org.apache.hudi</groupId>
                <artifactId>hudi-common</artifactId>
                <version>${hudi.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.zookeeper</groupId>
                        <artifactId>zookeeper</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hudi/hudi-hadoop-mr -->
            <dependency>
                <groupId>org.apache.hudi</groupId>
                <artifactId>hudi-hadoop-mr</artifactId>
                <version>${hudi.version}</version>
            </dependency>

            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-bom</artifactId>
                <version>1.14.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.mysql/mysql-connector-j -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>8.0.33</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.postgresql/postgresql -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.4.4</version>
            </dependency>

			<!-- https://mvnrepository.com/artifact/com.oracle.database.jdbc/ojdbc8 -->
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>19.18.0.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.oracle.database.nls/orai18n -->
            <dependency>
                <groupId>com.oracle.database.nls</groupId>
                <artifactId>orai18n</artifactId>
                <version>19.18.0.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.microsoft.sqlserver/mssql-jdbc/12.4.2.jre8 -->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>12.4.2.jre8</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.mockrunner/mockrunner-jdbc -->
            <dependency>
                <groupId>com.mockrunner</groupId>
                <artifactId>mockrunner-jdbc</artifactId>
                <version>1.0.1</version>
                <scope>test</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-dbcp2 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-dbcp2</artifactId>
                <version>2.9.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.groovy/groovy-groovysh -->
            <dependency>
                <groupId>org.apache.groovy</groupId>
                <artifactId>groovy-groovysh</artifactId>
                <version>4.0.9</version>
            </dependency>

            <dependency>
                <groupId>io.airlift</groupId>
                <artifactId>concurrent</artifactId>
                <version>202</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.odps</groupId>
                <artifactId>odps-sdk-core</artifactId>
                <version>${odps.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.odps</groupId>
                <artifactId>odps-sdk-table-api</artifactId>
                <version>${odps.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <configLocation>checkstyle.xml</configLocation>
                </configuration>
            </plugin>
        </plugins>
    </reporting>
</project>
