<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.starrocks</groupId>
    <artifactId>java-extensions</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>jdbc-bridge</module>
        <module>udf-extensions</module>
        <module>java-utils</module>
        <module>jni-connector</module>
        <module>udf-examples</module>
        <module>hudi-reader</module>
        <module>paimon-reader</module>
        <module>hive-reader</module>
        <module>hadoop-ext</module>
        <module>odps-reader</module>
        <module>common-runtime</module>
    </modules>
    <name>starrocks-java-extensions</name>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
        <hikaricp.version>3.4.5</hikaricp.version>
        <log4j.version>2.19.0</log4j.version>
        <compiler-plugin.version>3.8.1</compiler-plugin.version>
        <java-extensions.home>${basedir}</java-extensions.home>
        <aws-v2-sdk.version>2.23.19</aws-v2-sdk.version>
    </properties>
    <build>
        <plugins>
            <!-- copy config files from fe before checkstyle -->
            <plugin>
                <groupId>com.coderplus.maven.plugins</groupId>
                <artifactId>copy-rename-maven-plugin</artifactId>
                <version>1.0</version>
                <!-- only run in parent -->
                <inherited>false</inherited>
                <executions>
                    <execution>
                        <id>copy-file</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <fileSets>
                                <fileSet>
                                    <sourceFile>../fe/checkstyle.xml</sourceFile>
                                    <destinationFile>./checkstyle.xml</destinationFile>
                                </fileSet>
                                <fileSet>
                                    <sourceFile>../fe/checkstyle-header.txt</sourceFile>
                                    <destinationFile>./checkstyle-header.txt</destinationFile>
                                </fileSet>
                            </fileSets>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.1.1</version>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>8.40</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <configLocation>${java-extensions.home}/checkstyle.xml</configLocation>
                    <encoding>UTF-8</encoding>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>true</failsOnError>
                    <linkXRef>false</linkXRef>
                    <excludes>org/apache/hadoop/**</excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
