# StarRocks FE 优化配置 - 专门解决 RPC 过载和连接问题
# 基于您遇到的 "server is overcrowded" 和 Socket 连接问题优化

#####################################################################
## JVM 配置 - 增加内存和优化 GC
#####################################################################

LOG_DIR = ${STARROCKS_HOME}/log
DATE = "$(date +%Y%m%d-%H%M%S)"

# 增加堆内存到 16GB，优化 GC 参数
JAVA_OPTS="-Dlog4j2.formatMsgNoLookups=true -Xmx16384m -Xms8192m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:${LOG_DIR}/fe.gc.log.$DATE -XX:+PrintConcurrentLocks -Djava.security.policy=${STARROCKS_HOME}/conf/udf_security.policy"

JAVA_OPTS_FOR_JDK_11="-Dlog4j2.formatMsgNoLookups=true -Xmx16384m -Xms8192m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m -Xlog:gc*:${LOG_DIR}/fe.gc.log.$DATE:time -Djava.security.policy=${STARROCKS_HOME}/conf/udf_security.policy"

#####################################################################
## 基础配置
#####################################################################

sys_log_level = INFO
meta_dir = ${STARROCKS_HOME}/meta

# 端口配置
http_port = 8030
rpc_port = 9020
query_port = 9030
edit_log_port = 9010
mysql_service_nio_enabled = true

# 网络配置 - 根据您的环境调整
# priority_networks = 10.10.64.0/24

#####################################################################
## 关键优化: 网络和连接配置
#####################################################################

# HTTP 服务优化
http_backlog_num = 2048                    # 增加 HTTP 连接队列
http_max_initial_line_length = 8192        # 增加初始行长度
http_max_header_size = 65536               # 增加头部大小限制
http_max_chunk_size = 16384                # 增加块大小
http_slow_request_threshold_ms = 10000     # 增加慢请求阈值

# Thrift RPC 优化 - 关键配置
thrift_client_timeout_ms = 30000           # 增加客户端超时到 30 秒
thrift_backlog_num = 2048                  # 增加 Thrift 连接队列
thrift_rpc_timeout_ms = 30000              # 增加 RPC 超时到 30 秒
thrift_rpc_retry_times = 5                 # 增加重试次数
thrift_rpc_strict_mode = false             # 关闭严格模式以提高容错性
thrift_rpc_max_body_size = -1              # 不限制消息体大小

# BRPC 优化 - 解决过载问题的核心配置
brpc_connection_pool_size = 128            # 大幅增加连接池大小
brpc_idle_wait_max_time = 60000            # 增加空闲等待时间到 60 秒

#####################################################################
## 查询引擎优化
#####################################################################

# 连接数优化
qe_max_connection = 8192                   # 增加最大连接数
max_connection_scheduler_threads_num = 8192 # 增加连接调度线程数
max_conn_per_user = 1000                   # 增加每用户最大连接数

# 查询超时优化
qe_query_timeout_second = 600              # 增加查询超时到 10 分钟

# 慢查询日志优化
qe_slow_log_ms = 10000                     # 调整慢查询阈值到 10 秒
enable_qe_slow_log = true

#####################################################################
## 日志配置优化
#####################################################################

# 系统日志优化
log_roll_size_mb = 2048                    # 增加日志文件大小
sys_log_roll_num = 20                      # 增加日志文件数量
sys_log_delete_age = 3d                    # 减少日志保留时间以节省空间

# 审计日志优化
audit_log_roll_num = 30                    # 减少审计日志文件数量
audit_log_delete_age = 7d                  # 减少审计日志保留时间

#####################################################################
## 性能调优配置
#####################################################################

# 元数据配置
edit_log_roll_num = 100000                 # 增加编辑日志滚动数量
edit_log_write_slow_log_threshold_ms = 5000 # 增加慢写入阈值
meta_delay_toleration_second = 30          # 增加元数据延迟容忍度

# 统计信息优化
statistic_collect_interval_sec = 600       # 增加统计收集间隔
statistic_collect_concurrency = 6          # 增加统计收集并发度
statistic_collect_query_timeout = 7200     # 增加统计查询超时

# 存储优化
storage_usage_hard_limit_percent = 90      # 降低存储使用硬限制
storage_usage_hard_limit_reserve_bytes = 214748364800  # 200GB 预留空间

#####################################################################
## 监控和诊断配置
#####################################################################

# 端口连通性检查优化
port_connectivity_check_interval_sec = 30  # 减少检查间隔
port_connectivity_check_retry_times = 5    # 增加重试次数
port_connectivity_check_timeout_ms = 30000 # 增加检查超时

# 慢锁检测
slow_lock_threshold_ms = 5000              # 增加慢锁阈值

#####################################################################
## 高级优化配置
#####################################################################

# 物化视图优化
enable_materialized_view = true
enable_materialized_view_text_based_rewrite = true
skip_whole_phase_lock_mv_limit = 10        # 增加 MV 锁优化限制

# 并发控制优化
max_allowed_in_element_num_of_delete = 50000  # 增加删除语句 IN 元素限制

# 文件大小限制优化
json_file_size_limit = 8589934592          # 增加 JSON 文件大小限制到 8GB
max_varchar_length = 2097152               # 增加 VARCHAR 最大长度

#####################################################################
## 外部系统集成优化
#####################################################################

# Hive Metastore 优化
hive_meta_load_concurrency = 8             # 增加 Hive 元数据加载并发度
hive_meta_cache_refresh_interval_s = 3600  # 减少缓存刷新间隔
hive_meta_cache_ttl_s = 7200               # 减少缓存 TTL
max_hive_partitions_per_rpc = 10000        # 增加每次 RPC 获取的分区数

# JDBC 连接池优化
jdbc_connection_pool_size = 16             # 增加 JDBC 连接池大小
jdbc_minimum_idle_connections = 4          # 增加最小空闲连接数
jdbc_connection_idle_timeout_ms = 300000   # 减少连接空闲超时

#####################################################################
## 备份和恢复优化
#####################################################################

backup_job_default_timeout_ms = 259200000  # 增加备份作业超时到 3 天

#####################################################################
## 特殊优化配置 (针对您的具体问题)
#####################################################################

# 启用实验性功能以提高稳定性
enable_decimal_v3 = true
allow_system_reserved_names = false

# 区域设置
locale = zh_CN.UTF-8

#####################################################################
## 注意事项
#####################################################################

# 1. 应用此配置后需要重启 FE 服务
# 2. 建议同时优化 BE 配置以获得最佳效果
# 3. 监控系统资源使用情况，必要时进一步调整
# 4. 某些参数可能需要根据实际硬件配置调整

#####################################################################
## 动态配置建议 (通过 SQL 设置)
#####################################################################

# 连接 FE 后执行以下 SQL 来设置动态参数:
# SET GLOBAL enable_query_queue_select = true;
# SET GLOBAL enable_query_queue_statistic = true;
# SET GLOBAL enable_query_queue_load = true;
# SET GLOBAL query_queue_concurrency_limit = 100;
# SET GLOBAL query_queue_mem_used_pct_limit = 70;
# SET GLOBAL query_queue_cpu_used_permille_limit = 800;
# SET GLOBAL query_queue_max_queued_queries = 2000;
# SET GLOBAL query_queue_pending_timeout_second = 600;
# SET GLOBAL query_timeout = 600;
