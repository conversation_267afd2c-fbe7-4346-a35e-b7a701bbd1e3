<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com</groupId>
        <artifactId>starrocks-apache-hdfs-broker</artifactId>
        <version>3.4.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>hadoop-cos-shaded</artifactId>
    <version>3.3.0-8.3.7</version>
    <packaging>jar</packaging>

    <properties>
        <starrocks.home>${basedir}/../../../../</starrocks.home>
    </properties>

    <dependencies>
        <!-- https://mvnrepository.com/artifact/com.qcloud.cos/hadoop-cos -->
        <dependency>
            <groupId>com.qcloud.cos</groupId>
            <artifactId>hadoop-cos</artifactId>
            <version>3.3.0-8.3.7</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.qcloud/cos_api-bundle -->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api-bundle</artifactId>
            <version>5.6.137.2</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.qcloud/cosn-ranger-interface -->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cosn-ranger-interface</artifactId>
            <version>1.0.5</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.qcloud/chdfs_hadoop_plugin_network -->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>chdfs_hadoop_plugin_network</artifactId>
            <version>3.5</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.qcloud/hadoop-ranger-client-for-hadoop -->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>hadoop-ranger-client-for-hadoop</artifactId>
            <version>3.3.0-4.1</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.5.2</version>
                <configuration>
                    <relocations>
                         <relocation>
                             <!-- org.apache.hadoop.security.proto.SecurityProtos conflicts in hadoop-ranger-client-for-hadoop and hadoop-common-3.3 -->
                             <pattern>org.apache.hadoop.security.proto</pattern>
                             <shadedPattern>shaded.starrocks.org.apache.hadoop.security.proto</shadedPattern>
                         </relocation>
                    </relocations>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <!--set for ecplise lifecycle -->
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.apache.maven.plugins</groupId>
                                        <artifactId>maven-compiler-plugin</artifactId>
                                        <versionRange>[3.1,)</versionRange>
                                        <goals>
                                            <goal>compile</goal>
                                            <goal>testCompile</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore></ignore>
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
