<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com</groupId>
    <artifactId>starrocks-apache-hdfs-broker</artifactId>
    <version>3.4.0</version>
    <packaging>pom</packaging>

    <modules>
        <module>hadoop-cos-shaded</module>
        <module>broker-core</module>
    </modules>

    <name>apache_hdfs_broker</name>

    <properties>
        <starrocks.home>${basedir}/../../../</starrocks.home>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <hadoop.version>3.4.0</hadoop.version>
        <guava.version>32.0.1-jre</guava.version>
        <zookeeper.version>3.9.2</zookeeper.version>
        <protobuf.version>3.16.3</protobuf.version>
        <thrift.version>0.14.1</thrift.version>
        <tomcat.version>9.0.90</tomcat.version>
        <log4j.version>2.17.1</log4j.version>
        <jackson.version>2.13.4.2</jackson.version>
    </properties>

    <profiles>
        <!-- for custom internal repository -->
        <profile>
            <id>custom-env</id>
            <activation>
                <property>
                    <name>env.CUSTOM_MAVEN_REPO</name>
                </property>
            </activation>

            <repositories>
                <repository>
                    <id>custom-nexus</id>
                    <url>${env.CUSTOM_MAVEN_REPO}</url>
                </repository>
            </repositories>

            <pluginRepositories>
                <pluginRepository>
                    <id>custom-nexus</id>
                    <url>${env.CUSTOM_MAVEN_REPO}</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>

        <!-- for general repository -->
        <profile>
            <id>general-env</id>
            <activation>
                <property>
                    <name>!env.CUSTOM_MAVEN_REPO</name>
                </property>
            </activation>

            <repositories>
                <repository>
                    <id>central</id>
                    <name>central maven repo https</name>
                    <url>https://repo.maven.apache.org/maven2</url>
                </repository>
                <!-- for java-cup -->
                <repository>
                    <id>cloudera-thirdparty</id>
                    <url>https://repository.cloudera.com/content/repositories/third-party/</url>
                </repository>
                <repository>
                    <id>cloudera</id>
                    <url>https://repository.cloudera.com/artifactory/cloudera-repos/</url>
                </repository>
                <!-- for bdb je -->
                <repository>
                    <id>oracleReleases</id>
                    <url>https://download.oracle.com/maven</url>
                </repository>
            </repositories>

            <pluginRepositories>
                <!-- for cup-maven-plugin -->
                <pluginRepository>
                    <id>spring-plugins</id>
                    <url>http://repo.spring.io/plugins-release/</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <!-- https://mvnrepository.com/artifact/commons-cli/commons-cli -->
            <dependency>
                <groupId>commons-cli</groupId>
                <artifactId>commons-cli</artifactId>
                <version>1.4</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-codec/commons-codec -->
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.9</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-collections/commons-collections -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-configuration/commons-configuration -->
            <dependency>
                <groupId>commons-configuration</groupId>
                <artifactId>commons-configuration</artifactId>
                <version>1.6</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-daemon/commons-daemon -->
            <dependency>
                <groupId>commons-daemon</groupId>
                <artifactId>commons-daemon</artifactId>
                <version>1.0.13</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.7</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-lang/commons-lang -->
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.3.2</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-logging/commons-logging -->
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.1.3</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-annotations -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-annotations</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-auth -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-auth</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>zookeeper</artifactId>
                        <groupId>org.apache.zookeeper</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- https://mvnrepository.com/artifact/log4j/log4j -->
            <dependency>
                <groupId>log4j</groupId>
                <artifactId>log4j</artifactId>
                <version>1.2.17-cloudera6</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.zookeeper/zookeeper -->
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-common -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-common</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-hdfs-client</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-hdfs -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-hdfs</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>com.fasterxml.jackson.core</artifactId>
                        <groupId>jackson-databind</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-mapreduce-client-core -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-mapreduce-client-core</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/junit/junit -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.1</version>
                <scope>test</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.thrift/libthrift -->
            <dependency>
                <groupId>org.apache.thrift</groupId>
                <artifactId>libthrift</artifactId>
                <version>${thrift.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>tomcat-embed-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomcat</groupId>
                        <artifactId>tomcat-annotations-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.tomcat.embed/tomcat-embed-core -->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.tomcat/tomcat-annotations-api -->
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-annotations-api</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-api -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-core -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.google.protobuf/protobuf-java -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.slf4j/slf4j-api -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.30</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.slf4j/slf4j-log4j12 -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>1.7.5</version>
                <scope>test</scope>
            </dependency>

            <!-- support jdk9 -->
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>1.3.2</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-aws -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-aws</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-aliyun -->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-aliyun</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.ksyun.kmr/hadoop-ks3 -->
            <dependency>
                <groupId>com.ksyun.kmr</groupId>
                <artifactId>hadoop-ks3</artifactId>
                <version>3.1.1-1.1.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
